import React from 'react';

// Utility function to convert CSS variables to RGBA with opacity
function hexToRgba(colorVar: string, alpha: number): string {
  // This approach allows us to use CSS variables directly with opacity
  // The actual color will be resolved by the browser at runtime
  return colorVar.replace(')', `, ${alpha})`) // For rgb/rgba values
         .replace('rgb', 'rgba'); // Ensure we're using rgba format
}
import { motion } from 'framer-motion';
import { Cloud, Database, Cpu, Globe, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';
import AuroraBackground from './AuroraBackground';

const Hero = () => {

  const clients = [
    {
      name: "Perplexity AI",
      icon: <Cloud className="w-8 h-8" style={{ color: 'var(--color-icon)' }} />
    },
    {
      name: "LandingAI",
      icon: <Database className="w-8 h-8" style={{ color: 'var(--color-icon)' }} />
    },
    {
      name: "PGA",
      icon: <Cpu className="w-8 h-8" style={{ color: 'var(--color-icon)' }} />
    },
    {
      name: "G<PERSON>loop",
      icon: <Globe className="w-8 h-8" style={{ color: 'var(--color-icon)' }} />
    },
    {
      name: "BlackBox AI",
      icon: <Shield className="w-8 h-8" style={{ color: 'var(--color-icon)' }} />
    }
  ];

  return (
    <div className="relative min-h-screen bg-black text-white pt-16">
      <AuroraBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          <div className="inline-flex items-center bg-white/10 rounded-full px-4 py-2 space-x-2">
            <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded font-mono">NEW</span>
            <span className="text-sm font-mono">TRANSFORMING BUSINESSES WITH TECHNOLOGY →</span>
          </div>

          <h1 className="text-5xl md:text-7xl font-mono font-bold leading-tight tracking-tight">
            EMPOWERING YOUR
            <br />
            <span className="inline-block">
              DIGITAL FUTURE
            </span>
          </h1>

          <p className="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto font-mono">
            We provide a wide range of IT services to help businesses grow and succeed.
            Built for modern development needs.
          </p>

          <div className="flex items-center justify-center space-x-4">
            <button 
              className="px-8 py-3 rounded font-mono transition-colors" 
              style={{ 
                backgroundColor: 'var(--color-button-primary-bg)', 
                color: 'var(--color-button-primary-text)' 
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-hover)'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-bg)'}
            >
              GET STARTED
            </button>
            <Link 
              to="/clients"
              className="px-8 py-3 rounded font-mono transition-colors inline-block"
              style={{ 
                borderWidth: '1px', 
                borderStyle: 'solid',
                borderColor: 'var(--color-button-secondary-border)',
                color: 'var(--color-button-secondary-text)',
                backgroundColor: 'var(--color-button-secondary-bg)'
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-secondary-hover)'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-secondary-bg)'}
            >
              VIEW PORTFOLIO
            </Link>
          </div>

          <div className="pt-16">
            <p className="text-sm font-mono text-gray-500 mb-8">TRUSTED BY</p>
            <div className="flex flex-wrap justify-center items-center gap-12">
              {clients.map((client, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="group relative"
                >
                  <div 
                    className="w-32 h-20 backdrop-blur-sm rounded-lg flex flex-col items-center justify-center gap-2 transition-all duration-300"
                    style={{ 
                      backgroundColor: 'var(--color-card-bg)',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: 'var(--color-card-border)'
                    }}
                    onMouseOver={(e) => e.currentTarget.style.borderColor = 'var(--color-card-hover-border)'}
                    onMouseOut={(e) => e.currentTarget.style.borderColor = 'var(--color-card-border)'}
                  >
                    {client.icon}
                    <span 
                      className="font-mono text-xs transition-colors"
                      style={{ color: 'var(--color-text-secondary)' }}
                      onMouseOver={(e) => e.currentTarget.style.color = 'var(--color-primary)'}
                      onMouseOut={(e) => e.currentTarget.style.color = 'var(--color-text-secondary)'}
                    >
                      {client.name}
                    </span>
                  </div>
                  <div 
                    className="absolute inset-0 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" 
                    style={{ 
                      background: `linear-gradient(to right, ${hexToRgba('var(--color-primary)', 0.1)}, ${hexToRgba('var(--color-secondary)', 0.1)})` 
                    }}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Hero;
