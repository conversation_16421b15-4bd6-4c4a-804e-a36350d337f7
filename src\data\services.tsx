import React from 'react';
import { 
  Code,
  Cloud,
  Shield,
  Headphones,
  Database,
  Terminal,
  BarChart,
  Cog,
  Brain,
  Network,
  ShoppingCart,
  Monitor,
  HardDrive,
  Lightbulb
} from 'lucide-react';

export interface Service {
  icon: React.ReactNode;
  title: string;
  description: string;
  slug: string;
}

export const services: Service[] = [
  {
    icon: <Code className="text-purple-400" size={24} />,
    title: "CMS Development",
    description: "Custom CMS solutions with WordPress and WooCommerce integration",
    slug: "cms-development"
  },
  {
    icon: <Cloud className="text-purple-400" size={24} />,
    title: "Cloud Infrastructure",
    description: "Scalable cloud solutions and infrastructure management",
    slug: "cloud-infrastructure"
  },
  {
    icon: <Shield className="text-purple-400" size={24} />,
    title: "Cybersecurity",
    description: "Comprehensive security solutions and threat protection",
    slug: "cybersecurity"
  },
  {
    icon: <Headphones className="text-purple-400" size={24} />,
    title: "Remote IT Support",
    description: "24/7 technical support and system maintenance",
    slug: "remote-it-support"
  },
  {
    icon: <Code className="text-purple-400" size={24} />,
    title: "Custom Software",
    description: "Tailored software solutions for your business needs",
    slug: "custom-software"
  },
  {
    icon: <Terminal className="text-purple-400" size={24} />,
    title: "DevOps & CI/CD",
    description: "Streamlined deployment and integration processes",
    slug: "devops-cicd"
  },
  {
    icon: <BarChart className="text-purple-400" size={24} />,
    title: "Data Analytics",
    description: "Business intelligence and data visualization",
    slug: "data-analytics"
  },
  {
    icon: <Cog className="text-purple-400" size={24} />,
    title: "Managed IT",
    description: "Complete IT infrastructure management",
    slug: "managed-it"
  },
  {
    icon: <Brain className="text-purple-400" size={24} />,
    title: "Digital Transformation",
    description: "Strategic digital innovation and modernization",
    slug: "digital-transformation"
  },
  {
    icon: <Network className="text-purple-400" size={24} />,
    title: "Network Solutions",
    description: "Network monitoring and infrastructure management",
    slug: "network-solutions"
  },
  {
    icon: <ShoppingCart className="text-purple-400" size={24} />,
    title: "E-commerce",
    description: "Custom online store development and management",
    slug: "ecommerce"
  },
  {
    icon: <Monitor className="text-purple-400" size={24} />,
    title: "VDI Solutions",
    description: "Virtual desktop infrastructure and management",
    slug: "vdi-solutions"
  },
  {
    icon: <HardDrive className="text-purple-400" size={24} />,
    title: "Backup & Recovery",
    description: "Data backup and disaster recovery solutions",
    slug: "backup-recovery"
  },
  {
    icon: <Lightbulb className="text-purple-400" size={24} />,
    title: "IT Consulting",
    description: "Strategic IT planning and consulting services",
    slug: "it-consulting"
  },
  {
    icon: <Database className="text-purple-400" size={24} />,
    title: "Database Management",
    description: "Database administration and optimization",
    slug: "database-management"
  }
];