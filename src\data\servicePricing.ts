import { Service } from './services';

export interface PricingTier {
  name: string;
  price: string;
  features: string[];
  recommended?: boolean;
}

export const servicePricing: Record<string, PricingTier[]> = {
  'code-generation': [
    {
      name: 'Basic',
      price: '$49/mo',
      features: [
        'Up to 1,000 code generations',
        'Basic language support',
        'Standard response time',
        'Community support',
        'Basic API access'
      ]
    },
    {
      name: 'Pro',
      price: '$199/mo',
      recommended: true,
      features: [
        'Unlimited code generations',
        'All programming languages',
        'Priority response time',
        'Dedicated support',
        'Full API access',
        'Custom model fine-tuning'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom deployment options',
        'SLA guarantees',
        'Advanced security features',
        'Custom model training',
        'Integration support',
        'Account management'
      ]
    }
  ],
  'secure-sandbox': [
    {
      name: 'Developer',
      price: '$79/mo',
      features: [
        '100 concurrent sandboxes',
        'Basic security features',
        'Standard isolation',
        'Community support',
        'Basic monitoring'
      ]
    },
    {
      name: 'Team',
      price: '$299/mo',
      recommended: true,
      features: [
        '500 concurrent sandboxes',
        'Advanced security',
        'Custom isolation levels',
        'Priority support',
        'Advanced monitoring',
        'Team management'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Unlimited sandboxes',
        'Custom security policies',
        'Dedicated infrastructure',
        'Custom SLAs',
        'Advanced analytics',
        '24/7 support'
      ]
    }
  ],
  'cloud-computing': [
    {
      name: 'Starter',
      price: '$99/mo',
      features: [
        '4 CPU cores',
        '8GB RAM',
        '100GB storage',
        'Basic monitoring',
        'Community support'
      ]
    },
    {
      name: 'Business',
      price: '$399/mo',
      recommended: true,
      features: [
        '16 CPU cores',
        '32GB RAM',
        '500GB storage',
        'Advanced monitoring',
        'Priority support',
        'Auto-scaling'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom resources',
        'Dedicated hardware',
        'Global deployment',
        'Custom SLAs',
        'Advanced security',
        '24/7 support'
      ]
    }
  ],
  'version-control': [
    {
      name: 'Basic',
      price: '$29/mo',
      features: [
        'Unlimited repositories',
        'Basic branching',
        'Standard CI/CD',
        'Community support',
        'Basic security'
      ]
    },
    {
      name: 'Team',
      price: '$149/mo',
      recommended: true,
      features: [
        'Advanced branching',
        'Custom workflows',
        'Advanced CI/CD',
        'Priority support',
        'Security scanning',
        'Team management'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom workflows',
        'Dedicated support',
        'Advanced security',
        'Compliance features',
        'Custom integrations',
        'Audit logging'
      ]
    }
  ],
  'security': [
    {
      name: 'Essential',
      price: '$199/mo',
      features: [
        'Basic threat detection',
        'Standard monitoring',
        'Security alerts',
        'Community support',
        'Basic compliance'
      ]
    },
    {
      name: 'Advanced',
      price: '$499/mo',
      recommended: true,
      features: [
        'Advanced threat detection',
        '24/7 monitoring',
        'Incident response',
        'Priority support',
        'Compliance management',
        'Security training'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom security policies',
        'Dedicated SOC',
        'Advanced compliance',
        'Custom reporting',
        'Penetration testing',
        'Custom training'
      ]
    }
  ],
  'access-control': [
    {
      name: 'Basic',
      price: '$39/mo',
      features: [
        'Role-based access',
        'Basic policies',
        'Standard logging',
        'Community support',
        'Basic reporting'
      ]
    },
    {
      name: 'Pro',
      price: '$159/mo',
      recommended: true,
      features: [
        'Advanced RBAC',
        'Custom policies',
        'Advanced logging',
        'Priority support',
        'Advanced reporting',
        'SSO integration'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom RBAC',
        'Policy automation',
        'Custom integrations',
        'Advanced analytics',
        'Compliance features',
        'Custom support'
      ]
    }
  ],
  'ai-integration': [
    {
      name: 'Starter',
      price: '$149/mo',
      features: [
        'Basic AI models',
        'Standard APIs',
        'Basic integration',
        'Community support',
        'Usage analytics'
      ]
    },
    {
      name: 'Professional',
      price: '$499/mo',
      recommended: true,
      features: [
        'Advanced AI models',
        'Custom endpoints',
        'Advanced integration',
        'Priority support',
        'Advanced analytics',
        'Model fine-tuning'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom AI models',
        'Dedicated resources',
        'Custom integration',
        'Advanced security',
        'Custom training',
        'Dedicated support'
      ]
    }
  ],
  'real-time-processing': [
    {
      name: 'Basic',
      price: '$129/mo',
      features: [
        '1M events/month',
        'Basic processing',
        'Standard latency',
        'Community support',
        'Basic monitoring'
      ]
    },
    {
      name: 'Advanced',
      price: '$399/mo',
      recommended: true,
      features: [
        '10M events/month',
        'Advanced processing',
        'Low latency',
        'Priority support',
        'Advanced monitoring',
        'Custom rules'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Unlimited events',
        'Custom processing',
        'Ultra-low latency',
        'Dedicated support',
        'Custom analytics',
        'Custom SLAs'
      ]
    }
  ],
  'data-management': [
    {
      name: 'Basic',
      price: '$79/mo',
      features: [
        '100GB storage',
        'Basic backup',
        'Standard access',
        'Community support',
        'Basic security'
      ]
    },
    {
      name: 'Business',
      price: '$299/mo',
      recommended: true,
      features: [
        '1TB storage',
        'Advanced backup',
        'Fast access',
        'Priority support',
        'Advanced security',
        'Data analytics'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom storage',
        'Custom backup',
        'Global access',
        'Dedicated support',
        'Custom security',
        'Advanced analytics'
      ]
    }
  ],
  'cloud-storage': [
    {
      name: 'Basic',
      price: '$49/mo',
      features: [
        '500GB storage',
        'Basic backup',
        'Standard access',
        'Community support',
        'Basic security'
      ]
    },
    {
      name: 'Pro',
      price: '$199/mo',
      recommended: true,
      features: [
        '2TB storage',
        'Advanced backup',
        'Fast access',
        'Priority support',
        'Advanced security',
        'File versioning'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom storage',
        'Custom backup',
        'Global access',
        'Dedicated support',
        'Custom security',
        'Advanced features'
      ]
    }
  ],
  'api-integration': [
    {
      name: 'Basic',
      price: '$89/mo',
      features: [
        '100K requests/mo',
        'Basic endpoints',
        'Standard access',
        'Community support',
        'Basic monitoring'
      ]
    },
    {
      name: 'Professional',
      price: '$299/mo',
      recommended: true,
      features: [
        '1M requests/mo',
        'Advanced endpoints',
        'Fast access',
        'Priority support',
        'Advanced monitoring',
        'Custom integration'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Unlimited requests',
        'Custom endpoints',
        'Global access',
        'Dedicated support',
        'Custom monitoring',
        'Advanced features'
      ]
    }
  ],
  'monitoring': [
    {
      name: 'Basic',
      price: '$69/mo',
      features: [
        'Basic metrics',
        'Standard alerts',
        'Daily reports',
        'Community support',
        'Basic dashboard'
      ]
    },
    {
      name: 'Advanced',
      price: '$249/mo',
      recommended: true,
      features: [
        'Advanced metrics',
        'Custom alerts',
        'Real-time reports',
        'Priority support',
        'Custom dashboard',
        'Trend analysis'
      ]
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      features: [
        'Custom metrics',
        'Advanced alerting',
        'Custom reports',
        'Dedicated support',
        'Custom features',
        'Advanced analytics'
      ]
    }
  ]
};