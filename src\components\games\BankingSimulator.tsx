import React, { useEffect, useRef } from 'react';

interface Transaction {
  id: number;
  type: 'deposit' | 'withdrawal' | 'loan' | 'investment';
  amount: number;
  timestamp: number;
  processed: boolean;
}

interface Customer {
  id: number;
  x: number;
  y: number;
  transaction: Transaction;
  waitTime: number;
  satisfaction: number;
  customerType: 'regular' | 'vip' | 'business' | 'student';
  riskLevel: 'low' | 'medium' | 'high';
  creditScore: number;
  animationFrame: number;
  isWalking: boolean;
  skinTone: string;
  outfit: string;
}

interface GameState {
  balance: number;
  customers: Customer[];
  transactions: Transaction[];
  reputation: number;
  gameTime: number;
  gameOver: boolean;
  selectedAction: 'approve' | 'deny';
  totalProfit: number;
  riskAssessment: boolean;
  fraudDetected: number;
  successfulTransactions: number;
  dailyTarget: number;
  complianceScore: number;
  marketConditions: 'bull' | 'bear' | 'stable';
}

class BankingSimulatorLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  state: GameState;
  isRunning: boolean = false;
  lastCustomerSpawn: number = 0;
  transactionIdCounter: number = 1;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    this.state = {
      balance: 50000,
      customers: [],
      transactions: [],
      reputation: 100,
      gameTime: 120,
      gameOver: false,
      selectedAction: 'approve',
      totalProfit: 0,
      riskAssessment: false,
      fraudDetected: 0,
      successfulTransactions: 0,
      dailyTarget: 10000,
      complianceScore: 100,
      marketConditions: 'stable'
    };

    canvas.addEventListener('click', this.handleClick);
    
    // Start game timer
    setInterval(() => {
      if (this.isRunning && !this.state.gameOver) {
        this.state.gameTime--;
        if (this.state.gameTime <= 0) {
          this.state.gameOver = true;
        }
      }
    }, 1000);
  }

  handleClick = (e: MouseEvent) => {
    if (!this.isRunning || this.state.gameOver) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    // Check action buttons
    if (y > this.height - 60) {
      if (x < this.width / 2) {
        this.state.selectedAction = 'approve';
      } else {
        this.state.selectedAction = 'deny';
      }
      return;
    }

    // Check customers
    this.state.customers.forEach((customer, index) => {
      if (x > customer.x - 30 && x < customer.x + 30 &&
          y > customer.y - 30 && y < customer.y + 30) {
        this.processTransaction(customer, index);
      }
    });
  };

  processTransaction(customer: Customer, index: number) {
    const transaction = customer.transaction;
    const approved = this.state.selectedAction === 'approve';

    // Risk assessment
    const riskScore = this.calculateRiskScore(customer);
    const shouldApprove = this.shouldApproveTransaction(customer, riskScore);

    if (approved) {
      if (customer.riskLevel === 'high' && riskScore > 70) {
        // Potential fraud detected
        this.state.fraudDetected++;
        this.state.complianceScore -= 10;
        customer.satisfaction = 20;
        this.state.reputation -= 15;
      } else {
        // Process legitimate transaction
        let fee = 0;
        let success = true;

        switch (transaction.type) {
          case 'deposit':
            this.state.balance += transaction.amount;
            fee = transaction.amount * (customer.customerType === 'vip' ? 0.01 : 0.02);
            customer.satisfaction = 100;
            break;
          case 'withdrawal':
            if (this.state.balance >= transaction.amount) {
              this.state.balance -= transaction.amount;
              fee = customer.customerType === 'vip' ? 0 : 5;
              customer.satisfaction = 100;
            } else {
              success = false;
              customer.satisfaction = 20;
              this.state.reputation -= 5;
            }
            break;
          case 'loan':
            if (customer.creditScore >= 600) {
              this.state.balance -= transaction.amount;
              const interestRate = customer.creditScore > 750 ? 0.08 : 0.12;
              fee = transaction.amount * interestRate;
              customer.satisfaction = 90;
            } else {
              success = false;
              customer.satisfaction = 30;
              this.state.complianceScore += 5; // Good risk management
            }
            break;
          case 'investment':
            this.state.balance += transaction.amount;
            const managementFee = customer.customerType === 'vip' ? 0.03 : 0.05;
            fee = transaction.amount * managementFee;
            customer.satisfaction = 95;
            break;
        }

        if (success) {
          this.state.totalProfit += fee;
          this.state.successfulTransactions++;

          // Customer type bonuses
          if (customer.customerType === 'vip') {
            this.state.reputation += 2;
          } else if (customer.customerType === 'business') {
            this.state.totalProfit += fee * 0.5; // Business bonus
          }
        }
      }
    } else {
      // Denied transaction
      if (shouldApprove) {
        // Wrongly denied good customer
        customer.satisfaction = Math.max(10, customer.satisfaction - 40);
        this.state.reputation -= 5;
      } else {
        // Correctly denied risky transaction
        customer.satisfaction = Math.max(30, customer.satisfaction - 20);
        this.state.complianceScore += 3;
      }
    }

    transaction.processed = true;
    this.state.transactions.push(transaction);
    this.state.reputation += (customer.satisfaction - 50) / 25;
    this.state.reputation = Math.max(0, Math.min(100, this.state.reputation));

    // Remove customer
    this.state.customers.splice(index, 1);
  }

  calculateRiskScore(customer: Customer): number {
    let score = 0;

    // Credit score factor
    score += (800 - customer.creditScore) / 10;

    // Transaction amount factor
    if (customer.transaction.amount > 10000) score += 20;
    if (customer.transaction.amount > 50000) score += 30;

    // Customer type factor
    if (customer.customerType === 'student') score += 10;

    // Risk level factor
    if (customer.riskLevel === 'medium') score += 15;
    if (customer.riskLevel === 'high') score += 30;

    return Math.min(100, score);
  }

  shouldApproveTransaction(customer: Customer, riskScore: number): boolean {
    if (riskScore > 70) return false;
    if (customer.creditScore < 500 && customer.transaction.type === 'loan') return false;
    if (customer.transaction.amount > this.state.balance && customer.transaction.type === 'withdrawal') return false;
    return true;
  }

  spawnCustomer() {
    if (this.state.customers.length < 5) {
      const customerTypes: Customer['customerType'][] = ['regular', 'vip', 'business', 'student'];
      const customerType = customerTypes[Math.floor(Math.random() * customerTypes.length)];
      const riskLevels: Customer['riskLevel'][] = ['low', 'medium', 'high'];
      const riskLevel = riskLevels[Math.floor(Math.random() * riskLevels.length)];

      const transactionTypes: Transaction['type'][] = ['deposit', 'withdrawal', 'loan', 'investment'];
      let type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];

      // Customer type affects transaction preferences
      if (customerType === 'business' && Math.random() < 0.6) {
        type = Math.random() < 0.5 ? 'loan' : 'investment';
      } else if (customerType === 'student' && Math.random() < 0.7) {
        type = Math.random() < 0.8 ? 'withdrawal' : 'deposit';
      }

      let amount: number;
      const multiplier = customerType === 'vip' ? 3 : customerType === 'business' ? 5 : 1;

      switch (type) {
        case 'deposit':
        case 'investment':
          amount = Math.floor(Math.random() * 5000 * multiplier) + 500;
          break;
        case 'withdrawal':
          amount = Math.floor(Math.random() * 2000 * multiplier) + 100;
          break;
        case 'loan':
          amount = Math.floor(Math.random() * 15000 * multiplier) + 1000;
          break;
      }

      // Add fraud risk
      const isFraudulent = riskLevel === 'high' && Math.random() < 0.3;
      if (isFraudulent) {
        amount *= 2; // Suspicious large amounts
      }

      const skinTones = ['#FDBCB4', '#EEA990', '#D08B5B', '#AE5D29', '#8B4513'];
      const outfits = ['suit', 'casual', 'formal', 'business'];

      const customer: Customer = {
        id: this.transactionIdCounter,
        x: -50,
        y: 150 + Math.random() * (this.height - 300),
        transaction: {
          id: this.transactionIdCounter++,
          type,
          amount,
          timestamp: Date.now(),
          processed: false
        },
        waitTime: 0,
        satisfaction: 80,
        customerType,
        riskLevel,
        creditScore: Math.floor(Math.random() * 400) + 400, // 400-800
        animationFrame: 0,
        isWalking: true,
        skinTone: skinTones[Math.floor(Math.random() * skinTones.length)],
        outfit: outfits[Math.floor(Math.random() * outfits.length)]
      };

      this.state.customers.push(customer);
    }
  }

  update() {
    if (!this.isRunning || this.state.gameOver) return;

    // Spawn customers
    if (Date.now() - this.lastCustomerSpawn > 4000) {
      this.spawnCustomer();
      this.lastCustomerSpawn = Date.now();
    }

    // Update customers
    this.state.customers.forEach(customer => {
      customer.waitTime++;
      if (customer.waitTime > 600) { // 10 seconds at 60fps
        customer.satisfaction -= 1;
        this.state.reputation -= 0.5;
      }
    });
  }

  drawCustomer(customer: Customer) {
    this.ctx.save();

    // Animation
    customer.animationFrame += 0.1;
    const walkOffset = customer.isWalking ? Math.sin(customer.animationFrame) * 1 : 0;

    // Shadow
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
    this.ctx.ellipse(customer.x, customer.y + 30, 18, 6, 0, 0, Math.PI * 2);
    this.ctx.fill();

    // Body based on outfit
    const outfitColors = {
      'suit': '#2c3e50',
      'casual': '#3498db',
      'formal': '#8e44ad',
      'business': '#27ae60'
    };

    this.ctx.fillStyle = outfitColors[customer.outfit] || '#6c757d';
    this.ctx.fillRect(customer.x - 12, customer.y - 10 + walkOffset, 24, 35);

    // Head
    this.ctx.fillStyle = customer.skinTone;
    this.ctx.beginPath();
    this.ctx.arc(customer.x, customer.y - 20 + walkOffset, 10, 0, Math.PI * 2);
    this.ctx.fill();

    // Hair
    this.ctx.fillStyle = '#8B4513';
    this.ctx.beginPath();
    this.ctx.arc(customer.x, customer.y - 25 + walkOffset, 11, Math.PI, 0);
    this.ctx.fill();

    // Eyes
    this.ctx.fillStyle = '#000';
    this.ctx.beginPath();
    this.ctx.arc(customer.x - 4, customer.y - 22 + walkOffset, 1.5, 0, Math.PI * 2);
    this.ctx.arc(customer.x + 4, customer.y - 22 + walkOffset, 1.5, 0, Math.PI * 2);
    this.ctx.fill();

    // Customer type indicator
    const typeColors = {
      'regular': '#6c757d',
      'vip': '#ffd700',
      'business': '#28a745',
      'student': '#17a2b8'
    };

    this.ctx.fillStyle = typeColors[customer.customerType];
    this.ctx.beginPath();
    this.ctx.arc(customer.x + 15, customer.y - 25 + walkOffset, 5, 0, Math.PI * 2);
    this.ctx.fill();

    // Risk level indicator
    const riskColors = {
      'low': '#28a745',
      'medium': '#ffc107',
      'high': '#dc3545'
    };

    this.ctx.fillStyle = riskColors[customer.riskLevel];
    this.ctx.fillRect(customer.x - 18, customer.y - 30 + walkOffset, 6, 6);

    this.ctx.restore();
  }

  drawTransactionBubble(customer: Customer) {
    const bubbleX = customer.x + 30;
    const bubbleY = customer.y - 80;
    const bubbleWidth = 140;
    const bubbleHeight = 70;

    // Bubble background
    const gradient = this.ctx.createLinearGradient(bubbleX, bubbleY, bubbleX, bubbleY + bubbleHeight);
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(1, '#f8f9fa');
    this.ctx.fillStyle = gradient;

    this.ctx.beginPath();
    this.ctx.roundRect(bubbleX, bubbleY, bubbleWidth, bubbleHeight, 8);
    this.ctx.fill();
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Transaction details
    this.ctx.fillStyle = '#000';
    this.ctx.font = 'bold 12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`${customer.transaction.type.toUpperCase()}`, bubbleX + 8, bubbleY + 18);

    this.ctx.font = '11px Arial';
    this.ctx.fillText(`Amount: $${customer.transaction.amount.toLocaleString()}`, bubbleX + 8, bubbleY + 35);
    this.ctx.fillText(`Credit Score: ${customer.creditScore}`, bubbleX + 8, bubbleY + 50);
    this.ctx.fillText(`Risk: ${customer.riskLevel.toUpperCase()}`, bubbleX + 8, bubbleY + 65);
  }

  draw() {
    // Clear canvas with bank interior gradient
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
    gradient.addColorStop(0, '#ecf0f1');
    gradient.addColorStop(0.3, '#bdc3c7');
    gradient.addColorStop(1, '#95a5a6');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw marble floor pattern
    this.ctx.strokeStyle = '#7f8c8d';
    this.ctx.lineWidth = 1;
    for (let i = 0; i < this.width; i += 50) {
      for (let j = 0; j < this.height - 100; j += 50) {
        this.ctx.strokeRect(i, j, 50, 50);
      }
    }

    // Draw bank counter
    this.ctx.fillStyle = '#34495e';
    this.ctx.fillRect(0, this.height / 2 + 40, this.width, 30);

    // Counter details
    this.ctx.fillStyle = '#2c3e50';
    this.ctx.fillRect(0, this.height / 2 + 35, this.width, 5);

    // Teller windows
    for (let i = 0; i < 4; i++) {
      const x = 100 + i * 150;
      this.ctx.fillStyle = '#2c3e50';
      this.ctx.fillRect(x, this.height / 2 + 20, 80, 20);
      this.ctx.fillStyle = '#ecf0f1';
      this.ctx.fillRect(x + 5, this.height / 2 + 25, 70, 10);
    }

    // Draw customers
    this.state.customers.forEach(customer => {
      this.drawCustomer(customer);
      this.drawTransactionBubble(customer);
    });

    // Draw action buttons
    this.ctx.fillStyle = this.state.selectedAction === 'approve' ? '#28a745' : '#6c757d';
    this.ctx.fillRect(0, this.height - 60, this.width / 2 - 5, 60);
    this.ctx.fillStyle = this.state.selectedAction === 'deny' ? '#dc3545' : '#6c757d';
    this.ctx.fillRect(this.width / 2 + 5, this.height - 60, this.width / 2 - 5, 60);
    
    this.ctx.fillStyle = '#fff';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('APPROVE', this.width / 4, this.height - 25);
    this.ctx.fillText('DENY', (this.width * 3) / 4, this.height - 25);

    // Draw UI
    this.ctx.fillStyle = '#000';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`Bank Balance: $${this.state.balance.toLocaleString()}`, 10, 20);
    this.ctx.fillText(`Profit: $${Math.round(this.state.totalProfit).toLocaleString()}`, 10, 40);
    this.ctx.fillText(`Reputation: ${Math.round(this.state.reputation)}%`, 10, 60);
    this.ctx.fillText(`Time: ${this.state.gameTime}s`, 10, 80);

    // Instructions
    this.ctx.font = '12px Arial';
    this.ctx.fillText('Click customers to process transactions', 10, this.height - 80);

    if (this.state.gameOver) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.width, this.height);
      this.ctx.fillStyle = '#fff';
      this.ctx.font = '24px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Bank Closed!', this.width/2, this.height/2);
      this.ctx.font = '16px Arial';
      this.ctx.fillText(`Final Balance: $${this.state.balance.toLocaleString()}`, this.width/2, this.height/2 + 30);
      this.ctx.fillText(`Total Profit: $${Math.round(this.state.totalProfit).toLocaleString()}`, this.width/2, this.height/2 + 50);
      this.ctx.fillText(`Final Reputation: ${Math.round(this.state.reputation)}%`, this.width/2, this.height/2 + 70);
    }
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = () => {
    if (!this.isRunning) return;
    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface BankingSimulatorProps {
  isPreview?: boolean;
}

const BankingSimulator: React.FC<BankingSimulatorProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<BankingSimulatorLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new BankingSimulatorLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default BankingSimulator;
