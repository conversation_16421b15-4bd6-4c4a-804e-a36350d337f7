import React from 'react';
import { Target, <PERSON>, Bar<PERSON>hart, MessageSquare } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const GameLeads = () => {
  const features = [
    {
      icon: <Target className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Lead Generation",
      description: "Convert players into qualified leads through engaging gameplay"
    },
    {
      icon: <Users className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "User Engagement",
      description: "Keep users engaged with interactive gaming experiences"
    },
    {
      icon: <BarChart className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Analytics Integration",
      description: "Track user behavior and conversion metrics"
    },
    {
      icon: <MessageSquare className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Lead Nurturing",
      description: "Built-in systems for follow-up and conversion"
    }
  ];

  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-6">Game-Based Lead Generation</h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Transform casual players into qualified leads through engaging, interactive gaming experiences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'var(--color-card-bg)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => e.currentTarget.style.borderColor = 'var(--color-card-hover-border)'}
              onMouseOut={(e) => e.currentTarget.style.borderColor = 'var(--color-card-border)'}
            >
              <div
                className="rounded-lg p-3 inline-block mb-4"
                style={{ backgroundColor: 'color-mix(in srgb, var(--color-primary) 10%, transparent 90%)' }}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {feature.title}
              </h3>
              <p style={{ color: 'var(--color-text-secondary)' }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-3xl font-bold mb-6">Why Choose Game-Based Lead Generation?</h2>
          <p className="text-gray-400 mb-6">
            Game-based lead generation provides an engaging way to capture and qualify leads while 
            providing value through entertainment. This approach results in higher conversion rates 
            and better qualified leads.
          </p>

          <h3 className="text-2xl font-bold mb-4">Technical Capabilities</h3>
          <ul className="text-gray-400 space-y-2 mb-6">
            <li>• CRM integration</li>
            <li>• Lead scoring systems</li>
            <li>• Analytics tracking</li>
            <li>• Email automation</li>
            <li>• Social sharing features</li>
          </ul>

          <h3 className="text-2xl font-bold mb-4">Development Process</h3>
          <p className="text-gray-400 mb-6">
            Our development process focuses on creating engaging games that naturally integrate 
            lead generation mechanics while maintaining user interest and enjoyment.
          </p>
        </div>

        <div className="mt-12 text-center">
          <button
            className="px-8 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: 'var(--color-button-primary-bg)',
              color: 'var(--color-button-primary-text)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-hover)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-bg)'}
          >
            Start Your Lead Generation Project
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameLeads;
