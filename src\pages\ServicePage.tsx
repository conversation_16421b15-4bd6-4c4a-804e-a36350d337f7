import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Check } from 'lucide-react';
import { Link, useParams } from 'react-router-dom';
import { services } from '../data/services';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import ContactForm from '../components/ContactForm';
import AuroraBackground from '../components/AuroraBackground';
import { serviceFeatures } from '../data/serviceFeatures';
import { servicePricing } from '../data/servicePricing';

interface PricingTier {
  name: string;
  price: string;
  features: string[];
  recommended?: boolean;
}

const ServicePage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const service = services.find(s => s.slug === slug);
  
  if (!service) {
    return (
      <div className="min-h-screen bg-black text-white gradient-bg flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Service Not Found</h1>
          <Link to="/" className="text-purple-400 hover:text-purple-300">Return to Home</Link>
        </div>
      </div>
    );
  }

  const features = serviceFeatures[slug as keyof typeof serviceFeatures] || [];
  const pricing = servicePricing[slug as keyof typeof servicePricing] || [];

  return (
    <div className="min-h-screen bg-black text-white gradient-bg">
      <Navbar />
      
      <div className="pt-24">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <AuroraBackground />
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <Link
              to="/#services"
              className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8 group"
            >
              <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
              Back to Services
            </Link>
            
            <div className="flex items-center gap-6 mb-8">
              <div className="w-16 h-16 rounded-full bg-purple-600/10 flex items-center justify-center">
                {service.icon}
              </div>
              <h1 className="text-4xl md:text-5xl font-bold gradient-text">{service.title}</h1>
            </div>
            
            <p className="text-xl text-gray-400 max-w-3xl">{service.description}</p>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gray-900/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold mb-12 text-center gradient-text">Key Features</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="p-6 bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700/50"
                >
                  <div className="w-12 h-12 rounded-full bg-purple-600/10 flex items-center justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-gray-900/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold mb-12 text-center gradient-text">Pricing Plans</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {pricing.map((tier, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`p-6 rounded-lg border ${
                    tier.recommended
                      ? 'border-purple-500 bg-purple-900/20'
                      : 'border-gray-700 bg-gray-800/50'
                  } backdrop-blur-sm relative`}
                >
                  {tier.recommended && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-purple-500 text-white px-4 py-1 rounded-full text-sm">
                      Recommended
                    </div>
                  )}
                  <h3 className="text-xl font-semibold mb-2">{tier.name}</h3>
                  <p className="text-3xl font-bold mb-6 text-purple-400">{tier.price}</p>
                  <ul className="space-y-3">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2 text-gray-300">
                        <Check className="text-purple-400" size={20} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full mt-8 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                    Get Started
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <ContactForm />
        <Footer />
      </div>
    </div>
  );
};

export default ServicePage;
