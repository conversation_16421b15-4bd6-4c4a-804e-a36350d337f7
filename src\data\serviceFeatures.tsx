import React from 'react';
import { 
  Code, Shield, Headphones, Database, Cloud, Terminal, BarChart, Cog, Brain, Network, 
  Monitor, Server, Lock, Activity, Settings, Globe, Key, LineChart, HardDrive, Webhook,
  Bell, AlertTriangle, Zap, Cpu, GitBranch, Heart, HardDriveDownload, ShoppingBag
} from 'lucide-react';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

export const serviceFeatures: Record<string, Feature[]> = {
  'cms-development': [
    {
      icon: <Code size={24} className="text-purple-400" />,
      title: 'WordPress Development',
      description: 'Custom WordPress themes and plugins development'
    },
    {
      icon: <ShoppingBag size={24} className="text-purple-400" />,
      title: 'WooCommerce Integration',
      description: 'E-commerce functionality with WooCommerce'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Custom Plugin Development',
      description: 'Tailored plugins for specific business needs'
    }
  ],
  'cloud-infrastructure': [
    {
      icon: <Cloud size={24} className="text-purple-400" />,
      title: 'Cloud Solutions',
      description: 'Secure and scalable cloud infrastructure setup'
    },
    {
      icon: <Network size={24} className="text-purple-400" />,
      title: 'Global Distribution',
      description: 'Worldwide content delivery and load balancing'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Security First',
      description: 'Enterprise-grade security and compliance'
    }
  ],
  'cybersecurity': [
    {
      icon: <Lock size={24} className="text-purple-400" />,
      title: 'Threat Protection',
      description: 'Advanced threat detection and prevention'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Security Audits',
      description: 'Regular security assessments and compliance checks'
    },
    {
      icon: <Activity size={24} className="text-purple-400" />,
      title: '24/7 Monitoring',
      description: 'Continuous security monitoring and incident response'
    }
  ],
  'remote-it-support': [
    {
      icon: <Headphones size={24} className="text-purple-400" />,
      title: '24/7 Support',
      description: 'Round-the-clock technical assistance'
    },
    {
      icon: <Heart size={24} className="text-purple-400" />,
      title: 'Proactive Care',
      description: 'Preventive maintenance and system health monitoring'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Quick Resolution',
      description: 'Fast problem solving and system optimization'
    }
  ],
  'custom-software': [
    {
      icon: <Code size={24} className="text-purple-400" />,
      title: 'Full-Stack Development',
      description: 'End-to-end web application development'
    },
    {
      icon: <Cpu size={24} className="text-purple-400" />,
      title: 'Custom Solutions',
      description: 'Tailored software for specific business needs'
    },
    {
      icon: <GitBranch size={24} className="text-purple-400" />,
      title: 'API Integration',
      description: 'Seamless third-party API integration'
    }
  ],
  'devops-cicd': [
    {
      icon: <Terminal size={24} className="text-purple-400" />,
      title: 'Automation',
      description: 'Streamlined deployment and integration processes'
    },
    {
      icon: <GitBranch size={24} className="text-purple-400" />,
      title: 'CI/CD Pipeline',
      description: 'Continuous integration and deployment setup'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Infrastructure Management',
      description: 'Infrastructure as Code and automation'
    }
  ],
  'data-analytics': [
    {
      icon: <BarChart size={24} className="text-purple-400" />,
      title: 'Data Analysis',
      description: 'Advanced analytics and reporting solutions'
    },
    {
      icon: <LineChart size={24} className="text-purple-400" />,
      title: 'Visualization',
      description: 'Interactive dashboards and visual reports'
    },
    {
      icon: <Brain size={24} className="text-purple-400" />,
      title: 'Predictive Analytics',
      description: 'AI-powered insights and forecasting'
    }
  ],
  'managed-it': [
    {
      icon: <Cog size={24} className="text-purple-400" />,
      title: 'IT Management',
      description: 'Complete IT infrastructure oversight'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'System Optimization',
      description: 'Performance tuning and maintenance'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Security Management',
      description: 'Comprehensive security solutions'
    }
  ],
  'digital-transformation': [
    {
      icon: <Brain size={24} className="text-purple-400" />,
      title: 'Digital Strategy',
      description: 'Strategic digital transformation planning'
    },
    {
      icon: <Zap size={24} className="text-purple-400" />,
      title: 'Innovation',
      description: 'Cutting-edge technology adoption'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Process Optimization',
      description: 'Business process digitization and automation'
    }
  ],
  'network-solutions': [
    {
      icon: <Network size={24} className="text-purple-400" />,
      title: 'Network Oversight',
      description: '24/7 network performance monitoring'
    },
    {
      icon: <Activity size={24} className="text-purple-400" />,
      title: 'Performance Analysis',
      description: 'Real-time performance metrics and alerts'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Security Monitoring',
      description: 'Network security and threat detection'
    }
  ],
  'ecommerce': [
    {
      icon: <ShoppingBag size={24} className="text-purple-400" />,
      title: 'E-commerce Solutions',
      description: 'Custom online store development'
    },
    {
      icon: <Server size={24} className="text-purple-400" />,
      title: 'Payment Integration',
      description: 'Secure payment gateway setup'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Store Management',
      description: 'Complete e-commerce platform management'
    }
  ],
  'vdi-solutions': [
    {
      icon: <Monitor size={24} className="text-purple-400" />,
      title: 'Virtual Desktops',
      description: 'Secure virtual desktop infrastructure'
    },
    {
      icon: <Monitor size={24} className="text-purple-400" />,
      title: 'Remote Access',
      description: 'Seamless remote workspace solutions'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Security',
      description: 'Enhanced security and access control'
    }
  ],
  'backup-recovery': [
    {
      icon: <HardDrive size={24} className="text-purple-400" />,
      title: 'Data Backup',
      description: 'Automated backup solutions'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Disaster Recovery',
      description: 'Business continuity planning'
    },
    {
      icon: <HardDriveDownload size={24} className="text-purple-400" />,
      title: 'Data Storage',
      description: 'Secure and redundant storage solutions'
    }
  ],
  'it-consulting': [
    {
      icon: <Brain size={24} className="text-purple-400" />,
      title: 'Strategic Planning',
      description: 'Long-term IT strategy development'
    },
    {
      icon: <BarChart size={24} className="text-purple-400" />,
      title: 'Risk Assessment',
      description: 'Comprehensive IT risk analysis'
    },
    {
      icon: <Settings size={24} className="text-purple-400" />,
      title: 'Technology Roadmap',
      description: 'Future-proof technology planning'
    }
  ],
  'database-management': [
    {
      icon: <Database size={24} className="text-purple-400" />,
      title: 'Database Administration',
      description: 'Expert database management and optimization'
    },
    {
      icon: <Shield size={24} className="text-purple-400" />,
      title: 'Data Security',
      description: 'Secure data storage and access control'
    },
    {
      icon: <Activity size={24} className="text-purple-400" />,
      title: 'Performance Monitoring',
      description: 'Real-time database performance tracking'
    }
  ]
};
