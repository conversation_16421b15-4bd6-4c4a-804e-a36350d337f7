import React from "react";
import Particles from "@tsparticles/react";
import type { Engine } from "@tsparticles/engine";
import { loadSlim } from "@tsparticles/slim";

const LogoWithParticles: React.FC = () => {
  const particlesInit = async (engine: Engine): Promise<void> => {
    // Load the slim version of tsparticles
    await loadSlim(engine);
  };

  return (
    <div className="relative group" style={{ width: '160px', height: '48px' }}>
      <img 
        src="/logos/Spirelab_Lite_logo.png" 
        alt="Spirelab Solutions" 
        className="h-12 w-auto relative z-10 transition-transform duration-300 group-hover:scale-105"
      />
      <div className="absolute inset-0 z-0">
        <Particles
          id="logo-fire-particles"
          particlesInit={particlesInit}
          options={{
            fullScreen: false,
            background: {
              color: "transparent"
            },
            particles: {
              color: {
                value: ["#ff0000", "#ff7f00", "#ffff00"]
              },
              opacity: {
                value: 0.5
              },
              size: {
                value: { min: 1, max: 3 }
              },
              move: {
                enable: true,
                speed: 2,
                direction: "top",
                random: false,
                straight: false,
                outModes: {
                  default: "out"
                }
              }
            },
            interactivity: {
              events: {
                onHover: {
                  enable: true,
                  mode: "repulse"
                }
              }
            }
          }}
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            zIndex: 0
          }}
        />
      </div>
    </div>
  );
};

export default LogoWithParticles;
