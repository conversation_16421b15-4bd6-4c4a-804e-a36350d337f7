import Database from 'better-sqlite3';
import { Pool } from 'pg';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Database type
type DatabaseType = 'postgresql' | 'sqlite';

class DatabaseAdapter {
  private dbType: DatabaseType = 'sqlite';
  private pgPool?: Pool;
  private sqliteDb?: Database.Database;

  constructor() {
    this.initializeDatabase();
  }

  private async initializeDatabase() {
    // Try PostgreSQL first
    try {
      this.pgPool = new Pool({
        user: process.env.POSTGRES_USER || 'postgres',
        host: process.env.POSTGRES_HOST || 'localhost',
        database: process.env.POSTGRES_DB || 'postgres',
        password: process.env.POSTGRES_PASSWORD || '',
        port: parseInt(process.env.POSTGRES_PORT || '5433'),
      });

      // Test the connection
      const client = await this.pgPool.connect();
      await client.query('SELECT 1');
      client.release();
      
      this.dbType = 'postgresql';
      console.log('Using PostgreSQL database');
      await this.initializePostgreSQLTables();
    } catch (error) {
      console.warn('PostgreSQL connection failed, falling back to SQLite:', error.message);
      this.initializeSQLite();
    }
  }

  private initializeSQLite() {
    // Fallback to SQLite
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const dbPath = path.join(dataDir, 'showcase.db');
    this.sqliteDb = new Database(dbPath);
    this.dbType = 'sqlite';
    console.log('Using SQLite database');
    this.initializeSQLiteTables();
  }

  private async initializePostgreSQLTables() {
    if (!this.pgPool) return;

    const client = await this.pgPool.connect();
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS projects (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          url TEXT NOT NULL,
          description TEXT NOT NULL,
          category TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS project_features (
          id SERIAL PRIMARY KEY,
          project_id INTEGER NOT NULL,
          feature TEXT NOT NULL,
          is_highlighted BOOLEAN DEFAULT FALSE,
          FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
        );
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          is_admin BOOLEAN DEFAULT FALSE
        );
      `);

      // Create admin user if it doesn't exist
      const adminResult = await client.query('SELECT id FROM users WHERE email = $1', ['<EMAIL>']);
      if (adminResult.rows.length === 0) {
        await client.query(
          'INSERT INTO users (email, password, is_admin) VALUES ($1, $2, $3)',
          ['<EMAIL>', 'Element@qaz', true]
        );
      }
    } finally {
      client.release();
    }
  }

  private initializeSQLiteTables() {
    if (!this.sqliteDb) return;

    this.sqliteDb.exec(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS project_features (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectId INTEGER NOT NULL,
        feature TEXT NOT NULL,
        isHighlighted BOOLEAN DEFAULT 0,
        FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        isAdmin BOOLEAN DEFAULT 0
      );
    `);

    // Create admin user
    try {
      this.sqliteDb.prepare('DELETE FROM users WHERE email = ?').run('<EMAIL>');
      this.sqliteDb.prepare('INSERT INTO users (email, password, isAdmin) VALUES (?, ?, ?)').run(
        '<EMAIL>',
        'Element@qaz',
        1
      );
    } catch (error) {
      console.warn('Admin user creation failed:', error.message);
    }
  }

  async query(text: string, params: any[] = []): Promise<any> {
    if (this.dbType === 'postgresql' && this.pgPool) {
      const client = await this.pgPool.connect();
      try {
        const result = await client.query(text, params);
        return result.rows;
      } finally {
        client.release();
      }
    } else if (this.dbType === 'sqlite' && this.sqliteDb) {
      // Convert PostgreSQL-style queries to SQLite
      const sqliteQuery = this.convertToSQLite(text, params);
      if (text.toLowerCase().includes('select')) {
        return this.sqliteDb.prepare(sqliteQuery.text).all(...sqliteQuery.params);
      } else {
        const result = this.sqliteDb.prepare(sqliteQuery.text).run(...sqliteQuery.params);
        return [{ id: result.lastInsertRowid, changes: result.changes }];
      }
    }
    throw new Error('No database connection available');
  }

  async queryOne(text: string, params: any[] = []): Promise<any> {
    const results = await this.query(text, params);
    return results[0] || null;
  }

  private convertToSQLite(text: string, params: any[]): { text: string; params: any[] } {
    // Convert PostgreSQL $1, $2 syntax to SQLite ? syntax
    let sqliteText = text;
    let paramIndex = 1;
    while (sqliteText.includes(`$${paramIndex}`)) {
      sqliteText = sqliteText.replace(`$${paramIndex}`, '?');
      paramIndex++;
    }

    // Convert PostgreSQL-specific syntax to SQLite
    sqliteText = sqliteText
      .replace(/created_at/g, 'createdAt')
      .replace(/updated_at/g, 'updatedAt')
      .replace(/project_id/g, 'projectId')
      .replace(/is_highlighted/g, 'isHighlighted')
      .replace(/is_admin/g, 'isAdmin')
      .replace(/json_agg\(/g, 'json_group_array(')
      .replace(/json_build_object/g, 'json_object')
      .replace(/CURRENT_TIMESTAMP/g, 'CURRENT_TIMESTAMP');

    return { text: sqliteText, params };
  }

  getDbType(): DatabaseType {
    return this.dbType;
  }
}

// Create singleton instance
const dbAdapter = new DatabaseAdapter();

export const query = (text: string, params?: any[]) => dbAdapter.query(text, params);
export const queryOne = (text: string, params?: any[]) => dbAdapter.queryOne(text, params);
export const queryAll = (text: string, params?: any[]) => dbAdapter.query(text, params);
export const getDbType = () => dbAdapter.getDbType();

export default dbAdapter;
