/* Theme Overrides - Royal Red Theme */

/* Navigation */
nav {
  border-color: rgba(196, 30, 58, 0.3) !important;
}

/* Dropdown menus */
.dropdown-menu {
  background-color: var(--color-background) !important;
  border: 1px solid rgba(196, 30, 58, 0.3) !important;
}

/* Dropdown items */
.dropdown-item:hover {
  background-color: rgba(196, 30, 58, 0.2) !important;
  color: white !important;
}

/* Buttons */
button.primary {
  background-color: var(--color-primary) !important;
  color: white !important;
}

button.primary:hover {
  background-color: var(--color-secondary) !important;
}

button.secondary {
  border-color: var(--color-primary) !important;
}

button.secondary:hover {
  background-color: rgba(196, 30, 58, 0.2) !important;
}

/* Icons */
.icon {
  color: var(--color-icon) !important;
}

/* Cards */
.card {
  border-color: rgba(196, 30, 58, 0.3) !important;
}

.card:hover {
  border-color: rgba(196, 30, 58, 0.8) !important;
}

/* Text */
.text-highlight {
  color: var(--color-primary) !important;
}

.text-accent {
  color: var(--color-accent) !important;
}

/* Specific overrides for existing classes */
.hover\:bg-purple-900\/50:hover {
  background-color: rgba(196, 30, 58, 0.2) !important;
}

.hover\:text-purple-400:hover {
  color: var(--color-primary) !important;
}

.text-purple-400 {
  color: var(--color-primary) !important;
}

.hover\:border-purple-500\/50:hover {
  border-color: rgba(196, 30, 58, 0.5) !important;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-primary)) !important;
}

.from-purple-400 {
  --tw-gradient-from: var(--color-primary) !important;
}

.via-pink-500 {
  --tw-gradient-stops: var(--tw-gradient-from), var(--color-secondary), var(--tw-gradient-to, rgba(236, 72, 153, 0)) !important;
}

.to-purple-600 {
  --tw-gradient-to: var(--color-primary) !important;
}

.from-purple-500\/10 {
  --tw-gradient-from: rgba(196, 30, 58, 0.1) !important;
}

.to-pink-500\/10 {
  --tw-gradient-to: rgba(139, 0, 0, 0.1) !important;
}

.bg-orange-500 {
  background-color: var(--color-accent) !important;
}

.border-white\/20 {
  border-color: rgba(196, 30, 58, 0.2) !important;
}

.hover\:bg-white\/10:hover {
  background-color: rgba(196, 30, 58, 0.1) !important;
}

.bg-white\/10 {
  background-color: rgba(196, 30, 58, 0.1) !important;
}

.bg-white\/5 {
  background-color: rgba(196, 30, 58, 0.05) !important;
}

/* Glow effects */
.text-glow {
  text-shadow: 0 0 10px rgba(196, 30, 58, 0.5) !important;
}
