import React, { useEffect, useRef } from 'react';

interface Customer {
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  speed: number;
  satisfaction: number;
  waitTime: number;
  served: boolean;
  outfit: string;
  color: string;
  skinTone: string;
  hairColor: string;
  accessory: string;
  budget: number;
  personality: 'trendy' | 'classic' | 'casual' | 'formal';
  animationFrame: number;
  isWalking: boolean;
}

interface GameState {
  customers: Customer[];
  revenue: number;
  reputation: number;
  gameTime: number;
  gameOver: boolean;
  selectedOutfit: string;
  selectedColor: string;
  selectedAccessory: string;
  inventory: { [key: string]: number };
  trends: { [key: string]: number };
  seasonalBonus: number;
  comboMultiplier: number;
  perfectMatches: number;
}

class FashionStoreGameLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  state: GameState;
  isRunning: boolean = false;
  lastCustomerSpawn: number = 0;
  outfits: string[] = ['Evening Dress', 'Business Suit', 'Casual Wear', 'Formal Attire', 'Summer Dress', 'Winter Coat'];
  colors: string[] = ['Crimson', 'Navy', 'Emerald', 'Onyx', 'Rose', 'Gold', 'Silver', 'Ivory'];
  accessories: string[] = ['Handbag', 'Jewelry', 'Scarf', 'Hat', 'Sunglasses', 'Watch'];
  skinTones: string[] = ['#FDBCB4', '#EEA990', '#D08B5B', '#AE5D29', '#8B4513'];
  hairColors: string[] = ['#8B4513', '#D2691E', '#FFD700', '#000000', '#A0522D'];

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    this.state = {
      customers: [],
      revenue: 0,
      reputation: 100,
      gameTime: 90,
      gameOver: false,
      selectedOutfit: this.outfits[0],
      selectedColor: this.colors[0],
      selectedAccessory: this.accessories[0],
      inventory: {
        'Evening Dress': 10, 'Business Suit': 8, 'Casual Wear': 15,
        'Formal Attire': 6, 'Summer Dress': 12, 'Winter Coat': 5
      },
      trends: {
        'Evening Dress': 1.2, 'Business Suit': 1.0, 'Casual Wear': 0.9,
        'Formal Attire': 1.1, 'Summer Dress': 1.3, 'Winter Coat': 0.8
      },
      seasonalBonus: 1.0,
      comboMultiplier: 1.0,
      perfectMatches: 0
    };

    canvas.addEventListener('click', this.handleClick);
    
    // Start game timer
    setInterval(() => {
      if (this.isRunning && !this.state.gameOver) {
        this.state.gameTime--;
        if (this.state.gameTime <= 0) {
          this.state.gameOver = true;
        }
      }
    }, 1000);
  }

  handleClick = (e: MouseEvent) => {
    if (!this.isRunning || this.state.gameOver) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    const interfaceY = this.height - 160;

    // Check interface clicks
    if (y > interfaceY) {
      // Color selection
      const colorStartY = interfaceY + 25;
      const colorSize = 25;
      for (let i = 0; i < this.colors.length; i++) {
        const colorX = 10 + (i % 4) * (colorSize + 5);
        const colorY = colorStartY + Math.floor(i / 4) * (colorSize + 5);

        if (x >= colorX && x <= colorX + colorSize &&
            y >= colorY && y <= colorY + colorSize) {
          this.state.selectedColor = this.colors[i];
          return;
        }
      }

      // Outfit selection
      for (let i = 0; i < this.outfits.length; i++) {
        const outfitX = 150 + (i % 3) * 80;
        const outfitY = interfaceY + 25 + Math.floor(i / 3) * 35;

        if (x >= outfitX && x <= outfitX + 75 &&
            y >= outfitY && y <= outfitY + 30) {
          if (this.state.inventory[this.outfits[i]] > 0) {
            this.state.selectedOutfit = this.outfits[i];
          }
          return;
        }
      }

      // Accessory selection
      for (let i = 0; i < this.accessories.length; i++) {
        const accX = 400 + (i % 3) * 60;
        const accY = interfaceY + 25 + Math.floor(i / 3) * 25;

        if (x >= accX && x <= accX + 55 &&
            y >= accY && y <= accY + 20) {
          this.state.selectedAccessory = this.accessories[i];
          return;
        }
      }
      return;
    }

    // Check if clicking on a customer
    this.state.customers.forEach(customer => {
      if (!customer.served &&
          x > customer.x - 25 && x < customer.x + 25 &&
          y > customer.y - 35 && y < customer.y + 25) {
        this.serveCustomer(customer);
      }
    });
  };

  serveCustomer(customer: Customer) {
    const outfitMatch = customer.outfit === this.state.selectedOutfit;
    const colorMatch = customer.color === this.state.selectedColor;
    const accessoryMatch = customer.accessory === this.state.selectedAccessory;

    // Check inventory
    if (this.state.inventory[this.state.selectedOutfit] <= 0) {
      customer.satisfaction = 10;
      customer.served = true;
      this.state.reputation -= 10;
      return;
    }

    let basePrice = 150;
    let satisfaction = 30;
    let matches = 0;

    if (outfitMatch) {
      matches++;
      satisfaction += 25;
      basePrice += 100;
    }
    if (colorMatch) {
      matches++;
      satisfaction += 20;
      basePrice += 50;
    }
    if (accessoryMatch) {
      matches++;
      satisfaction += 15;
      basePrice += 30;
    }

    // Perfect match bonus
    if (matches === 3) {
      satisfaction = 100;
      basePrice *= 1.5;
      this.state.perfectMatches++;
      this.state.comboMultiplier = Math.min(3.0, this.state.comboMultiplier + 0.2);
    } else {
      this.state.comboMultiplier = Math.max(1.0, this.state.comboMultiplier - 0.1);
    }

    // Apply personality bonus
    if (customer.personality === 'trendy' && this.state.trends[this.state.selectedOutfit] > 1.1) {
      satisfaction += 10;
      basePrice *= 1.2;
    }

    // Apply trend multiplier
    basePrice *= this.state.trends[this.state.selectedOutfit];
    basePrice *= this.state.comboMultiplier;

    // Budget check
    if (basePrice > customer.budget) {
      satisfaction = Math.max(10, satisfaction - 30);
      basePrice = customer.budget * 0.8;
    }

    this.state.revenue += Math.round(basePrice);
    this.state.inventory[this.state.selectedOutfit]--;
    customer.satisfaction = Math.min(100, satisfaction);
    customer.served = true;

    this.state.reputation += (satisfaction - 50) / 15;
    this.state.reputation = Math.max(0, Math.min(100, this.state.reputation));
  }

  spawnCustomer() {
    if (this.state.customers.length < 4) {
      const personalities: Customer['personality'][] = ['trendy', 'classic', 'casual', 'formal'];
      const personality = personalities[Math.floor(Math.random() * personalities.length)];

      // Personality affects preferences
      let preferredOutfits: string[];
      switch (personality) {
        case 'trendy':
          preferredOutfits = ['Summer Dress', 'Evening Dress', 'Casual Wear'];
          break;
        case 'classic':
          preferredOutfits = ['Business Suit', 'Formal Attire'];
          break;
        case 'casual':
          preferredOutfits = ['Casual Wear', 'Summer Dress'];
          break;
        case 'formal':
          preferredOutfits = ['Formal Attire', 'Business Suit', 'Evening Dress'];
          break;
      }

      const customer: Customer = {
        x: -40,
        y: 120 + Math.random() * (this.height - 280),
        targetX: 80 + Math.random() * (this.width - 160),
        targetY: 120 + Math.random() * (this.height - 280),
        speed: 0.8 + Math.random() * 0.6,
        satisfaction: 60,
        waitTime: 0,
        served: false,
        outfit: preferredOutfits[Math.floor(Math.random() * preferredOutfits.length)],
        color: this.colors[Math.floor(Math.random() * this.colors.length)],
        accessory: this.accessories[Math.floor(Math.random() * this.accessories.length)],
        skinTone: this.skinTones[Math.floor(Math.random() * this.skinTones.length)],
        hairColor: this.hairColors[Math.floor(Math.random() * this.hairColors.length)],
        budget: 100 + Math.random() * 400,
        personality,
        animationFrame: 0,
        isWalking: true
      };
      this.state.customers.push(customer);
    }
  }

  update() {
    if (!this.isRunning || this.state.gameOver) return;

    // Spawn customers with dynamic rate
    const spawnRate = Math.max(2000, 4000 - (this.state.gameTime * 10));
    if (Date.now() - this.lastCustomerSpawn > spawnRate) {
      this.spawnCustomer();
      this.lastCustomerSpawn = Date.now();
    }

    // Update trends periodically
    if (this.state.gameTime % 20 === 0) {
      this.updateTrends();
    }

    // Restock inventory periodically
    if (this.state.gameTime % 30 === 0) {
      this.restockInventory();
    }

    // Update customers
    this.state.customers.forEach((customer, index) => {
      if (!customer.served) {
        // Move towards target
        const dx = customer.targetX - customer.x;
        const dy = customer.targetY - customer.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 5) {
          customer.x += (dx / distance) * customer.speed;
          customer.y += (dy / distance) * customer.speed;
          customer.isWalking = true;
        } else {
          customer.isWalking = false;
          customer.waitTime++;

          // Patience decreases over time
          if (customer.waitTime > 180) { // 3 seconds at 60fps
            customer.satisfaction = Math.max(0, customer.satisfaction - 1);
            if (customer.waitTime > 600) { // 10 seconds
              this.state.reputation -= 2;
              this.state.customers.splice(index, 1); // Customer leaves
              return;
            }
          }
        }
      } else {
        // Move out of store
        customer.x += 3;
        customer.isWalking = true;
        if (customer.x > this.width + 50) {
          this.state.customers.splice(index, 1);
        }
      }
    });
  }

  updateTrends() {
    // Randomly shift trends
    Object.keys(this.state.trends).forEach(outfit => {
      const change = (Math.random() - 0.5) * 0.2;
      this.state.trends[outfit] = Math.max(0.5, Math.min(2.0, this.state.trends[outfit] + change));
    });
  }

  restockInventory() {
    // Add random stock
    Object.keys(this.state.inventory).forEach(outfit => {
      if (this.state.inventory[outfit] < 3) {
        this.state.inventory[outfit] += Math.floor(Math.random() * 3) + 1;
      }
    });
  }

  drawCustomer(customer: Customer) {
    this.ctx.save();

    // Animation
    customer.animationFrame += 0.1;
    const walkOffset = customer.isWalking ? Math.sin(customer.animationFrame) * 2 : 0;

    // Shadow
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
    this.ctx.ellipse(customer.x, customer.y + 25, 15, 5, 0, 0, Math.PI * 2);
    this.ctx.fill();

    // Body
    this.ctx.fillStyle = customer.skinTone;
    this.ctx.beginPath();
    this.ctx.ellipse(customer.x, customer.y - 5 + walkOffset, 12, 18, 0, 0, Math.PI * 2);
    this.ctx.fill();

    // Head
    this.ctx.beginPath();
    this.ctx.arc(customer.x, customer.y - 25 + walkOffset, 8, 0, Math.PI * 2);
    this.ctx.fill();

    // Hair
    this.ctx.fillStyle = customer.hairColor;
    this.ctx.beginPath();
    this.ctx.arc(customer.x, customer.y - 28 + walkOffset, 9, Math.PI, 0);
    this.ctx.fill();

    // Eyes
    this.ctx.fillStyle = '#000';
    this.ctx.beginPath();
    this.ctx.arc(customer.x - 3, customer.y - 27 + walkOffset, 1, 0, Math.PI * 2);
    this.ctx.arc(customer.x + 3, customer.y - 27 + walkOffset, 1, 0, Math.PI * 2);
    this.ctx.fill();

    // Outfit preview on customer
    const outfitColor = this.getOutfitColor(customer.outfit, customer.color);
    this.ctx.fillStyle = outfitColor;
    this.ctx.fillRect(customer.x - 8, customer.y - 15 + walkOffset, 16, 20);

    // Satisfaction indicator
    const satisfactionColor = customer.satisfaction > 70 ? '#28a745' :
                             customer.satisfaction > 40 ? '#ffc107' : '#dc3545';
    this.ctx.fillStyle = satisfactionColor;
    this.ctx.beginPath();
    this.ctx.arc(customer.x + 12, customer.y - 30 + walkOffset, 4, 0, Math.PI * 2);
    this.ctx.fill();

    this.ctx.restore();
  }

  getOutfitColor(outfit: string, color: string): string {
    const colorMap: { [key: string]: string } = {
      'Crimson': '#DC143C', 'Navy': '#000080', 'Emerald': '#50C878',
      'Onyx': '#353839', 'Rose': '#FF007F', 'Gold': '#FFD700',
      'Silver': '#C0C0C0', 'Ivory': '#FFFFF0'
    };
    return colorMap[color] || '#6c757d';
  }

  draw() {
    // Clear canvas with gradient background
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
    gradient.addColorStop(0, '#f8f9fa');
    gradient.addColorStop(1, '#e9ecef');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw store floor pattern
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 1;
    for (let i = 0; i < this.width; i += 40) {
      for (let j = 0; j < this.height - 160; j += 40) {
        this.ctx.strokeRect(i, j, 40, 40);
      }
    }

    // Draw store fixtures
    this.drawStoreFurniture();

    // Draw customers with enhanced graphics
    this.state.customers.forEach(customer => {
      this.drawCustomer(customer);

      // Customer request bubble with better styling
      if (!customer.served) {
        this.drawRequestBubble(customer);
      }
    });

    this.drawSelectionInterface();
    this.drawUI();
    this.drawGameOverScreen();
  }

  drawStoreFurniture() {
    // Clothing racks
    this.ctx.fillStyle = '#8B4513';
    this.ctx.fillRect(20, 50, 10, 80);
    this.ctx.fillRect(this.width - 30, 50, 10, 80);

    // Mirrors
    this.ctx.fillStyle = '#C0C0C0';
    this.ctx.fillRect(this.width - 50, 40, 15, 60);
    this.ctx.strokeStyle = '#000';
    this.ctx.strokeRect(this.width - 50, 40, 15, 60);

    // Cash register
    this.ctx.fillStyle = '#2c3e50';
    this.ctx.fillRect(this.width / 2 - 20, this.height - 180, 40, 20);
  }

  drawRequestBubble(customer: Customer) {
    const bubbleX = customer.x + 25;
    const bubbleY = customer.y - 60;
    const bubbleWidth = 120;
    const bubbleHeight = 50;

    // Bubble background with gradient
    const gradient = this.ctx.createLinearGradient(bubbleX, bubbleY, bubbleX, bubbleY + bubbleHeight);
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(1, '#f8f9fa');
    this.ctx.fillStyle = gradient;

    // Rounded rectangle
    this.ctx.beginPath();
    this.ctx.roundRect(bubbleX, bubbleY, bubbleWidth, bubbleHeight, 8);
    this.ctx.fill();
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Bubble tail
    this.ctx.beginPath();
    this.ctx.moveTo(bubbleX + 10, bubbleY + bubbleHeight);
    this.ctx.lineTo(customer.x, customer.y - 20);
    this.ctx.lineTo(bubbleX + 25, bubbleY + bubbleHeight);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fill();
    this.ctx.stroke();

    // Request text with icons
    this.ctx.fillStyle = '#000';
    this.ctx.font = 'bold 10px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`👗 ${customer.outfit}`, bubbleX + 8, bubbleY + 15);
    this.ctx.fillText(`🎨 ${customer.color}`, bubbleX + 8, bubbleY + 28);
    this.ctx.fillText(`💎 ${customer.accessory}`, bubbleX + 8, bubbleY + 41);

    // Budget indicator
    this.ctx.font = '8px Arial';
    this.ctx.fillStyle = '#6c757d';
    this.ctx.fillText(`Budget: $${Math.round(customer.budget)}`, bubbleX + 8, bubbleY + 52);
  }

  drawSelectionInterface() {
    const interfaceY = this.height - 160;

    // Background panel
    this.ctx.fillStyle = 'rgba(52, 58, 64, 0.95)';
    this.ctx.fillRect(0, interfaceY, this.width, 160);

    // Color selection area
    this.ctx.fillStyle = '#fff';
    this.ctx.font = 'bold 12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('Colors:', 10, interfaceY + 20);

    const colorStartY = interfaceY + 25;
    const colorSize = 25;
    for (let i = 0; i < this.colors.length; i++) {
      const x = 10 + (i % 4) * (colorSize + 5);
      const y = colorStartY + Math.floor(i / 4) * (colorSize + 5);

      this.ctx.fillStyle = this.getOutfitColor('', this.colors[i]);
      this.ctx.fillRect(x, y, colorSize, colorSize);

      if (this.state.selectedColor === this.colors[i]) {
        this.ctx.strokeStyle = '#ffc107';
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(x - 2, y - 2, colorSize + 4, colorSize + 4);
      }
    }

    // Outfit selection area
    this.ctx.fillStyle = '#fff';
    this.ctx.fillText('Outfits:', 150, interfaceY + 20);

    for (let i = 0; i < this.outfits.length; i++) {
      const x = 150 + (i % 3) * 80;
      const y = interfaceY + 25 + Math.floor(i / 3) * 35;

      const isSelected = this.state.selectedOutfit === this.outfits[i];
      const stock = this.state.inventory[this.outfits[i]] || 0;

      this.ctx.fillStyle = isSelected ? '#007bff' : (stock > 0 ? '#28a745' : '#dc3545');
      this.ctx.fillRect(x, y, 75, 30);

      this.ctx.fillStyle = '#fff';
      this.ctx.font = '8px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(this.outfits[i], x + 37, y + 12);
      this.ctx.fillText(`Stock: ${stock}`, x + 37, y + 22);
    }

    // Accessory selection
    this.ctx.fillStyle = '#fff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('Accessories:', 400, interfaceY + 20);

    for (let i = 0; i < this.accessories.length; i++) {
      const x = 400 + (i % 3) * 60;
      const y = interfaceY + 25 + Math.floor(i / 3) * 25;

      const isSelected = this.state.selectedAccessory === this.accessories[i];
      this.ctx.fillStyle = isSelected ? '#ffc107' : '#6c757d';
      this.ctx.fillRect(x, y, 55, 20);

      this.ctx.fillStyle = '#fff';
      this.ctx.font = '8px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(this.accessories[i], x + 27, y + 12);
    }
  }

  drawUI() {

    // Main UI panel
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.width, 35);

    this.ctx.fillStyle = '#fff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`💰 Revenue: $${this.state.revenue}`, 10, 22);
    this.ctx.fillText(`⭐ Reputation: ${Math.round(this.state.reputation)}%`, 150, 22);
    this.ctx.fillText(`⏰ Time: ${this.state.gameTime}s`, 320, 22);
    this.ctx.fillText(`🔥 Combo: x${this.state.comboMultiplier.toFixed(1)}`, 450, 22);

    // Trend indicators
    this.ctx.font = '10px Arial';
    this.ctx.fillText('📈 Trending:', 10, 50);
    let trendX = 80;
    Object.entries(this.state.trends).forEach(([outfit, multiplier]) => {
      if (multiplier > 1.1) {
        this.ctx.fillStyle = '#28a745';
        this.ctx.fillText(`${outfit} (+${Math.round((multiplier - 1) * 100)}%)`, trendX, 50);
        trendX += 120;
      }
    });
  }

  drawGameOverScreen() {
    if (this.state.gameOver) {
      // Overlay
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.85)';
      this.ctx.fillRect(0, 0, this.width, this.height);

      // Results panel
      const panelWidth = 400;
      const panelHeight = 300;
      const panelX = (this.width - panelWidth) / 2;
      const panelY = (this.height - panelHeight) / 2;

      this.ctx.fillStyle = '#fff';
      this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
      this.ctx.strokeStyle = '#007bff';
      this.ctx.lineWidth = 3;
      this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);

      // Title
      this.ctx.fillStyle = '#007bff';
      this.ctx.font = 'bold 24px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('🏪 Fashion Store Results', this.width/2, panelY + 40);

      // Stats
      this.ctx.fillStyle = '#000';
      this.ctx.font = '16px Arial';
      const stats = [
        `💰 Final Revenue: $${this.state.revenue}`,
        `⭐ Final Reputation: ${Math.round(this.state.reputation)}%`,
        `🎯 Perfect Matches: ${this.state.perfectMatches}`,
        `👥 Customers Served: ${this.state.customers.filter(c => c.served).length}`,
        `📊 Performance: ${this.getPerformanceRating()}`
      ];

      stats.forEach((stat, index) => {
        this.ctx.fillText(stat, this.width/2, panelY + 80 + index * 30);
      });
    }
  }

  getPerformanceRating(): string {
    const score = this.state.revenue + (this.state.reputation * 10) + (this.state.perfectMatches * 50);
    if (score > 2000) return '🌟 Fashion Mogul';
    if (score > 1500) return '👑 Style Expert';
    if (score > 1000) return '💎 Trendsetter';
    if (score > 500) return '👗 Boutique Owner';
    return '🛍️ Fashion Enthusiast';
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = () => {
    if (!this.isRunning) return;
    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface FashionStoreGameProps {
  isPreview?: boolean;
}

const FashionStoreGame: React.FC<FashionStoreGameProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<FashionStoreGameLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new FashionStoreGameLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default FashionStoreGame;
