import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Shield, Lock, AlertTriangle, Eye } from 'lucide-react';

const Cybersecurity = () => {
  const features = [
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Threat Protection",
      description: "Advanced threat detection and prevention systems"
    },
    {
      icon: <Lock className="text-purple-400" size={24} />,
      title: "Security Audits",
      description: "Comprehensive security assessments and compliance checks"
    },
    {
      icon: <AlertTriangle className="text-purple-400" size={24} />,
      title: "Incident Response",
      description: "24/7 security incident monitoring and response"
    },
    {
      icon: <Eye className="text-purple-400" size={24} />,
      title: "Security Monitoring",
      description: "Continuous security monitoring and threat analysis"
    }
  ];

  const pricing = [
    {
      name: "Essential",
      price: "Contact Us",
      features: [
        "Basic Security Assessment",
        "Vulnerability Scanning",
        "Security Monitoring",
        "Incident Alerts",
        "Basic Support"
      ]
    },
    {
      name: "Advanced",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Security Assessment",
        "Penetration Testing",
        "24/7 Monitoring",
        "Incident Response",
        "Compliance Management",
        "Priority Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Security Solutions",
        "Advanced Threat Protection",
        "Security Team Training",
        "Custom Compliance",
        "Dedicated SOC Team",
        "24/7 Premium Support"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Cybersecurity Services"
      description="Comprehensive security solutions to protect your business from digital threats"
      features={features}
      pricing={pricing}
      technologies={['SIEM', 'EDR', 'IDS/IPS', 'Firewall', 'Zero Trust', 'SOC']}
      codeExample={`// Security Configuration Example
const securityConfig = {
  firewallRules: [
    {
      port: 443,
      protocol: 'HTTPS',
      allowed: true,
      sources: ['trusted-ips']
    }
  ],
  
  encryptionConfig: {
    algorithm: 'AES-256-GCM',
    keyRotation: true,
    rotationPeriod: '30d'
  },

  accessControl: {
    mfa: true,
    sessionTimeout: 3600,
    maxLoginAttempts: 3,
    passwordPolicy: {
      minLength: 12,
      requireSpecialChars: true,
      requireNumbers: true,
      expiryDays: 90
    }
  }
}`}
    />
  );
};

export default Cybersecurity;
