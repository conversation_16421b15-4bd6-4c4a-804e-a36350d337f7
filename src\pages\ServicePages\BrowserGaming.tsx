import React from 'react';
import { Globe, Layers, Zap, Monitor, Smartphone, Users, Lock, Gauge } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const BrowserGaming = () => {
  const features = [
    {
      icon: <Monitor className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Cross-Platform Compatibility",
      description: "Games that run flawlessly across all modern browsers and devices"
    },
    {
      icon: <Zap className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Instant Playability",
      description: "No downloads or installations required - start playing immediately"
    },
    {
      icon: <Layers className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Progressive Web Apps",
      description: "Optional installation for offline play and native-like experience"
    },
    {
      icon: <Gauge className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Optimized Performance",
      description: "Fast loading times and smooth gameplay across all devices"
    }
  ];

  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-6">Browser-Based Gaming Solutions</h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Create engaging HTML5 games that run directly in the browser, providing seamless access to your gaming experience across all platforms.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'var(--color-card-bg)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => e.currentTarget.style.borderColor = 'var(--color-card-hover-border)'}
              onMouseOut={(e) => e.currentTarget.style.borderColor = 'var(--color-card-border)'}
            >
              <div
                className="rounded-lg p-3 inline-block mb-4"
                style={{ backgroundColor: 'color-mix(in srgb, var(--color-primary) 10%, transparent 90%)' }}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {feature.title}
              </h3>
              <p style={{ color: 'var(--color-text-secondary)' }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-3xl font-bold mb-6">Why Choose Browser-Based Games?</h2>
          <p className="text-gray-400 mb-6">
            Browser-based games offer unparalleled accessibility and reach. With no installation required, 
            players can jump straight into the action, making them perfect for casual gaming, educational 
            content, and marketing campaigns.
          </p>

          <h3 className="text-2xl font-bold mb-4">Technical Capabilities</h3>
          <ul className="text-gray-400 space-y-2 mb-6">
            <li>• Advanced 2D and 3D graphics using WebGL</li>
            <li>• Real-time multiplayer functionality</li>
            <li>• Offline support through Service Workers</li>
            <li>• Cross-device save states and progression</li>
            <li>• Integration with social platforms</li>
          </ul>

          <h3 className="text-2xl font-bold mb-4">Development Process</h3>
          <p className="text-gray-400 mb-6">
            Our development process focuses on creating optimized, responsive games that provide a 
            consistent experience across all platforms. We use modern web technologies and frameworks 
            to ensure high performance and maintainability.
          </p>
        </div>

        <div className="mt-12 text-center">
          <button
            className="px-8 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: 'var(--color-button-primary-bg)',
              color: 'var(--color-button-primary-text)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-hover)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-bg)'}
          >
            Start Your Game Project
          </button>
        </div>
      </div>
    </div>
  );
};

export default BrowserGaming;
