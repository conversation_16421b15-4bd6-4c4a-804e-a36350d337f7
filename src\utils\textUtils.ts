// Utility functions for text processing

// Clean text for TTS to make it sound natural
export function cleanTextForTTS(text: string): string {
  if (!text) return '';
  
  let cleanedText = text;
  
  // Remove thinking patterns first (more aggressive)
  cleanedText = cleanedText
    // Remove <think> tags and content
    .replace(/<think>[\s\S]*?<\/think>/gi, '')
    .replace(/<thinking>[\s\S]*?<\/thinking>/gi, '')
    .replace(/&lt;think&gt;[\s\S]*?&lt;\/think&gt;/gi, '')
    .replace(/&lt;thinking&gt;[\s\S]*?&lt;\/thinking&gt;/gi, '')
    // Remove any content that starts with <think> even without closing tag
    .replace(/<think>[\s\S]*/gi, '')
    .replace(/&lt;think&gt;[\s\S]*/gi, '')
    // Remove thinking phrases
    .replace(/^.*thinking.*$/gim, '')
    .replace(/^.*let me think.*$/gim, '')
    .replace(/^.*i need to.*$/gim, '')
    .replace(/^.*i should.*$/gim, '')
    .replace(/^.*let me check.*$/gim, '')
    .replace(/^.*make sure.*$/gim, '')
    .replace(/^.*double-check.*$/gim, '')
    .replace(/^.*alright.*$/gim, '')
    .replace(/^.*okay.*$/gim, '');
  
  // Remove markdown formatting for better TTS readability
  cleanedText = cleanedText
    // Remove bold formatting (**text** or __text__)
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/__(.*?)__/g, '$1')
    // Remove italic formatting (*text* or _text_)
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/_(.*?)_/g, '$1')
    // Remove other markdown elements that might interfere with TTS
    .replace(/`(.*?)`/g, '$1')  // Remove code backticks
    .replace(/#{1,6}\s*/g, '')  // Remove heading markers
    .replace(/^\s*[-*+]\s*/gm, '• ')  // Convert markdown lists to bullet points
    .replace(/^\s*\d+\.\s*/gm, '')  // Remove numbered list markers
    // Clean up extra whitespace
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();

  return cleanedText;
}

// Clean text for display (preserve some formatting but make it readable)
export function cleanTextForDisplay(text: string): string {
  if (!text) return '';
  
  let cleanedText = text;
  
  // Remove thinking patterns first (same as TTS)
  cleanedText = cleanedText
    // Remove <think> tags and content
    .replace(/<think>[\s\S]*?<\/think>/gi, '')
    .replace(/<thinking>[\s\S]*?<\/thinking>/gi, '')
    .replace(/&lt;think&gt;[\s\S]*?&lt;\/think&gt;/gi, '')
    .replace(/&lt;thinking&gt;[\s\S]*?&lt;\/thinking&gt;/gi, '')
    // Remove any content that starts with <think> even without closing tag
    .replace(/<think>[\s\S]*/gi, '')
    .replace(/&lt;think&gt;[\s\S]*/gi, '')
    // Remove thinking phrases
    .replace(/^.*thinking.*$/gim, '')
    .replace(/^.*let me think.*$/gim, '')
    .replace(/^.*i need to.*$/gim, '')
    .replace(/^.*i should.*$/gim, '')
    .replace(/^.*let me check.*$/gim, '')
    .replace(/^.*make sure.*$/gim, '')
    .replace(/^.*double-check.*$/gim, '')
    .replace(/^.*alright.*$/gim, '')
    .replace(/^.*okay.*$/gim, '');
  
  // Convert markdown to HTML-friendly format
  cleanedText = cleanedText
    // Convert bold markdown to HTML
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/__(.*?)__/g, '<strong>$1</strong>')
    // Convert italic markdown to HTML
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/_(.*?)_/g, '<em>$1</em>')
    // Convert code to HTML
    .replace(/`(.*?)`/g, '<code>$1</code>')
    // Convert lists to proper HTML
    .replace(/^\s*[-*+]\s*(.+)$/gm, '• $1')
    // Clean up extra whitespace
    .replace(/\n\s*\n/g, '\n')
    .trim();

  return cleanedText;
}
