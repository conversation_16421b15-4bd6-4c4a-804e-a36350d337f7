import { useEffect, useState } from 'react';
import { Engine } from "@tsparticles/engine";
import { Particles } from "@tsparticles/react";
import { loadSlim } from "@tsparticles/slim";
import './particles.css';

const ParticleEffect = () => {
  const [init, setInit] = useState(false);
  
  useEffect(() => {
    const initParticles = async () => {
      try {
        await loadSlim(window.tsParticles);
        setInit(true);
      } catch (error) {
        console.error("Error initializing particles:", error);
      }
    };
    
    initParticles();
  }, []);

  return (
    <div className="tech-particles">
      {init && (
        <Particles
          id="tsparticles"
          options={{
            fullScreen: false,
            fpsLimit: 120,
            particles: {
              number: {
                value: 150,
                density: {
                  enable: true
                }
              },
              color: {
                value: ["#ff3232", "#b63059", "#cc2e2e", "#ff0050", "#c73c3c"]
              },
              shape: {
                type: "circle"
              },
              opacity: {
                value: 0.4,
                animation: {
                  enable: true,
                  speed: 0.8,
                  sync: false
                }
              },
              size: {
                value: { min: 0.5, max: 2.5 },
                animation: {
                  enable: true,
                  speed: 2,
                  sync: false
                }
              },
              links: {
                enable: true,
                distance: 120,
                color: "#ff3250",
                opacity: 0.35,
                width: 1,
                triangles: {
                  enable: true,
                  color: "#ff0040",
                  opacity: 0.15
                }
              },
              move: {
                enable: true,
                speed: 1.5,
                direction: "none",
                random: true,
                straight: false,
                outModes: {
                  default: "out"
                },
                attract: {
                  enable: true,
                  rotate: {
                    x: 600,
                    y: 1200
                  }
                },
                trail: {
                  enable: true,
                  length: 5
                }
              }
            },
            interactivity: {
              events: {
                onHover: {
                  enable: true,
                  mode: ["grab", "bubble"]
                },
                onClick: {
                  enable: true,
                  mode: "push"
                },
                resize: {
                  enable: true
                }
              },
              modes: {
                grab: {
                  distance: 140,
                  links: {
                    opacity: 1
                  }
                },
                bubble: {
                  distance: 200,
                  size: 8,
                  duration: 2,
                  opacity: 0.8
                },
                repulse: {
                  distance: 200,
                  duration: 0.4
                },
                push: {
                  quantity: 6
                },
                remove: {
                  quantity: 2
                }
              }
            },
            detectRetina: true
          }}
        />
      )}
      
      {/* Data Streams */}
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      <div className="data-stream"></div>
      
      {/* Pulse Nodes */}
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      <div className="pulse-node"></div>
      
      {/* Digital Overlay */}
      <div className="digital-overlay"></div>
      
      {/* Tech Icons */}
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
      <div className="tech-icon"></div>
    </div>
  );
};

// Add this to the window object to avoid TypeScript errors
declare global {
  interface Window {
    tsParticles: Engine;
  }
}

// Add a fallback if window.tsParticles is undefined
if (typeof window !== 'undefined' && !window.tsParticles) {
  window.tsParticles = {} as Engine;
}

export default ParticleEffect;
