import express from 'express';
import multer from 'multer';
import nodemailer from 'nodemailer';
import cors from 'cors';

const router = express.Router();
const upload = multer({ dest: 'uploads/' });

// Configure SMTP transport
const transporter = nodemailer.createTransport({
  host: 'mail.spirelab.net',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'Alpha@123#$'
  },
  tls: {
    rejectUnauthorized: false
  }
});

// Enable CORS
router.use(cors());

// Handle form submission
router.post('/contact', upload.array('files', 4), async (req, res) => {
  try {
    const { firstName, lastName, email, phone, service, description } = req.body;
    const files = req.files;

    // Create email content
    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: `New Contact Form Submission - ${service}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${firstName} ${lastName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone}</p>
        <p><strong>Service:</strong> ${service}</p>
        <p><strong>Description:</strong></p>
        <p>${description.replace(/\n/g, '<br>')}</p>
      `,
      attachments: files ? files.map(file => ({
        filename: file.originalname,
        path: file.path
      })) : []
    };

    // Send email
    await transporter.sendMail(mailOptions);

    // Clean up uploaded files
    if (files) {
      files.forEach(file => {
        fs.unlinkSync(file.path);
      });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ success: false, error: 'Failed to send email' });
  }
});

export default router;
