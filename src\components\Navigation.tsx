import React, { useState, useEffect } from 'react';
import TopBar from './TopBar';
import Navbar from './Navbar';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed w-full z-50">
      <div 
        className={`transition-all duration-300 ease-in-out transform ${
          isScrolled ? '-translate-y-full h-0' : 'translate-y-0'
        }`}
        style={{ 
          height: isScrolled ? 0 : 'auto',
          overflow: 'hidden'
        }}
      >
        <TopBar />
      </div>
      <div 
        className={`transition-all duration-300 ease-in-out transform ${
          isScrolled ? 'translate-y-0' : 'translate-y-0'
        }`}
        style={{
          transform: `translateY(${isScrolled ? '0' : '0'})`
        }}
      >
        <Navbar />
      </div>
    </div>
  );
};

export default Navigation;
