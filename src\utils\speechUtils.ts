/**
 * Speech utility functions for text-to-speech functionality
 */

// Check if the browser supports speech synthesis
const isSpeechSynthesisSupported = (): boolean => {
  return 'speechSynthesis' in window;
};

// Get available voices
const getVoices = (): SpeechSynthesisVoice[] => {
  if (!isSpeechSynthesisSupported()) {
    return [];
  }
  
  return window.speechSynthesis.getVoices();
};

// Find the best quality voice
const findBestVoice = (): SpeechSynthesisVoice | null => {
  if (!isSpeechSynthesisSupported()) {
    return null;
  }
  
  const voices = getVoices();
  if (voices.length === 0) return null;
  
  // Preferred voices in order (premium voices first)
  const preferredVoices = [
    // Premium voices
    { nameContains: 'premium', lang: 'en-US' },
    { nameContains: 'enhanced', lang: 'en-US' },
    { nameContains: 'neural', lang: 'en-US' },
    { nameContains: 'wavenet', lang: 'en-US' },
    // Microsoft voices (often higher quality)
    { nameContains: 'microsoft', lang: 'en-US' },
    { nameContains: 'google', lang: 'en-US' },
    // Any English US voice
    { nameContains: '', lang: 'en-US' },
    // Any English voice
    { nameContains: '', lang: 'en-' },
  ];
  
  // Try to find a preferred voice
  for (const preference of preferredVoices) {
    const voice = voices.find(v => 
      v.lang.includes(preference.lang) && 
      v.name.toLowerCase().includes(preference.nameContains.toLowerCase())
    );
    if (voice) return voice;
  }
  
  // Default to first English voice or any voice
  return voices.find(v => v.lang.includes('en-')) || voices[0];
};

// Speak text using the Web Speech API with improved voice quality
const speak = (text: string, voiceIndex: number = -1, rate: number = 0.9, pitch: number = 1): void => {
  if (!isSpeechSynthesisSupported() || !text) {
    return;
  }

  // Cancel any ongoing speech
  window.speechSynthesis.cancel();
  
  // Create a new utterance
  const utterance = new SpeechSynthesisUtterance(text);
  
  // Get available voices
  const voices = getVoices();
  
  // Set voice - either use selected index or find best voice
  if (voiceIndex >= 0 && voices.length > voiceIndex) {
    utterance.voice = voices[voiceIndex];
  } else {
    const bestVoice = findBestVoice();
    if (bestVoice) utterance.voice = bestVoice;
  }
  
  // Set rate and pitch - slightly slower rate for better clarity
  utterance.rate = rate;
  utterance.pitch = pitch;
  
  // Speak the text
  window.speechSynthesis.speak(utterance);
};

// Stop speaking
const stopSpeaking = (): void => {
  if (isSpeechSynthesisSupported()) {
    window.speechSynthesis.cancel();
  }
};

// Check if currently speaking
const isSpeaking = (): boolean => {
  if (!isSpeechSynthesisSupported()) {
    return false;
  }
  
  return window.speechSynthesis.speaking;
};

export {
  isSpeechSynthesisSupported,
  getVoices,
  speak,
  stopSpeaking,
  isSpeaking
};
