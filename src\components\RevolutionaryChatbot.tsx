import React, { useState, useEffect, useRef } from 'react';
import { X, Send, Mic, MicOff, Volume2, VolumeX, Minimize2, Maximize2, Sparkles } from 'lucide-react';
import chatService from '../services/chatService';
import { ttsService } from '../services/ttsService';
import { cleanTextForTTS, cleanTextForDisplay } from '../utils/textUtils';
import { ChatCompletionMessageParam } from 'openai/resources';
import AIAvatar, { AIEmotion } from './AIAvatar';
import './RevolutionaryChatbot.css';

// Speech Recognition interfaces
interface SpeechRecognitionEvent {
  results: {
    [key: number]: {
      [key: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onstart: (() => void) | null;
  onend: (() => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  emotion?: AIEmotion;
}

const RevolutionaryChatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI assistant. I'm here to help you with any questions about our IT solutions and services. How can I assist you today?",
      isUser: false,
      timestamp: new Date(),
      emotion: 'happy'
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechEnabled, setSpeechEnabled] = useState(true);
  const [currentEmotion, setCurrentEmotion] = useState<AIEmotion>('idle');
  const [voiceLevel, setVoiceLevel] = useState(0);
  const [isThinking, setIsThinking] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      
      if (recognitionRef.current) {
        recognitionRef.current.continuous = false;
        recognitionRef.current.interimResults = false;
        recognitionRef.current.lang = 'en-US';

        recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
          const transcript = event.results[0][0].transcript;
          setInputText(transcript);
          setIsListening(false);
          setCurrentEmotion('curious');
        };

        recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
          setCurrentEmotion('idle');
        };

        recognitionRef.current.onend = () => {
          setIsListening(false);
          setCurrentEmotion('idle');
        };
      }
    }
  }, []);

  // Voice level simulation for visual effects
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isSpeaking) {
      interval = setInterval(() => {
        // Simulate voice level with random values for visual effect
        setVoiceLevel(0.3 + Math.random() * 0.7);
      }, 100);
    } else {
      setVoiceLevel(0);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isSpeaking]);

  // Determine emotion based on message content
  const determineEmotion = (text: string): AIEmotion => {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('excited') || lowerText.includes('amazing') || lowerText.includes('fantastic')) {
      return 'excited';
    } else if (lowerText.includes('happy') || lowerText.includes('great') || lowerText.includes('wonderful')) {
      return 'happy';
    } else if (lowerText.includes('question') || lowerText.includes('curious') || lowerText.includes('interesting')) {
      return 'curious';
    } else if (lowerText.includes('thinking') || lowerText.includes('analyzing') || lowerText.includes('processing')) {
      return 'thinking';
    } else {
      return 'idle';
    }
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);
    setIsThinking(true);
    setCurrentEmotion('thinking');

    try {
      const conversationHistory: ChatCompletionMessageParam[] = [
        ...messages.map(msg => ({
          role: msg.isUser ? 'user' as const : 'assistant' as const,
          content: msg.text
        })),
        { role: 'user', content: inputText }
      ];

      const response = await chatService.sendMessage(conversationHistory);
      const cleanedResponseForDisplay = cleanTextForDisplay(response.message);
      const cleanedResponseForTTS = cleanTextForTTS(response.message);
      const emotion = determineEmotion(cleanedResponseForDisplay);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: cleanedResponseForDisplay,
        isUser: false,
        timestamp: new Date(),
        emotion
      };

      setMessages(prev => [...prev, aiMessage]);
      setCurrentEmotion(emotion);
      setIsThinking(false);

      // Speak the response if speech is enabled
      if (speechEnabled && cleanedResponseForTTS) {
        setIsSpeaking(true);
        setCurrentEmotion('speaking');
        
        try {
          await ttsService.speak(cleanedResponseForTTS);
        } catch (error) {
          console.error('TTS Error:', error);
        } finally {
          setIsSpeaking(false);
          setCurrentEmotion(emotion);
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
      setCurrentEmotion('idle');
    } finally {
      setIsLoading(false);
      setIsThinking(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleListening = () => {
    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
      setCurrentEmotion('idle');
    } else {
      recognitionRef.current?.start();
      setIsListening(true);
      setCurrentEmotion('listening');
    }
  };

  const toggleSpeech = () => {
    if (isSpeaking) {
      ttsService.stop();
      setIsSpeaking(false);
      setCurrentEmotion('idle');
    }
    setSpeechEnabled(!speechEnabled);
  };

  const clearChat = () => {
    setMessages([{
      id: '1',
      text: "Hello! I'm your AI assistant. I'm here to help you with any questions about our IT solutions and services. How can I assist you today?",
      isUser: false,
      timestamp: new Date(),
      emotion: 'happy'
    }]);
    setCurrentEmotion('happy');
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="revolutionary-chat-trigger"
        aria-label="Open AI Assistant"
      >
        <div className="trigger-avatar">
          <AIAvatar 
            emotion="idle" 
            isListening={false} 
            isSpeaking={false} 
            isThinking={false}
          />
        </div>
        <div className="trigger-pulse" />
        <div className="trigger-text">
          <Sparkles className="sparkle-icon" />
          AI Assistant
        </div>
      </button>
    );
  }

  return (
    <div className={`revolutionary-chatbot ${isMinimized ? 'minimized' : ''}`}>
      {/* Header */}
      <div className="chat-header">
        <div className="header-content">
          <div className="ai-status">
            <div className="status-dot" />
            <span>AI Assistant Online</span>
          </div>
          <div className="header-controls">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="control-btn"
              aria-label={isMinimized ? "Maximize" : "Minimize"}
            >
              {isMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="control-btn close-btn"
              aria-label="Close"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* AI Avatar Section */}
          <div className="avatar-section">
            <AIAvatar 
              emotion={currentEmotion}
              isListening={isListening}
              isSpeaking={isSpeaking}
              isThinking={isThinking}
              voiceLevel={voiceLevel}
            />
          </div>

          {/* Messages */}
          <div className="messages-container">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}
              >
                <div className="message-content">
                  <div className="message-text">{message.text}</div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
                {!message.isUser && (
                  <div className="message-emotion">
                    {message.emotion && (
                      <span className={`emotion-indicator ${message.emotion}`}>
                        {message.emotion}
                      </span>
                    )}
                  </div>
                )}
              </div>
            ))}
            
            {isLoading && (
              <div className="message ai-message loading">
                <div className="message-content">
                  <div className="typing-indicator">
                    <div className="typing-dot" />
                    <div className="typing-dot" />
                    <div className="typing-dot" />
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="input-area">
            <div className="input-controls">
              <button
                onClick={toggleListening}
                className={`control-btn ${isListening ? 'active' : ''}`}
                disabled={isLoading}
                aria-label={isListening ? "Stop listening" : "Start voice input"}
              >
                {isListening ? <MicOff size={18} /> : <Mic size={18} />}
              </button>
              
              <button
                onClick={toggleSpeech}
                className={`control-btn ${speechEnabled ? 'active' : ''}`}
                aria-label={speechEnabled ? "Disable speech" : "Enable speech"}
              >
                {speechEnabled ? <Volume2 size={18} /> : <VolumeX size={18} />}
              </button>
            </div>

            <div className="input-wrapper">
              <input
                ref={inputRef}
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={isListening ? "Listening..." : "Type your message..."}
                disabled={isLoading || isListening}
                className="message-input"
              />
              
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading}
                className="send-btn"
                aria-label="Send message"
              >
                <Send size={18} />
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="chat-footer">
            <button onClick={clearChat} className="footer-btn">
              Clear Chat
            </button>
            <div className="tts-status">
              {ttsService.isElevenLabsAvailable() ? (
                <span className="status-text premium">🎙️ Premium Voice Active</span>
              ) : (
                <span className="status-text standard">🔊 Standard Voice</span>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default RevolutionaryChatbot;
