import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { GitMerge, Server, Settings, Workflow } from 'lucide-react';

const DevOpsCICD = () => {
  const features = [
    {
      icon: <GitMerge className="text-purple-400" size={24} />,
      title: "CI/CD Pipeline",
      description: "Automated build, test, and deployment pipelines"
    },
    {
      icon: <Server className="text-purple-400" size={24} />,
      title: "Infrastructure as Code",
      description: "Automated infrastructure provisioning and management"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Container Orchestration",
      description: "Docker and Kubernetes deployment and management"
    },
    {
      icon: <Workflow className="text-purple-400" size={24} />,
      title: "Process Automation",
      description: "Streamlined development and deployment workflows"
    }
  ];

  const pricing = [
    {
      name: "Startup",
      price: "Contact Us",
      features: [
        "Basic CI/CD Setup",
        "Docker Containerization",
        "Basic Monitoring",
        "Email Support",
        "Standard SLA"
      ]
    },
    {
      name: "Business",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced CI/CD Pipeline",
        "Kubernetes Orchestration",
        "Infrastructure as Code",
        "24/7 Monitoring",
        "Priority Support",
        "Custom Workflows"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom DevOps Solution",
        "Multi-Cloud Support",
        "Advanced Security",
        "Custom Integration",
        "Dedicated Team",
        "Premium SLA"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="DevOps & CI/CD Solutions"
      description="Streamline your development and deployment processes with modern DevOps practices"
      features={features}
      pricing={pricing}
      technologies={['Jenkins', 'GitLab', 'Docker', 'Kubernetes', 'Terraform', 'Ansible']}
      codeExample={`# Docker Compose Configuration
version: '3.8'

services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DB_HOST: db
    depends_on:
      - db
    networks:
      - app-network

  db:
    image: postgres:13
    volumes:
      - db-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secure_password
      POSTGRES_DB: myapp
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  db-data:`}
    />
  );
};

export default DevOpsCICD;
