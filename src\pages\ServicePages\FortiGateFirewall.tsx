import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Shield, Network, Cloud, Zap, Lock } from 'lucide-react';

const FortiGateFirewall = () => {
  const features = [
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Security Fabric",
      description: "Integrated security architecture with automated threat response"
    },
    {
      icon: <Network className="text-purple-400" size={24} />,
      title: "SD-WAN",
      description: "Built-in SD-WAN capabilities for intelligent application steering"
    },
    {
      icon: <Cloud className="text-purple-400" size={24} />,
      title: "FortiGuard Services",
      description: "Real-time threat intelligence and automated security updates"
    },
    {
      icon: <Lock className="text-purple-400" size={24} />,
      title: "Zero Trust Access",
      description: "Secure remote access with identity-based security"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Essential NGFW Features",
        "Basic SD-WAN",
        "IPSec VPN",
        "Web Filtering",
        "8x5 Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Threat Protection",
        "Security Fabric Integration",
        "Enterprise SD-WAN",
        "Zero Trust Access",
        "24/7 Support",
        "Priority Response"
      ]
    },
    {
      name: "Data Center",
      price: "Contact Us",
      features: [
        "High Performance Security",
        "Custom Integration",
        "High Availability",
        "DCSP Integration",
        "Dedicated Support",
        "Custom SLA"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="FortiGate Firewall Solutions"
      description="Enterprise security with integrated SD-WAN and advanced threat protection"
      features={features}
      pricing={pricing}
      technologies={['FortiOS', 'Security Fabric', 'SD-WAN', 'FortiGuard', 'Zero Trust', 'NGFW']}
      codeExample={`// FortiGate Security Fabric Integration
class FortiGateSecurityFabric {
  constructor() {
    this.devices = new Map();
    this.policies = new Map();
    this.fabricConnectors = [];
  }

  async initializeSecurityFabric() {
    // Initialize Security Fabric components
    await this.discoverDevices();
    await this.configureFabricConnectors();
    await this.synchronizePolicies();
    
    return {
      status: 'initialized',
      devices: this.devices.size,
      connectors: this.fabricConnectors.length
    };
  }

  async configureSdWan() {
    const sdWanConfig = {
      interfaces: this.getWanInterfaces(),
      healthChecks: this.configureHealthChecks(),
      rules: this.generateSdWanRules(),
      qos: {
        applications: this.getApplicationProfiles(),
        bandwidth: this.getBandwidthPolicies()
      }
    };

    // Apply SD-WAN configuration
    await this.applySdWanConfig(sdWanConfig);
    
    return {
      status: 'configured',
      activeInterfaces: sdWanConfig.interfaces.length,
      rules: sdWanConfig.rules.length
    };
  }

  async implementZeroTrust() {
    const zeroTrustPolicy = {
      identityVerification: true,
      devicePosture: true,
      applicationControl: true,
      microsegmentation: {
        enabled: true,
        segments: this.defineSecuritySegments()
      },
      authentication: {
        method: 'certificate',
        mfa: true,
        sso: true
      }
    };

    // Implement Zero Trust policies
    await this.applyZeroTrustPolicy(zeroTrustPolicy);
    
    return {
      status: 'implemented',
      segments: zeroTrustPolicy.microsegmentation.segments.length,
      authMethods: Object.keys(zeroTrustPolicy.authentication)
    };
  }
}`}
    />
  );
};

export default FortiGateFirewall;
