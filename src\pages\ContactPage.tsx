import React, { useState, FormEvent, useEffect } from 'react';
import { MapPin, Phone, Mail } from 'lucide-react';
import emailjs from '@emailjs/browser';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  service: string;
  description: string;
}

const ContactPage = () => {
  useEffect(() => {
    emailjs.init("lRPim9tbeflBWVbCy");
  }, []);

  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    service: '',
    description: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const templateParams = {
        to_name: "Spirelab",
        from_name: `${formData.firstName} ${formData.lastName}`,
        from_email: formData.email,
        phone_number: formData.phone,
        service_type: formData.service,
        message: formData.description
      };

      const result = await emailjs.send(
        'service_kfqm6jh',
        'template_vzr0p11',
        templateParams,
        'lRPim9tbeflBWVbCy'
      );

      if (result.text === 'OK') {
        setSubmitStatus('success');
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          service: '',
          description: ''
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-black min-h-screen pt-24 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="bg-gray-900/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-800">
            <h2 className="text-3xl font-bold text-white mb-6">CONTACT INFORMATION</h2>
            <p className="text-gray-400 mb-12">
              Cultivating Innovation in the Digital Landscape, We're Pioneers in Harnessing IT.
            </p>

            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <MapPin className="w-6 h-6 text-purple-500 mr-2" />
                  OFFICES
                </h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-purple-400 font-medium">UK Office</h4>
                    <p className="text-gray-400">23 New Drum Street</p>
                    <p className="text-gray-400">London</p>
                    <p className="text-gray-400">United Kingdom</p>
                    <p className="text-gray-400">E1 7AY</p>
                  </div>
                  <div>
                    <h4 className="text-purple-400 font-medium">Sri Lankan Office</h4>
                    <p className="text-gray-400">316 2nd Floor</p>
                    <p className="text-gray-400">Super Market Complex</p>
                    <p className="text-gray-400">Mount Lavinia</p>
                    <p className="text-gray-400">Sri Lanka</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Phone className="w-6 h-6 text-purple-500 mr-2" />
                  PHONE NUMBERS
                </h3>
                <div className="space-y-2">
                  <p className="text-gray-400">+44 (738) 187-649</p>
                  <p className="text-gray-400">+94 (764) 630-230</p>
                  <p className="text-gray-400">+94 (770) 130-044</p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Mail className="w-6 h-6 text-purple-500 mr-2" />
                  EMAIL ADDRESS
                </h3>
                <div className="space-y-2">
                  <p className="text-purple-400"><EMAIL></p>
                  <p className="text-purple-400"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gray-900/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-800">
            <h2 className="text-3xl font-bold text-white mb-4">KEEP IN TOUCH</h2>
            <p className="text-gray-400 mb-8">
              We're thrilled to have you visit our website. If you have any questions, suggestions, or just want to say hello, please feel free to reach out. We'd love to hear from you!
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-gray-400 mb-2">First Name *</label>
                  <input
                    id="firstName"
                    type="text"
                    name="firstName"
                    required
                    value={formData.firstName}
                    onChange={handleChange}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-gray-400 mb-2">Last Name *</label>
                  <input
                    id="lastName"
                    type="text"
                    name="lastName"
                    required
                    value={formData.lastName}
                    onChange={handleChange}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="email" className="block text-gray-400 mb-2">Email *</label>
                  <input
                    id="email"
                    type="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-gray-400 mb-2">Phone</label>
                  <input
                    id="phone"
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="service" className="block text-gray-400 mb-2">Services *</label>
                <select
                  id="service"
                  name="service"
                  required
                  value={formData.service}
                  onChange={handleChange}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                >
                  <option value="">Select a service</option>
                  <option value="CMS Development">CMS Development</option>
                  <option value="Cloud Infrastructure">Cloud Infrastructure</option>
                  <option value="Cybersecurity">Cybersecurity</option>
                  <option value="Remote IT Support">Remote IT Support</option>
                  <option value="Custom Software">Custom Software</option>
                  <option value="DevOps & CI/CD">DevOps & CI/CD</option>
                  <option value="Data Analytics">Data Analytics</option>
                  <option value="Managed IT">Managed IT</option>
                  <option value="Digital Transformation">Digital Transformation</option>
                  <option value="Network Solutions">Network Solutions</option>
                  <option value="E-commerce Solutions">E-commerce Solutions</option>
                  <option value="VDI Solutions">VDI Solutions</option>
                </select>
              </div>

              <div>
                <label htmlFor="description" className="block text-gray-400 mb-2">Project / Service Description *</label>
                <textarea
                  id="description"
                  name="description"
                  required
                  rows={6}
                  value={formData.description}
                  onChange={handleChange}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500"
                  placeholder="Type your message here"
                ></textarea>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </button>

              {submitStatus === 'success' && (
                <p className="text-green-500 mt-4 text-center">Message sent successfully!</p>
              )}
              {submitStatus === 'error' && (
                <p className="text-red-500 mt-4 text-center">
                  {errorMessage || 'Error sending message. Please try again.'}
                </p>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
