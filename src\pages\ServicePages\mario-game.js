class MarioGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        
        // Player properties
        this.player = {
            x: 50,
            y: this.height - 60,
            width: 30,
            height: 30,
            velocityY: 0,
            speed: 5,
            jumping: false
        };

        // Platform properties
        this.platforms = [
            { x: 0, y: this.height - 20, width: this.width, height: 20 },
            { x: 300, y: this.height - 100, width: 100, height: 20 },
            { x: 100, y: this.height - 180, width: 100, height: 20 }
        ];

        // Game state
        this.keys = {};
        this.gravity = 0.5;
        this.isRunning = false;

        // Event listeners
        window.addEventListener('keydown', (e) => this.keys[e.key] = true);
        window.addEventListener('keyup', (e) => this.keys[e.key] = false);
    }

    start() {
        this.isRunning = true;
        this.gameLoop();
    }

    stop() {
        this.isRunning = false;
    }

    update() {
        // Horizontal movement
        if (this.keys['ArrowLeft']) {
            this.player.x -= this.player.speed;
        }
        if (this.keys['ArrowRight']) {
            this.player.x += this.player.speed;
        }

        // Apply gravity
        this.player.velocityY += this.gravity;
        this.player.y += this.player.velocityY;

        // Check for platform collisions
        let onPlatform = false;
        for (let platform of this.platforms) {
            if (this.checkCollision(this.player, platform)) {
                if (this.player.velocityY > 0) {
                    this.player.y = platform.y - this.player.height;
                    this.player.velocityY = 0;
                    this.player.jumping = false;
                    onPlatform = true;
                }
            }
        }

        // Jumping
        if (this.keys['ArrowUp'] && !this.player.jumping && onPlatform) {
            this.player.velocityY = -12;
            this.player.jumping = true;
        }

        // Boundaries
        if (this.player.x < 0) this.player.x = 0;
        if (this.player.x + this.player.width > this.width) {
            this.player.x = this.width - this.player.width;
        }
    }

    checkCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#87CEEB';
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Draw platforms
        this.ctx.fillStyle = '#8B4513';
        for (let platform of this.platforms) {
            this.ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
        }

        // Draw player
        this.ctx.fillStyle = 'red';
        this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);
    }

    gameLoop() {
        if (!this.isRunning) return;

        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }

    resize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        this.width = width;
        this.height = height;
        
        // Adjust platform positions
        this.platforms[0].y = this.height - 20;
        this.platforms[0].width = this.width;
    }
}

// Initialize preview
function initMarioPreview() {
    const canvas = document.getElementById('mario-preview');
    if (canvas) {
        canvas.width = canvas.parentElement.clientWidth;
        canvas.height = canvas.parentElement.clientHeight;
        const game = new MarioGame(canvas);
        game.draw();
    }
}

// Initialize when document is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMarioPreview);
} else {
    initMarioPreview();
}
