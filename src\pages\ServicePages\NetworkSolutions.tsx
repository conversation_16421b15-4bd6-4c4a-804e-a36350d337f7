import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Network, Wifi, Settings, Activity } from 'lucide-react';

const NetworkSolutions = () => {
  const features = [
    {
      icon: <Network className="text-purple-400" size={24} />,
      title: "Network Management",
      description: "Comprehensive network monitoring and management"
    },
    {
      icon: <Wifi className="text-purple-400" size={24} />,
      title: "Wireless Solutions",
      description: "Enterprise-grade wireless network solutions"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Network Optimization",
      description: "Performance tuning and bandwidth optimization"
    },
    {
      icon: <Activity className="text-purple-400" size={24} />,
      title: "Performance Monitoring",
      description: "Real-time network performance monitoring"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Basic Network Monitoring",
        "Essential Security",
        "Email Support",
        "Monthly Reports",
        "Business Hours Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Monitoring",
        "Network Optimization",
        "Security Management",
        "24/7 Support",
        "Weekly Reports",
        "Priority Response"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Network Solutions",
        "Global Network Management",
        "Advanced Security",
        "Custom SLA",
        "Dedicated Team",
        "Executive Reports"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Network Solutions"
      description="Professional network monitoring and management services"
      features={features}
      pricing={pricing}
      technologies={['Cisco', 'Juniper', 'Meraki', 'Wireshark', 'PRTG', 'SolarWinds']}
      codeExample={`// Network Monitoring System
class NetworkMonitor {
  constructor() {
    this.devices = new Map();
    this.alerts = [];
    this.metrics = {
      bandwidth: [],
      latency: [],
      packetLoss: []
    };
  }

  async monitorNetwork() {
    const devices = await this.scanNetwork();
    
    devices.forEach(async device => {
      const metrics = await this.collectMetrics(device);
      this.updateDeviceStatus(device, metrics);
      
      if (this.detectAnomaly(metrics)) {
        await this.createAlert({
          device: device.id,
          type: 'anomaly',
          metrics: metrics,
          timestamp: new Date()
        });
      }
    });
  }

  async optimizePerformance() {
    const bottlenecks = this.identifyBottlenecks();
    const recommendations = this.generateRecommendations(bottlenecks);
    
    return {
      currentPerformance: this.calculatePerformance(),
      bottlenecks: bottlenecks,
      recommendations: recommendations,
      estimatedImprovement: this.calculateImprovementPotential()
    };
  }
}`}
    />
  );
};

export default NetworkSolutions;
