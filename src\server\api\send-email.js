const nodemailer = require('nodemailer');
const express = require('express');
const cors = require('cors');
const router = express.Router();

// Enable CORS
router.use(cors());
router.use(express.json());

// Create transporter
const transporter = nodemailer.createTransport({
  host: 'mail.spirelab.net',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'Alpha@123#$'
  },
  tls: {
    rejectUnauthorized: false
  }
});

router.post('/send-email', async (req, res) => {
  try {
    const { firstName, lastName, email, phone, service, description } = req.body;

    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: `New Contact Form Submission - ${service}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${firstName} ${lastName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone}</p>
        <p><strong>Service:</strong> ${service}</p>
        <p><strong>Description:</strong></p>
        <p>${description.replace(/\n/g, '<br>')}</p>
      `
    };

    await transporter.sendMail(mailOptions);
    res.json({ success: true });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
