/* Revolutionary Chatbot Styles */
.revolutionary-chat-trigger {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1), 
    rgba(147, 51, 234, 0.1)
  );
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 15px 25px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 50px rgba(59, 130, 246, 0.2);
  overflow: hidden;
  animation: triggerFloat 6s ease-in-out infinite;
}

.revolutionary-chat-trigger:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 0 80px rgba(59, 130, 246, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
}

@keyframes triggerFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.trigger-avatar {
  width: 60px;
  height: 60px;
  position: relative;
}

.trigger-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 50%;
  animation: triggerPulse 2s ease-out infinite;
}

@keyframes triggerPulse {
  0% {
    width: 60px;
    height: 60px;
    opacity: 1;
  }
  100% {
    width: 120px;
    height: 120px;
    opacity: 0;
  }
}

.trigger-text {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.sparkle-icon {
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
  50% { transform: rotate(180deg) scale(1.2); opacity: 1; }
}

/* Main Chatbot Container */
.revolutionary-chatbot {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 360px;
  height: 520px;
  z-index: 1000;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.9),
    rgba(30, 30, 50, 0.9)
  );
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 80px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: chatbotAppear 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
}

@keyframes chatbotAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.revolutionary-chatbot.minimized {
  height: 50px;
  animation: chatbotMinimize 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes chatbotMinimize {
  from { height: 520px; }
  to { height: 50px; }
}

/* Header */
.chat-header {
  padding: 15px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.2),
    rgba(147, 51, 234, 0.2)
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 20px #10b981;
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.header-controls {
  display: flex;
  gap: 10px;
}

.control-btn {
  width: 30px;
  height: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.control-btn.active {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

/* Avatar Section */
.avatar-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: radial-gradient(
    circle at center,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.avatar-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(147, 51, 234, 0.1), transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1), transparent 50%);
  animation: backgroundShift 8s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
  from { opacity: 0.5; }
  to { opacity: 1; }
}

/* Messages Container */
.messages-container {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Messages */
.message {
  display: flex;
  flex-direction: column;
  gap: 5px;
  animation: messageAppear 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  align-items: flex-end;
}

.ai-message {
  align-items: flex-start;
}

.message-content {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-message .message-content {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.3), 
    rgba(147, 51, 234, 0.3)
  );
  border-bottom-right-radius: 5px;
}

.ai-message .message-content {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1), 
    rgba(255, 255, 255, 0.05)
  );
  border-bottom-left-radius: 5px;
}

.message-text {
  color: white;
  line-height: 1.4;
  font-size: 13px;
}

.message-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 5px;
  text-align: right;
}

.ai-message .message-time {
  text-align: left;
}

.message-emotion {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.emotion-indicator {
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.emotion-indicator.happy { background: rgba(251, 191, 36, 0.2); color: #fbbf24; }
.emotion-indicator.excited { background: rgba(239, 68, 68, 0.2); color: #f87171; }
.emotion-indicator.curious { background: rgba(16, 185, 129, 0.2); color: #34d399; }
.emotion-indicator.thinking { background: rgba(168, 85, 247, 0.2); color: #c084fc; }

/* Loading Message */
.message.loading .message-content {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05), 
    rgba(255, 255, 255, 0.02)
  );
}

.typing-indicator {
  display: flex;
  gap: 5px;
  align-items: center;
  padding: 10px 0;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: typingDot 1.5s ease-in-out infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.3s; }
.typing-dot:nth-child(3) { animation-delay: 0.6s; }

@keyframes typingDot {
  0%, 60%, 100% { transform: scale(1); opacity: 0.6; }
  30% { transform: scale(1.3); opacity: 1; }
}

/* Input Area */
.input-area {
  padding: 15px;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.3),
    rgba(30, 30, 50, 0.3)
  );
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.input-wrapper {
  display: flex;
  gap: 10px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 13px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.message-input:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.send-btn {
  width: 38px;
  height: 38px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.8),
    rgba(147, 51, 234, 0.8)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Footer */
.chat-footer {
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-btn {
  padding: 8px 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.footer-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.tts-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-text {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-text.premium {
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

.status-text.standard {
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 480px) {
  .revolutionary-chat-trigger {
    bottom: 15px;
    right: 15px;
    padding: 10px 16px;
  }

  .trigger-text {
    font-size: 13px;
  }

  .revolutionary-chatbot {
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .avatar-section {
    padding: 15px;
  }

  .messages-container {
    padding: 12px;
  }

  .input-area {
    padding: 12px;
  }

  .message-content {
    max-width: 90%;
    padding: 10px 14px;
  }

  .message-text {
    font-size: 12px;
  }
}

/* Tablet responsive design */
@media (max-width: 768px) and (min-width: 481px) {
  .revolutionary-chatbot {
    width: 320px;
    height: 480px;
    bottom: 15px;
    right: 15px;
  }

  .avatar-section {
    padding: 18px;
  }

  .messages-container {
    padding: 12px;
  }

  .input-area {
    padding: 12px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .revolutionary-chatbot {
    border: 3px solid white;
  }
  
  .message-content {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
  
  .control-btn {
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}
