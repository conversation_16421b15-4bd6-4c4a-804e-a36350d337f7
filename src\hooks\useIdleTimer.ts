import { useState, useEffect } from 'react';

const STORAGE_KEY = 'lastPopupClose';
const ONE_DAY_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

const useIdleTimer = (initialTimeout: number = 5000, subsequentTimeout: number = 20000) => {
  const [isIdle, setIsIdle] = useState(false);
  const [hasShownOnce, setHasShownOnce] = useState(false);

  useEffect(() => {
    const lastClose = localStorage.getItem(STORAGE_KEY);
    const now = new Date().getTime();

    // If there's a stored date and it's less than a day ago, don't show popup
    if (lastClose && (now - parseInt(lastClose)) < ONE_DAY_MS) {
      return;
    }

    let timeoutId: ReturnType<typeof setTimeout>;

    const resetTimer = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      // Use longer timeout if popup has been shown once
      const timeout = hasShownOnce ? subsequentTimeout : initialTimeout;
      timeoutId = setTimeout(() => setIsIdle(true), timeout);
    };

    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart'
    ];

    events.forEach(event => {
      document.addEventListener(event, resetTimer);
    });

    resetTimer();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      events.forEach(event => {
        document.removeEventListener(event, resetTimer);
      });
    };
  }, [initialTimeout, subsequentTimeout, hasShownOnce]);

  const handleClose = () => {
    setIsIdle(false);
    setHasShownOnce(true);
    localStorage.setItem(STORAGE_KEY, new Date().getTime().toString());
  };

  return { isIdle, handleClose };
};

export default useIdleTimer;
