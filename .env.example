# PostgreSQL Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=spirelab_showcase
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Server Configuration
SERVER_PORT=3001
API_HOSTNAME=http://localhost:3001

# Client Configuration (Vite environment variables)
CLIENT_PORT=5173
VITE_API_HOSTNAME=http://localhost:3001

# Production Example:
# POSTGRES_HOST=your-production-db-host
# POSTGRES_DB=your-production-db
# POSTGRES_USER=your-production-user
# POSTGRES_PASSWORD=your-production-password
# SERVER_PORT=8080
# API_HOSTNAME=https://your-domain.com
# CLIENT_PORT=3000
# VITE_API_HOSTNAME=https://your-domain.com
