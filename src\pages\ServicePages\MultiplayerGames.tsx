import React from 'react';
import { Users, Zap, Shield, Globe } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const MultiplayerGames = () => {
  const features = [
    {
      icon: <Users className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Real-time Multiplayer",
      description: "Seamless real-time interaction between players worldwide"
    },
    {
      icon: <Zap className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Low Latency",
      description: "Optimized networking for responsive gameplay"
    },
    {
      icon: <Shield className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Secure Communication",
      description: "Encrypted data transfer and anti-cheat measures"
    },
    {
      icon: <Globe className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Global Scale",
      description: "Infrastructure that scales with your player base"
    }
  ];

  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-6">Real-Time Multiplayer Games</h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Create engaging multiplayer experiences with real-time interaction and seamless synchronization across all players.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'var(--color-card-bg)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => e.currentTarget.style.borderColor = 'var(--color-card-hover-border)'}
              onMouseOut={(e) => e.currentTarget.style.borderColor = 'var(--color-card-border)'}
            >
              <div
                className="rounded-lg p-3 inline-block mb-4"
                style={{ backgroundColor: 'color-mix(in srgb, var(--color-primary) 10%, transparent 90%)' }}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {feature.title}
              </h3>
              <p style={{ color: 'var(--color-text-secondary)' }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-3xl font-bold mb-6">Why Choose Multiplayer Games?</h2>
          <p className="text-gray-400 mb-6">
            Multiplayer games create engaging social experiences that keep players coming back. 
            Our solutions provide the infrastructure needed for seamless real-time interaction.
          </p>

          <h3 className="text-2xl font-bold mb-4">Technical Capabilities</h3>
          <ul className="text-gray-400 space-y-2 mb-6">
            <li>• WebSocket integration</li>
            <li>• State synchronization</li>
            <li>• Latency compensation</li>
            <li>• Scalable server architecture</li>
            <li>• Anti-cheat systems</li>
          </ul>

          <h3 className="text-2xl font-bold mb-4">Development Process</h3>
          <p className="text-gray-400 mb-6">
            Our development process focuses on creating robust, scalable multiplayer systems 
            that provide smooth gameplay experiences for players worldwide.
          </p>
        </div>

        <div className="mt-12 text-center">
          <button
            className="px-8 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: 'var(--color-button-primary-bg)',
              color: 'var(--color-button-primary-text)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-hover)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-bg)'}
          >
            Start Your Multiplayer Project
          </button>
        </div>
      </div>
    </div>
  );
};

export default MultiplayerGames;
