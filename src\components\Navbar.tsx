import { useState, useEffect } from 'react';
import { Menu, X, Github, Twitter, Linkedin, ChevronDown } from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import LogoWithParticles from './LogoWithParticles';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [servicesOpen, setServicesOpen] = useState(false);
  const [solutionsOpen, setSolutionsOpen] = useState(false);
  const [firewallsOpen, setFirewallsOpen] = useState(false);
  const [gameDevOpen, setGameDevOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as Element).closest('.dropdown-container')) {
        setServicesOpen(false);
        setSolutionsOpen(false);
        setFirewallsOpen(false);
        setGameDevOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  useEffect(() => {
    setServicesOpen(false);
    setSolutionsOpen(false);
    setFirewallsOpen(false);
    setGameDevOpen(false);
    setIsOpen(false);
  }, [location.pathname]);

  const serviceLinks = [
    { name: 'CMS Development', path: '/services/cms-development' },
    { name: 'Cloud Infrastructure', path: '/services/cloud-infrastructure' },
    { name: 'Cybersecurity', path: '/services/cybersecurity' },
    { name: 'Remote IT Support', path: '/services/remote-it-support' },
    { name: 'Custom Software', path: '/services/custom-software' },
    { name: 'DevOps & CI/CD', path: '/services/devops-cicd' },
    { name: 'Data Analytics', path: '/services/data-analytics' },
    { name: 'Managed IT', path: '/services/managed-it' },
    { name: 'Digital Transformation', path: '/services/digital-transformation' },
    { name: 'Network Solutions', path: '/services/network-solutions' },
    { name: 'E-commerce Solutions', path: '/services/ecommerce' },
    { name: 'VDI Solutions', path: '/services/vdi-solutions' }
  ];

  const firewallLinks = [
    { name: 'Sophos Firewall', path: '/services/sophos-firewall' },
    { name: 'FortiGate Firewall', path: '/services/fortigate-firewall' },
    { name: 'Netgate Firewall', path: '/services/netgate-firewall' },
    { name: 'SonicWall Firewall', path: '/services/sonicwall-firewall' }
  ];

  const gameDevLinks = [
    { name: 'Browser Gaming', path: '/services/browser-gaming' },
    { name: 'WebGL Canvas', path: '/services/webgl-canvas' },
    { name: 'Multiplayer Games', path: '/services/multiplayer-games' },
    { name: 'Game Leads', path: '/services/game-leads' }
  ];

  const handleNavClick = (path: string) => {
    if (path.startsWith('/#')) {
      const sectionId = path.substring(2);
      if (!isHomePage) {
        navigate('/');
        setTimeout(() => {
          const element = document.getElementById(sectionId);
          element?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } else {
        const element = document.getElementById(sectionId);
        element?.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      navigate(path);
    }
  };

  const toggleDropdown = (e: React.MouseEvent, dropdown: 'services' | 'solutions' | 'firewalls' | 'gamedev') => {
    e.stopPropagation();
    if (dropdown === 'services') {
      setServicesOpen(!servicesOpen);
      setSolutionsOpen(false);
      setGameDevOpen(false);
    } else if (dropdown === 'solutions') {
      setSolutionsOpen(!solutionsOpen);
      setServicesOpen(false);
      setGameDevOpen(false);
    } else if (dropdown === 'firewalls') {
      setFirewallsOpen(!firewallsOpen);
    } else {
      setGameDevOpen(!gameDevOpen);
      setServicesOpen(false);
      setSolutionsOpen(false);
    }
  };

  return (
    <nav className="w-full backdrop-blur-sm border-b theme-border" style={{ 
      backgroundColor: 'rgba(0, 0, 0, 0.8)'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <LogoWithParticles />
            </Link>
          </div>
          
          <div className="flex items-center space-x-6 ml-10">
            <Link to="/" className="text-sm font-medium transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              Home
            </Link>

            {/* Services Dropdown */}
            <div className="dropdown-container relative">
              <button
                onClick={(e) => toggleDropdown(e, 'services')}
                className="flex items-center space-x-1 text-sm font-medium transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-primary)'; 
                }}
                onMouseOut={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-secondary)'; 
                }}
              >
                <span>Our Services</span>
                <ChevronDown size={16} className={`transform transition-transform ${servicesOpen ? 'rotate-180' : ''}`} style={{ color: 'var(--color-primary)' }} />
              </button>
              {servicesOpen && (
                <div className="absolute left-0 mt-2 w-64 rounded-lg shadow-xl py-2 z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.95)', borderWidth: '1px', borderStyle: 'solid', borderColor: 'var(--color-card-border)' }}>
                  {serviceLinks.map((link) => (
                    <Link
                      key={link.path}
                      to={link.path}
                      className="block px-4 py-2 text-sm transition-colors duration-200"
                      style={{ color: 'var(--color-text-secondary)' }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.color = 'var(--color-text-primary)';
                        e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.1)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.color = 'var(--color-text-secondary)';
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {link.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Solutions Dropdown */}
            <div className="dropdown-container relative">
              <button
                onClick={(e) => toggleDropdown(e, 'solutions')}
                className="flex items-center space-x-1 text-sm font-medium transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-primary)'; 
                }}
                onMouseOut={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-secondary)'; 
                }}
              >
                <span>Solutions</span>
                <ChevronDown size={16} className={`transform transition-transform ${solutionsOpen ? 'rotate-180' : ''}`} style={{ color: 'var(--color-primary)' }} />
              </button>
              {solutionsOpen && (
                <div className="absolute left-0 mt-2 w-64 rounded-lg shadow-xl py-2 z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.95)', borderWidth: '1px', borderStyle: 'solid', borderColor: 'var(--color-card-border)' }}>
                  {/* Firewalls Submenu */}
                  <div className="relative group">
                    <button
                      onClick={(e) => toggleDropdown(e, 'firewalls')}
                      className="w-full flex items-center justify-between px-4 py-2 text-sm transition-colors duration-200"
                      style={{ color: 'var(--color-text-secondary)' }}
                      onMouseOver={(e) => {
                        if (!firewallsOpen) {
                          e.currentTarget.style.color = 'var(--color-text-primary)';
                          e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.1)';
                        }
                      }}
                      onMouseOut={(e) => {
                        if (!firewallsOpen) {
                          e.currentTarget.style.color = 'var(--color-text-secondary)';
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }
                      }}
                    >
                      <span>Firewalls</span>
                      <ChevronDown size={16} className={`transform transition-transform ${firewallsOpen ? 'rotate-180' : ''}`} style={{ color: 'var(--color-primary)' }} />
                    </button>
                    {firewallsOpen && (
                      <div className="absolute left-full top-0 mt-0 w-64 rounded-lg shadow-xl py-2" style={{ backgroundColor: 'rgba(0, 0, 0, 0.95)', borderWidth: '1px', borderStyle: 'solid', borderColor: 'var(--color-card-border)' }}>
                        {firewallLinks.map((link) => (
                          <Link
                            key={link.path}
                            to={link.path}
                            className="block px-4 py-2 text-sm transition-colors duration-200"
                            style={{ color: 'var(--color-text-secondary)' }}
                            onMouseOver={(e) => {
                              e.currentTarget.style.color = 'var(--color-text-primary)';
                              e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.1)';
                            }}
                            onMouseOut={(e) => {
                              e.currentTarget.style.color = 'var(--color-text-secondary)';
                              e.currentTarget.style.backgroundColor = 'transparent';
                            }}
                          >
                            {link.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Game Dev Dropdown */}
            <div className="dropdown-container relative">
              <button
                onClick={(e) => toggleDropdown(e, 'gamedev')}
                className="flex items-center space-x-1 text-sm font-medium transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-primary)'; 
                }}
                onMouseOut={(e) => { 
                  e.currentTarget.style.color = 'var(--color-text-secondary)'; 
                }}
              >
                <span>Game Dev</span>
                <ChevronDown size={16} className={`transform transition-transform ${gameDevOpen ? 'rotate-180' : ''}`} style={{ color: 'var(--color-primary)' }} />
              </button>
              {gameDevOpen && (
                <div className="absolute left-0 mt-2 w-48 rounded-lg shadow-xl py-2 z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.95)', borderWidth: '1px', borderStyle: 'solid', borderColor: 'var(--color-card-border)' }}>
                  {gameDevLinks.map((link) => (
                    <Link
                      key={link.path}
                      to={link.path}
                      className="block px-4 py-2 text-sm transition-colors duration-200"
                      style={{ color: 'var(--color-text-secondary)' }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.color = 'var(--color-text-primary)';
                        e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.1)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.color = 'var(--color-text-secondary)';
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {link.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            <Link
              to="/clients"
              className="text-sm font-medium transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}
            >
              Clients
            </Link>

            <Link
              to="/showcase"
              className="text-sm font-medium transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}
            >
              Showcase
            </Link>

            <Link
              to="/contact"
              className="text-sm font-medium transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}
            >
              Contact Us
            </Link>

            <button
              onClick={() => handleNavClick('/#about')}
              className="text-sm font-medium transition-all duration-300" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}
            >
              About
            </button>
            
            <div className="flex items-center space-x-6 ml-8">
              <a 
                href="https://github.com" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => {
                  e.currentTarget.style.color = 'var(--color-primary)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.color = 'var(--color-text-secondary)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <Github size={20} />
              </a>
              <a 
                href="https://twitter.com" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => {
                  e.currentTarget.style.color = 'var(--color-primary)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.color = 'var(--color-text-secondary)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <Twitter size={20} />
              </a>
              <a 
                href="https://linkedin.com" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="transition-all duration-300"
                style={{ color: 'var(--color-text-secondary)' }}
                onMouseOver={(e) => {
                  e.currentTarget.style.color = 'var(--color-primary)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.color = 'var(--color-text-secondary)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <Linkedin size={20} />
              </a>
            </div>

            <a 
              href="https://client.spirelab.net"
              target="_blank"
              rel="noopener noreferrer"
              className="ml-6 px-4 py-1 rounded-lg text-sm font-medium transition-all duration-300"
              style={{ 
                backgroundColor: 'var(--color-primary)', 
                color: 'white',
                border: 'none',
                textDecoration: 'none',
                display: 'inline-block'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--color-secondary)';
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--color-primary)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              Client Portal
            </a>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <LogoWithParticles />
              </Link>
            </div>

            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-400 hover:text-white"
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>

          {/* Mobile menu */}
          {isOpen && (
            <div className="border-t border-gray-800">
              <div className="px-2 pt-2 pb-3 space-y-1">
                <Link to="/" className="block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.05)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; e.currentTarget.style.backgroundColor = 'transparent'; }}>
                  Home
                </Link>

                {/* Mobile Services Dropdown */}
                <div className="relative">
                  <button
                    onClick={(e) => toggleDropdown(e, 'services')}
                    className="w-full flex items-center justify-between px-3 py-2 text-base font-medium text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                  >
                    <span>Services</span>
                    <ChevronDown size={16} className={`transform transition-transform ${servicesOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {servicesOpen && (
                    <div className="pl-4 space-y-1">
                      {serviceLinks.map((link) => (
                        <Link
                          key={link.path}
                          to={link.path}
                          className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                        >
                          {link.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Solutions Dropdown */}
                <div className="relative">
                  <button
                    onClick={(e) => toggleDropdown(e, 'solutions')}
                    className="w-full flex items-center justify-between px-3 py-2 text-base font-medium text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                  >
                    <span>Solutions</span>
                    <ChevronDown size={16} className={`transform transition-transform ${solutionsOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {solutionsOpen && (
                    <div className="pl-4 space-y-1">
                      {/* Mobile Firewalls Submenu */}
                      <button
                        onClick={(e) => toggleDropdown(e, 'firewalls')}
                        className="w-full flex items-center justify-between px-3 py-2 text-base font-medium text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                      >
                        <span>Firewalls</span>
                        <ChevronDown size={16} className={`transform transition-transform ${firewallsOpen ? 'rotate-180' : ''}`} />
                      </button>
                      {firewallsOpen && (
                        <div className="pl-4 space-y-1">
                          {firewallLinks.map((link) => (
                            <Link
                              key={link.path}
                              to={link.path}
                              className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                            >
                              {link.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Mobile Game Dev Dropdown */}
                <div className="relative">
                  <button
                    onClick={(e) => toggleDropdown(e, 'gamedev')}
                    className="w-full flex items-center justify-between px-3 py-2 text-base font-medium text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                  >
                    <span>Game Dev</span>
                    <ChevronDown size={16} className={`transform transition-transform ${gameDevOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {gameDevOpen && (
                    <div className="pl-4 space-y-1">
                      {gameDevLinks.map((link) => (
                        <Link
                          key={link.path}
                          to={link.path}
                          className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-opacity-20 rounded-md"
                        >
                          {link.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                <Link
                  to="/clients"
                  className="block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.05)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; e.currentTarget.style.backgroundColor = 'transparent'; }}
                >
                  Clients
                </Link>

                <Link
                  to="/showcase"
                  className="block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.05)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; e.currentTarget.style.backgroundColor = 'transparent'; }}
                >
                  Showcase
                </Link>

                <Link
                  to="/contact"
                  className="block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-text-primary)'; e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.05)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; e.currentTarget.style.backgroundColor = 'transparent'; }}
                >
                  KEEP IN TOUCH
                </Link>

                <button
                  onClick={() => {
                    handleNavClick('/#about');
                    setIsOpen(false);
                  }}
                  className="block w-full text-left px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md"
                  style={{ color: 'var(--color-text-secondary)' }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.color = 'var(--color-text-primary)';
                    e.currentTarget.style.backgroundColor = 'rgba(196, 30, 58, 0.05)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.color = 'var(--color-text-secondary)';
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  About
                </button>
                
                <div className="flex items-center space-x-4 px-3 py-2">
                  <a 
                    href="https://github.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="transition-all duration-300"
                    style={{ color: 'var(--color-text-secondary)' }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.color = 'var(--color-primary)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.color = 'var(--color-text-secondary)';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    <Github size={20} />
                  </a>
                  <a 
                    href="https://twitter.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="transition-all duration-300"
                    style={{ color: 'var(--color-text-secondary)' }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.color = 'var(--color-primary)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.color = 'var(--color-text-secondary)';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    <Twitter size={20} />
                  </a>
                  <a 
                    href="https://linkedin.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="transition-all duration-300"
                    style={{ color: 'var(--color-text-secondary)' }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.color = 'var(--color-primary)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.color = 'var(--color-text-secondary)';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    <Linkedin size={20} />
                  </a>
                </div>

                <div className="px-3 pt-2">
                  <a 
                    href="https://client.spirelab.net"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full px-4 py-1 rounded-lg text-sm font-medium transition-all duration-300 block text-center"
                    style={{ 
                      backgroundColor: 'var(--color-primary)', 
                      color: 'white',
                      border: 'none',
                      textDecoration: 'none'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-secondary)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-primary)';
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    Client Portal
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
