<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/logos/icon.ico" />
    <link rel="shortcut icon" href="/logos/icon.ico" />
    <link rel="apple-touch-icon" href="/logos/icon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Spirelab Solutions</title>
    <script>
      const services = [
        "Browser-Based Gaming",
        "WebGL & Canvas",
        "Real-Time Multiplayer",
        "Game-Based Lead Generation"
      ];
      let currentIndex = 0;

      setInterval(() => {
        currentIndex = (currentIndex + 1) % services.length;
        document.title = `Spirelab Solutions | ${services[currentIndex]}`;
      }, 5000);
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
