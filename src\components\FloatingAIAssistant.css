/* Floating AI Assistant */
.floating-ai-assistant {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Floating Avatar */
.floating-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 20px rgba(102, 126, 234, 0.4),
    0 0 0 2px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-avatar:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 8px 30px rgba(102, 126, 234, 0.6),
    0 0 0 3px rgba(255, 255, 255, 0.2);
}

.floating-avatar.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 
    0 8px 30px rgba(79, 172, 254, 0.6),
    0 0 0 3px rgba(255, 255, 255, 0.3);
}

.floating-avatar.speaking {
  animation: speaking-pulse 0.5s ease-in-out infinite alternate;
}

.floating-avatar.listening {
  animation: listening-glow 1s ease-in-out infinite alternate;
}

/* Avatar Core */
.avatar-core {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

/* Avatar Face */
.avatar-face {
  width: 30px;
  height: 30px;
  position: relative;
}

.avatar-eyes {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 20px;
  height: 8px;
  margin: 6px auto 4px;
}

.eye {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #333;
  position: relative;
  animation: blink 3s infinite;
}

.eye-pupil {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 1px;
  left: 1px;
}

.avatar-mouth {
  width: 8px;
  height: 4px;
  border-radius: 0 0 8px 8px;
  background: #333;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.floating-avatar.speaking .avatar-mouth {
  animation: mouth-speak 0.3s ease-in-out infinite alternate;
}

/* Pulse Rings */
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 2px solid rgba(102, 126, 234, 0.6);
  border-radius: 50%;
  animation: pulse-ring 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-ring.delay-1 {
  animation-delay: 0.5s;
  border-color: rgba(102, 126, 234, 0.4);
}

.pulse-ring.delay-2 {
  animation-delay: 1s;
  border-color: rgba(102, 126, 234, 0.2);
}

/* Status Dot */
.status-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.6);
  animation: status-pulse 2s ease-in-out infinite;
}

/* Sparkle Icon */
.sparkle-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  color: #fbbf24;
  animation: sparkle-twinkle 2s ease-in-out infinite;
  filter: drop-shadow(0 0 4px rgba(251, 191, 36, 0.6));
}

/* Chat Popup */
.chat-popup {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 380px;
  height: 500px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: popup-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Chat Header */
.chat-header {
  padding: 1rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: status-pulse 2s ease-in-out infinite;
}

.voice-status {
  font-size: 0.75rem;
  color: #6b7280;
}

.voice-status .premium {
  color: #f59e0b;
  font-weight: 500;
}

.voice-status .standard {
  color: #6b7280;
}

.close-btn {
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  transform: scale(1.05);
}

/* Messages Area */
.messages-area {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.messages-area::-webkit-scrollbar {
  width: 4px;
}

.messages-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.messages-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Messages */
.message {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  animation: message-appear 0.3s ease-out;
}

.message.user {
  align-items: flex-end;
}

.message.ai {
  align-items: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 16px;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.ai .message-content {
  background: rgba(255, 255, 255, 0.8);
  color: #1f2937;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom-left-radius: 4px;
}

.message.loading .message-content {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.message-time {
  font-size: 0.7rem;
  color: #9ca3af;
  padding: 0 0.5rem;
}

/* Typing Animation */
.typing-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  padding: 0.5rem 0;
}

.typing-dots .dot {
  width: 6px;
  height: 6px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing-bounce 1.4s ease-in-out infinite both;
}

.typing-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dots .dot:nth-child(3) { animation-delay: 0s; }

/* Input Area */
.input-area {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-row {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.voice-controls {
  display: flex;
  gap: 0.5rem;
}

.voice-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.voice-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  transform: translateY(-1px);
}

.voice-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.input-wrapper {
  flex: 1;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  outline: none;
  transition: all 0.2s ease;
}

.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: center;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
}

/* Animations */
@keyframes blink {
  0%, 90%, 100% { transform: scaleY(1); }
  95% { transform: scaleY(0.1); }
}

@keyframes mouth-speak {
  0% { transform: scaleY(1); }
  100% { transform: scaleY(1.5) scaleX(1.2); }
}

@keyframes speaking-pulse {
  0% { box-shadow: 0 8px 30px rgba(79, 172, 254, 0.6), 0 0 0 3px rgba(255, 255, 255, 0.3); }
  100% { box-shadow: 0 12px 40px rgba(79, 172, 254, 0.8), 0 0 0 5px rgba(255, 255, 255, 0.4); }
}

@keyframes listening-glow {
  0% { box-shadow: 0 8px 30px rgba(16, 185, 129, 0.6), 0 0 0 3px rgba(255, 255, 255, 0.3); }
  100% { box-shadow: 0 12px 40px rgba(16, 185, 129, 0.8), 0 0 0 5px rgba(255, 255, 255, 0.4); }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes status-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes sparkle-twinkle {
  0%, 100% { 
    opacity: 1; 
    transform: rotate(0deg) scale(1);
  }
  50% { 
    opacity: 0.6; 
    transform: rotate(180deg) scale(1.1);
  }
}

@keyframes popup-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes message-appear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .floating-ai-assistant {
    bottom: 1rem;
    right: 1rem;
  }
  
  .chat-popup {
    width: calc(100vw - 2rem);
    right: -1rem;
    height: 450px;
  }
  
  .floating-avatar {
    width: 50px;
    height: 50px;
  }
  
  .avatar-core {
    width: 32px;
    height: 32px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chat-popup {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .chat-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .ai-status {
    color: #f9fafb;
  }
  
  .message.ai .message-content {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-input {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .voice-btn, .clear-btn {
    background: rgba(31, 41, 55, 0.8);
    color: #d1d5db;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .input-area {
    background: rgba(17, 24, 39, 0.8);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
