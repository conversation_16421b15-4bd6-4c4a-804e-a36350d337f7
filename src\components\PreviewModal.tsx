import React, { useEffect, useState } from 'react';
import { X, Smartphone, Tablet, Laptop, Monitor, Maximize2 } from 'lucide-react';

interface PreviewModalProps {
  url: string;
  title: string;
  isOpen: boolean;
  onClose: () => void;
}

type DeviceType = 'mobile' | 'tablet' | 'laptop' | 'desktop' | 'fullscreen';

interface DeviceConfig {
  name: string;
  width: string;
  height: string;
  icon: React.ReactNode;
}

const PreviewModal: React.FC<PreviewModalProps> = ({ url, title, isOpen, onClose }) => {
  const [selectedDevice, setSelectedDevice] = useState<DeviceType>('fullscreen');
  
  const deviceConfigs: Record<DeviceType, DeviceConfig> = {
    mobile: {
      name: 'Mobile',
      width: '375px',
      height: '667px',
      icon: <Smartphone size={18} />
    },
    tablet: {
      name: 'Tablet',
      width: '768px',
      height: '1024px',
      icon: <Tablet size={18} />
    },
    laptop: {
      name: 'Laptop',
      width: '1366px',
      height: '768px',
      icon: <Laptop size={18} />
    },
    desktop: {
      name: 'Desktop',
      width: '1920px',
      height: '1080px',
      icon: <Monitor size={18} />
    },
    fullscreen: {
      name: 'Fullscreen',
      width: '100%',
      height: '100%',
      icon: <Maximize2 size={18} />
    }
  };
  useEffect(() => {
    // Prevent scrolling when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    // Cleanup function
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-90">
      <div className="relative w-full max-w-[95vw] h-[90vh] bg-gray-900 rounded-lg overflow-hidden shadow-2xl flex flex-col">
        {/* Header with title, device selector, and close button */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <h3 className="text-lg font-medium text-white truncate mr-2">{title}</h3>
          
          {/* Device selector */}
          <div className="flex-1 flex items-center justify-center space-x-2 px-2 overflow-x-auto hide-scrollbar">
            {Object.entries(deviceConfigs).map(([device, config]) => (
              <button
                key={device}
                onClick={() => setSelectedDevice(device as DeviceType)}
                className={`flex items-center px-3 py-1.5 rounded text-xs font-medium transition-colors ${selectedDevice === device 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`}
                aria-label={`View as ${config.name}`}
                title={`View as ${config.name}`}
              >
                <span className="mr-1.5">{config.icon}</span>
                <span className="hidden sm:inline">{config.name}</span>
              </button>
            ))}
          </div>
          
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors ml-2"
            aria-label="Close preview"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Preview container with responsive sizing based on selected device */}
        <div className="flex-1 overflow-hidden flex items-center justify-center bg-gray-950 p-4">
          <div 
            className={`relative bg-white ${selectedDevice !== 'fullscreen' ? 'rounded-lg shadow-lg overflow-hidden transition-all duration-300' : 'w-full h-full'}`}
            style={{
              width: deviceConfigs[selectedDevice].width,
              height: deviceConfigs[selectedDevice].height,
              maxWidth: '100%',
              maxHeight: '100%',
              transform: selectedDevice === 'mobile' ? 'scale(0.9)' : 'none'
            }}
          >
            <iframe 
              src={url} 
              title={title}
              className="w-full h-full border-0"
              sandbox="allow-scripts allow-same-origin allow-forms"
              loading="lazy"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewModal;
