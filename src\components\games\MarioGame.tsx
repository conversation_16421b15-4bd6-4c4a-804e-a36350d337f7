import React, { useEffect, useRef } from 'react';

interface Platform {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface Enemy {
  x: number;
  y: number;
  width: number;
  height: number;
  speed: number;
  direction: number;
  startX: number;
  range: number;
}

interface Coin {
  x: number;
  y: number;
  width: number;
  height: number;
  collected: boolean;
}

interface Player {
  x: number;
  y: number;
  width: number;
  height: number;
  velocityX: number;
  velocityY: number;
  speed: number;
  jumping: boolean;
  direction: number;
  lives: number;
  invulnerable: boolean;
  invulnerableTimer: number;
}

interface GameState {
  score: number;
  timeLeft: number;
  gameOver: boolean;
  cameraX: number;
}

class MarioGameLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  player: Player;
  platforms: Platform[];
  enemies: Enemy[];
  coins: Coin[];
  keys: { [key: string]: boolean };
  gravity: number;
  isRunning: boolean;
  gameState: GameState;
  levelWidth: number;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;
    this.levelWidth = 3000; // Extended level width for scrolling
    
    this.player = {
      x: 50,
      y: this.height - 60,
      width: 30,
      height: 30,
      velocityX: 0,
      velocityY: 0,
      speed: 5,
      jumping: false,
      direction: 1,
      lives: 3,
      invulnerable: false,
      invulnerableTimer: 0
    };

    // Create a more interesting level layout
    this.platforms = [
      { x: 0, y: this.height - 20, width: this.levelWidth, height: 20 }, // Ground
      { x: 300, y: this.height - 100, width: 100, height: 20 },
      { x: 500, y: this.height - 150, width: 100, height: 20 },
      { x: 700, y: this.height - 200, width: 100, height: 20 },
      { x: 900, y: this.height - 150, width: 100, height: 20 },
      { x: 1100, y: this.height - 100, width: 100, height: 20 },
      { x: 1300, y: this.height - 150, width: 100, height: 20 },
      { x: 1500, y: this.height - 200, width: 100, height: 20 },
      { x: 1700, y: this.height - 150, width: 100, height: 20 },
      { x: 1900, y: this.height - 100, width: 100, height: 20 },
    ];

    // Add enemies at strategic positions
    this.enemies = [
      { x: 400, y: this.height - 50, width: 30, height: 30, speed: 2, direction: 1, startX: 400, range: 100 },
      { x: 800, y: this.height - 50, width: 30, height: 30, speed: 2, direction: 1, startX: 800, range: 100 },
      { x: 1200, y: this.height - 50, width: 30, height: 30, speed: 2, direction: 1, startX: 1200, range: 100 },
      { x: 1600, y: this.height - 50, width: 30, height: 30, speed: 2, direction: 1, startX: 1600, range: 100 },
    ];

    // Add coins throughout the level
    this.coins = [];
    for (let i = 0; i < 20; i++) {
      this.coins.push({
        x: 300 + i * 150,
        y: this.height - 100 - Math.random() * 100,
        width: 20,
        height: 20,
        collected: false
      });
    }

    this.gameState = {
      score: 0,
      timeLeft: 120, // 2 minutes
      gameOver: false,
      cameraX: 0
    };

    this.keys = {};
    this.gravity = 0.5;
    this.isRunning = false;

    // Start countdown timer
    setInterval(() => {
      if (this.isRunning && !this.gameState.gameOver) {
        this.gameState.timeLeft--;
        if (this.gameState.timeLeft <= 0) {
          this.gameOver();
        }
      }
    }, 1000);
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameOver() {
    this.gameState.gameOver = true;
    this.isRunning = false;
  }

  checkCollision(rect1: any, rect2: any) {
    return rect1.x < rect2.x + rect2.width &&
           rect1.x + rect1.width > rect2.x &&
           rect1.y < rect2.y + rect2.height &&
           rect1.y + rect1.height > rect2.y;
  }

  update() {
    if (this.gameState.gameOver) return;

    // Handle player movement
    if (this.keys['ArrowLeft']) {
      this.player.velocityX = -this.player.speed;
      this.player.direction = -1;
    } else if (this.keys['ArrowRight']) {
      this.player.velocityX = this.player.speed;
      this.player.direction = 1;
    } else {
      this.player.velocityX = 0;
    }

    // Apply physics
    this.player.velocityY += this.gravity;
    this.player.x += this.player.velocityX;
    this.player.y += this.player.velocityY;

    // Update camera position
    this.gameState.cameraX = Math.max(0, Math.min(this.player.x - this.width / 3, this.levelWidth - this.width));

    // Check platform collisions
    let onPlatform = false;
    for (let platform of this.platforms) {
      if (this.checkCollision(this.player, platform)) {
        if (this.player.velocityY > 0) {
          this.player.y = platform.y - this.player.height;
          this.player.velocityY = 0;
          this.player.jumping = false;
          onPlatform = true;
        }
      }
    }

    // Handle jumping
    if (this.keys['ArrowUp'] && !this.player.jumping && onPlatform) {
      this.player.velocityY = -12;
      this.player.jumping = true;
    }

    // Update enemies
    for (let enemy of this.enemies) {
      enemy.x += enemy.speed * enemy.direction;
      
      // Enemy patrol behavior
      if (Math.abs(enemy.x - enemy.startX) > enemy.range) {
        enemy.direction *= -1;
      }

      // Check collision with player
      if (!this.player.invulnerable && this.checkCollision(this.player, enemy)) {
        this.player.lives--;
        if (this.player.lives <= 0) {
          this.gameOver();
        } else {
          // Make player invulnerable temporarily
          this.player.invulnerable = true;
          this.player.invulnerableTimer = 60; // 60 frames = 1 second at 60fps
        }
      }
    }

    // Update invulnerability
    if (this.player.invulnerable) {
      this.player.invulnerableTimer--;
      if (this.player.invulnerableTimer <= 0) {
        this.player.invulnerable = false;
      }
    }

    // Check coin collisions
    for (let coin of this.coins) {
      if (!coin.collected && this.checkCollision(this.player, coin)) {
        coin.collected = true;
        this.gameState.score += 100;
      }
    }

    // Level boundaries
    if (this.player.x < 0) this.player.x = 0;
    if (this.player.x + this.player.width > this.levelWidth) {
      this.player.x = this.levelWidth - this.player.width;
    }
  }

  draw() {
    this.ctx.save();
    
    // Clear canvas
    this.ctx.fillStyle = '#87CEEB';
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Apply camera transform
    this.ctx.translate(-this.gameState.cameraX, 0);

    // Draw platforms
    this.ctx.fillStyle = '#8B4513';
    for (let platform of this.platforms) {
      this.ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
    }

    // Draw enemies
    this.ctx.fillStyle = '#FF0000';
    for (let enemy of this.enemies) {
      this.ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
    }

    // Draw coins
    this.ctx.fillStyle = '#FFD700';
    for (let coin of this.coins) {
      if (!coin.collected) {
        this.ctx.beginPath();
        this.ctx.arc(coin.x + coin.width/2, coin.y + coin.height/2, coin.width/2, 0, Math.PI * 2);
        this.ctx.fill();
      }
    }

    // Draw player
    if (!this.player.invulnerable || Math.floor(Date.now() / 100) % 2) {
      this.ctx.fillStyle = '#00FF00';
      this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);
    }

    this.ctx.restore();

    // Draw HUD (not affected by camera)
    this.ctx.fillStyle = '#000000';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Lives: ${this.player.lives}`, 10, 30);
    this.ctx.fillText(`Score: ${this.gameState.score}`, 10, 60);
    this.ctx.fillText(`Time: ${this.gameState.timeLeft}`, 10, 90);

    if (this.gameState.gameOver) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.width, this.height);
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = '40px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('GAME OVER', this.width/2, this.height/2);
      this.ctx.font = '20px Arial';
      this.ctx.fillText(`Final Score: ${this.gameState.score}`, this.width/2, this.height/2 + 40);
    }
  }

  gameLoop = () => {
    if (!this.isRunning) return;

    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  }

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface MarioGameProps {
  isPreview?: boolean;
}

const MarioGame: React.FC<MarioGameProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<MarioGameLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new MarioGameLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (gameRef.current) {
          gameRef.current.keys[e.key] = true;
        }
      };

      const handleKeyUp = (e: KeyboardEvent) => {
        if (gameRef.current) {
          gameRef.current.keys[e.key] = false;
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);
      
      gameRef.current.start();

      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default MarioGame;
