import React from 'react';
import { Code2, <PERSON><PERSON>, <PERSON>ap, Monitor } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const WebGLCanvas = () => {
  const features = [
    {
      icon: <Code2 className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Advanced Graphics",
      description: "High-performance 2D and 3D rendering with WebGL and Canvas APIs"
    },
    {
      icon: <Cpu className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Hardware Acceleration",
      description: "Utilize GPU acceleration for smooth graphics and animations"
    },
    {
      icon: <Zap className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Real-time Rendering",
      description: "Fluid animations and responsive interactive graphics"
    },
    {
      icon: <Monitor className="w-6 h-6" style={{ color: 'var(--color-icon)' }} />,
      title: "Cross-browser Support",
      description: "Consistent performance across modern browsers and devices"
    }
  ];

  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-6">WebGL & Canvas Development</h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Create stunning visual experiences with hardware-accelerated graphics and animations using WebGL and Canvas technologies.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'var(--color-card-bg)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => e.currentTarget.style.borderColor = 'var(--color-card-hover-border)'}
              onMouseOut={(e) => e.currentTarget.style.borderColor = 'var(--color-card-border)'}
            >
              <div
                className="rounded-lg p-3 inline-block mb-4"
                style={{ backgroundColor: 'color-mix(in srgb, var(--color-primary) 10%, transparent 90%)' }}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {feature.title}
              </h3>
              <p style={{ color: 'var(--color-text-secondary)' }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-3xl font-bold mb-6">Why Choose WebGL & Canvas?</h2>
          <p className="text-gray-400 mb-6">
            WebGL and Canvas technologies provide the foundation for creating high-performance graphics 
            and animations directly in the browser. Perfect for games, data visualization, and interactive experiences.
          </p>

          <h3 className="text-2xl font-bold mb-4">Technical Capabilities</h3>
          <ul className="text-gray-400 space-y-2 mb-6">
            <li>• Hardware-accelerated 3D graphics</li>
            <li>• Custom shaders and effects</li>
            <li>• Physics simulations</li>
            <li>• Particle systems</li>
            <li>• Advanced animations</li>
          </ul>

          <h3 className="text-2xl font-bold mb-4">Development Process</h3>
          <p className="text-gray-400 mb-6">
            Our development process focuses on optimizing performance while maintaining visual quality. 
            We use modern frameworks and tools to create efficient, scalable graphics solutions.
          </p>
        </div>

        <div className="mt-12 text-center">
          <button
            className="px-8 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: 'var(--color-button-primary-bg)',
              color: 'var(--color-button-primary-text)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-hover)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--color-button-primary-bg)'}
          >
            Start Your Graphics Project
          </button>
        </div>
      </div>
    </div>
  );
};

export default WebGLCanvas;
