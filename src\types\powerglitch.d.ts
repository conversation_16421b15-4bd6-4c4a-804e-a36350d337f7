declare module 'powerglitch' {
  interface GlitchOptions {
    timing?: {
      duration?: number;
      iterations?: number;
      easing?: string;
    };
    glitchTimeSpan?: {
      start?: number;
      end?: number;
    };
    shake?: {
      velocity?: number;
      amplitudeX?: number;
      amplitudeY?: number;
    };
    slice?: {
      count?: number;
      velocity?: number;
      minHeight?: number;
      maxHeight?: number;
      hueRotate?: boolean;
    };
    pulse?: boolean;
  }

  export const PowerGlitch: {
    glitch: (element: HTMLElement, options?: GlitchOptions) => void;
  };
}
