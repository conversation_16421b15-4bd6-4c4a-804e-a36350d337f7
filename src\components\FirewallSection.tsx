
import { Link } from 'react-router-dom';
import { Shield, Lock, Network, Cloud } from 'lucide-react';
import { motion } from 'framer-motion';

const FirewallSection = () => {
  const firewalls = [
    {
      name: 'Sophos',
      icon: <Shield className="theme-icon w-12 h-12" />,
      description: 'Next-generation firewall with synchronized security and cloud-native management',
      features: ['Synchronized Security', 'Xstream Architecture', 'Cloud Management'],
      link: '/services/sophos-firewall'
    },
    {
      name: 'FortiGate',
      icon: <Lock className="theme-icon w-12 h-12" />,
      description: 'Enterprise security with integrated SD-WAN and advanced threat protection',
      features: ['Security Fabric', 'SD-WAN', 'FortiGuard Services'],
      link: '/services/fortigate-firewall'
    },
    {
      name: 'Netgate',
      icon: <Network className="theme-icon w-12 h-12" />,
      description: 'Enterprise-grade open source firewall with professional support',
      features: ['pfSense Plus', 'Open Source', 'Advanced Routing'],
      link: '/services/netgate-firewall'
    },
    {
      name: 'SonicWall',
      icon: <Cloud className="theme-icon w-12 h-12" />,
      description: 'Next-generation security with real-time breach detection and prevention',
      features: ['Capture ATP', 'Real-Time Protection', 'Cloud Edge'],
      link: '/services/sonicwall-firewall'
    }
  ];

  return (
    <section className="py-20" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-mono font-bold mb-6" style={{ color: 'var(--color-text-primary)' }}>
            <span className="relative inline-block">
              <span className="relative z-10">Enterprise Firewall Solutions</span>
              <span className="absolute bottom-0 left-0 w-full h-3 -mb-2 transform -skew-x-12" 
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.2 }}></span>
            </span>
          </h2>
          <p className="max-w-2xl mx-auto font-mono" style={{ color: 'var(--color-text-secondary)' }}>
            Comprehensive network security solutions to protect your organization from advanced threats
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {firewalls.map((firewall, index) => (
            <motion.div
              key={firewall.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <Link to={firewall.link}>
                <div className="h-full p-6 backdrop-blur-sm rounded-lg transition-all duration-300" 
                  style={{
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    borderColor: 'var(--color-card-border)'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.borderColor = 'var(--color-card-hover-border)';
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.borderColor = 'var(--color-card-border)';
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                  }}>
                  <div className="w-16 h-16 rounded-lg flex items-center justify-center mb-6 transition-colors"
                    style={{ backgroundColor: 'rgba(196, 30, 58, 0.1)' }}>
                    {firewall.icon}
                  </div>
                  <h3 className="text-xl font-mono font-semibold mb-4" style={{ color: 'var(--color-text-primary)' }}>{firewall.name}</h3>
                  <p className="mb-6 font-mono text-sm" style={{ color: 'var(--color-text-secondary)' }}>{firewall.description}</p>
                  <ul className="space-y-2">
                    {firewall.features.map((feature, i) => (
                      <li key={i} className="text-sm font-mono flex items-center" style={{ color: 'var(--color-text-secondary)' }}>
                        <span className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-center mt-12"
        >
          <Link
            to="/services/network-solutions"
            className="inline-flex items-center px-6 py-3 rounded-lg text-sm font-mono transition-all duration-300"
            style={{
              borderWidth: '1px',
              borderStyle: 'solid',
              borderColor: 'var(--color-button-secondary-border)',
              color: 'var(--color-text-primary)',
              backgroundColor: 'transparent'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--color-button-secondary-hover)';
              e.currentTarget.style.borderColor = 'var(--color-primary)';
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderColor = 'var(--color-button-secondary-border)';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            Explore All Security Solutions
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default FirewallSection;
