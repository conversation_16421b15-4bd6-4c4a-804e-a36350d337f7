import { useState } from 'react';
import { Globe, Code2, Target, Cpu } from 'lucide-react';
import { Link } from 'react-router-dom';
import MarioGame from './games/MarioGame';
import WebGLGame from './games/WebGLGame';
import SnakeGame from './games/SnakeGame';
import BreakoutGame from './games/BreakoutGame';
import FashionStoreGame from './games/FashionStoreGame';
import BankingSimulator from './games/BankingSimulator';
import RestaurantManager from './games/RestaurantManager';
import RealEstateTycoon from './games/RealEstateTycoon';
import GamePopup from './games/GamePopup';

const GameDev = () => {
  const [activeGame, setActiveGame] = useState<'mario' | 'webgl' | 'snake' | 'breakout' | 'fashion' | 'banking' | 'restaurant' | 'realestate' | null>(null);

  const features = [
    {
      icon: <Globe className="w-6 h-6 theme-icon" />,
      title: "Browser-Based Gaming",
      description: "Cross-platform HTML5 games that run seamlessly in modern browsers without installation",
      link: "/services/browser-gaming"
    },
    {
      icon: <Code2 className="w-6 h-6 theme-icon" />,
      title: "WebGL & Canvas",
      description: "High-performance 2D and 3D game rendering using cutting-edge web technologies",
      link: "/services/webgl-canvas"
    },
    {
      icon: <Cpu className="w-6 h-6 theme-icon" />,
      title: "Real-Time Multiplayer",
      description: "Engaging multiplayer experiences with WebSocket integration and state synchronization",
      link: "/services/multiplayer-games"
    },
    {
      icon: <Target className="w-6 h-6 theme-icon" />,
      title: "Game-Based Lead Generation",
      description: "Interactive gaming experiences designed to capture and convert qualified leads",
      link: "/services/game-leads"
    }
  ];

  const games = [
    {
      type: 'mario' as const,
      title: "Mario-like Platformer",
      description: "A classic platformer with enemies, coins, and lives",
      component: MarioGame
    },
    {
      type: 'webgl' as const,
      title: "WebGL Squares",
      description: "Interactive WebGL demo with mouse attraction physics",
      component: WebGLGame
    },
    {
      type: 'snake' as const,
      title: "Snake Game",
      description: "Classic snake game with increasing difficulty",
      component: SnakeGame
    },
    {
      type: 'breakout' as const,
      title: "Breakout",
      description: "Arcade-style brick breaker with mouse controls",
      component: BreakoutGame
    }
  ];

  const businessGames = [
    {
      type: 'fashion' as const,
      title: "Fashion Store Manager",
      description: "Manage a fashion boutique and serve customers with style",
      component: FashionStoreGame,
      sector: "Fashion & Retail"
    },
    {
      type: 'banking' as const,
      title: "Banking Simulator",
      description: "Process transactions and manage bank operations",
      component: BankingSimulator,
      sector: "Financial Services"
    },
    {
      type: 'restaurant' as const,
      title: "Restaurant Manager",
      description: "Run a busy restaurant and keep customers happy",
      component: RestaurantManager,
      sector: "Hospitality & Food"
    },
    {
      type: 'realestate' as const,
      title: "Real Estate Tycoon",
      description: "Build your property empire through smart investments",
      component: RealEstateTycoon,
      sector: "Real Estate & Investment"
    }
  ];

  return (
    <div className="py-20" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4" style={{ color: 'var(--color-text-primary)' }}>
            <span className="relative inline-block">
              <span className="relative z-10">Game Development Services</span>
              <span className="absolute bottom-0 left-0 w-full h-3 -mb-2 transform -skew-x-12" 
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.2 }}></span>
            </span>
          </h2>
          <p className="text-xl max-w-3xl mx-auto" style={{ color: 'var(--color-text-secondary)' }}>
            Transform your gaming vision into reality with our web-based game development expertise. 
            We create immersive browser games that combine cutting-edge technology with engaging gameplay.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <Link 
              to={feature.link}
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-hover-border)';
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(196, 30, 58, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-border)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div className="rounded-lg p-3 inline-block mb-4" style={{ backgroundColor: 'rgba(196, 30, 58, 0.1)' }}>
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {feature.title}
              </h3>
              <p className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                {feature.description}
              </p>
            </Link>
          ))}
        </div>

        <div className="text-center mb-12">
          <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-text-primary)' }}>
            <span className="relative inline-block">
              <span className="relative z-10">Try Our Game Demos</span>
              <span className="absolute bottom-0 left-0 w-full h-2 -mb-1 transform -skew-x-12" 
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.2 }}></span>
            </span>
          </h3>
          <p style={{ color: 'var(--color-text-secondary)' }}>
            Experience our game development capabilities firsthand with these interactive demos
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {games.map((game) => (
            <div
              key={game.type}
              onClick={() => setActiveGame(game.type)}
              className="backdrop-blur-sm rounded-lg overflow-hidden cursor-pointer transition-all duration-300"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-hover-border)';
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(196, 30, 58, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-border)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div className="aspect-[4/3]" style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}>
                <game.component isPreview={true} />
              </div>
              <div className="p-4">
                <h3 className="text-base font-semibold mb-1" style={{ color: 'var(--color-text-primary)' }}>
                  {game.title}
                </h3>
                <p className="text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                  {game.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mb-12 mt-20">
          <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-text-primary)' }}>
            <span className="relative inline-block">
              <span className="relative z-10">Business Sector Demos</span>
              <span className="absolute bottom-0 left-0 w-full h-2 -mb-1 transform -skew-x-12"
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.2 }}></span>
            </span>
          </h3>
          <p style={{ color: 'var(--color-text-secondary)' }}>
            Explore how gaming can enhance business operations across different industries
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {businessGames.map((game) => (
            <div
              key={game.type}
              onClick={() => setActiveGame(game.type)}
              className="backdrop-blur-sm rounded-lg overflow-hidden cursor-pointer transition-all duration-300"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-hover-border)';
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(196, 30, 58, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-border)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div className="aspect-[4/3]" style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}>
                <game.component isPreview={true} />
              </div>
              <div className="p-4">
                <div className="text-xs font-medium mb-1" style={{ color: 'var(--color-primary)' }}>
                  {game.sector}
                </div>
                <h3 className="text-base font-semibold mb-1" style={{ color: 'var(--color-text-primary)' }}>
                  {game.title}
                </h3>
                <p className="text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                  {game.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <GamePopup 
          gameType={activeGame} 
          onClose={() => setActiveGame(null)} 
        />
      </div>
    </div>
  );
};

export default GameDev;
