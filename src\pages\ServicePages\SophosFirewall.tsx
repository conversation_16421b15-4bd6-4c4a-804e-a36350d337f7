import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Shield, Lock, Cloud, Settings, Zap } from 'lucide-react';

const SophosFirewall = () => {
  const features = [
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Synchronized Security",
      description: "Integrates network and endpoint security for comprehensive protection"
    },
    {
      icon: <Lock className="text-purple-400" size={24} />,
      title: "Xstream Architecture",
      description: "Advanced threat protection with TLS inspection and deep packet analysis"
    },
    {
      icon: <Cloud className="text-purple-400" size={24} />,
      title: "Cloud Management",
      description: "Centralized cloud-based management through Sophos Central"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "SD-WAN Features",
      description: "Intelligent routing and VPN capabilities for optimized performance"
    }
  ];

  const pricing = [
    {
      name: "Essential",
      price: "Contact Us",
      features: [
        "Basic Firewall Protection",
        "Web Protection & Control",
        "Email Protection",
        "Basic VPN Support",
        "8x5 Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Threat Protection",
        "Synchronized Security",
        "Cloud Management",
        "SD-WAN Features",
        "24/7 Support",
        "Priority Response"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Security Policies",
        "Enterprise SD-WAN",
        "High Availability",
        "Custom Integration",
        "Dedicated Support Team",
        "Custom SLA"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Sophos Firewall Solutions"
      description="Next-generation firewall with synchronized security and cloud-native management"
      features={features}
      pricing={pricing}
      technologies={['Sophos XG', 'Xstream Architecture', 'Synchronized Security', 'SD-WAN', 'Zero Trust', 'VPN']}
      codeExample={`// Sophos Firewall Security Policy Configuration
class SophosFirewallPolicy {
  constructor() {
    this.securityRules = [];
    this.synchronized = false;
    this.xstreamEnabled = true;
  }

  addSecurityRule(rule) {
    const { source, destination, action, service } = rule;
    
    // Validate rule components
    this.validateRule(rule);
    
    // Add synchronized security tags if enabled
    if (this.synchronized) {
      rule.tags = this.getSynchronizedTags(source);
    }
    
    // Apply Xstream inspection if enabled
    if (this.xstreamEnabled) {
      rule.inspection = {
        tlsInspection: true,
        deepPacketAnalysis: true,
        threatProtection: true
      };
    }
    
    // Add rule to policy
    this.securityRules.push({
      id: this.generateRuleId(),
      ...rule,
      timestamp: new Date(),
      status: 'active'
    });
  }

  applySynchronizedSecurity() {
    this.synchronized = true;
    
    // Update existing rules with synchronized security
    this.securityRules.forEach(rule => {
      rule.tags = this.getSynchronizedTags(rule.source);
      rule.heartbeat = true;
    });
    
    return {
      status: 'synchronized',
      rulesUpdated: this.securityRules.length,
      timestamp: new Date()
    };
  }

  enableXstreamProtection() {
    return {
      tlsInspection: 'enabled',
      deepPacketAnalysis: 'enabled',
      performanceOptimization: 'active',
      threatProtection: {
        status: 'enabled',
        engines: ['ai', 'ml', 'signature']
      }
    };
  }
}`}
    />
  );
};

export default SophosFirewall;
