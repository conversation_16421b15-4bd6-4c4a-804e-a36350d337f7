<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Demos</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .game-card {
            background: #2a2a2a;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .game-card:hover {
            transform: translateY(-5px);
        }

        .game-preview {
            width: 100%;
            height: 200px;
            background: #333;
            position: relative;
        }

        .game-info {
            padding: 15px;
        }

        .game-title {
            margin: 0;
            font-size: 1.2em;
            color: #fff;
        }

        .game-description {
            color: #aaa;
            font-size: 0.9em;
            margin-top: 10px;
        }

        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            max-width: 800px;
            width: 90%;
        }

        .close-popup {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        canvas {
            background: #000;
            max-width: 100%;
            display: block;
        }

        #mario-game, #webgl-demo {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="game-grid">
            <div class="game-card" onclick="openGame('mario')">
                <div class="game-preview">
                    <canvas id="mario-preview"></canvas>
                </div>
                <div class="game-info">
                    <h3 class="game-title">Mario-like Platformer</h3>
                    <p class="game-description">A classic platformer game demo. Click to play!</p>
                </div>
            </div>

            <div class="game-card" onclick="openGame('webgl')">
                <div class="game-preview">
                    <canvas id="webgl-preview"></canvas>
                </div>
                <div class="game-info">
                    <h3 class="game-title">WebGL Squares</h3>
                    <p class="game-description">Interactive WebGL demo with moving squares. Click to play!</p>
                </div>
            </div>
        </div>
    </div>

    <div id="game-popup" class="popup-overlay">
        <div class="popup-content">
            <button class="close-popup" onclick="closePopup()">&times;</button>
            <canvas id="game-canvas"></canvas>
        </div>
    </div>

    <script src="mario-game.js"></script>
    <script src="webgl-demo.js"></script>
    <script src="popup-handler.js"></script>
</body>
</html>
