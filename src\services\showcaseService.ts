import { Project, User, Site } from '../types/showcase';

const API_URL = `${import.meta.env.VITE_API_HOSTNAME || 'http://localhost:3001'}/api/showcase`;

// Auth state
let currentUser: User | null = null;
let authToken: string | null = null;

// Login function
export const login = async (email: string, password: string): Promise<User> => {
  console.log('Attempting login with:', { email, password });
  
  try {
    const response = await fetch(`${API_URL}/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    console.log('Login response status:', response.status);
    
    if (!response.ok) {
      const error = await response.json();
      console.error('Login error:', error);
      throw new Error(error.error || 'Login failed');
    }

    const data = await response.json();
    console.log('Login successful, received data:', data);
    
    if (!data.user) {
      console.error('No user data received');
      throw new Error('Invalid response from server');
    }
    
    currentUser = data.user;
    authToken = 'dummy-token'; // In a real app, this would come from the server
    
    // Store in localStorage for persistence
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    localStorage.setItem('authToken', authToken);
    
    return data.user;
  } catch (err) {
    console.error('Login exception:', err);
    throw err;
  }
};

// Logout function
export const logout = () => {
  console.log('Logging out user');
  currentUser = null;
  authToken = null;
  localStorage.removeItem('currentUser');
  localStorage.removeItem('authToken');
  console.log('User logged out successfully');
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  // Try to load from localStorage
  const storedUser = localStorage.getItem('currentUser');
  const storedToken = localStorage.getItem('authToken');
  
  if (storedUser && storedToken) {
    try {
      currentUser = JSON.parse(storedUser);
      authToken = storedToken;
      return true;
    } catch (e) {
      console.error('Failed to parse stored user data', e);
      localStorage.removeItem('currentUser');
      localStorage.removeItem('authToken');
      return false;
    }
  }
  
  return false;
};

// Get current user
export const getCurrentUser = (): User | null => {
  // Try to load from localStorage
  if (!currentUser) {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        currentUser = JSON.parse(storedUser);
      } catch (e) {
        console.error('Failed to parse stored user data', e);
        return null;
      }
    }
  }
  
  return currentUser;
};

// Get all projects
export const getProjects = async (): Promise<Project[]> => {
  const response = await fetch(`${API_URL}/projects`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch projects');
  }
  
  return response.json();
};

// Get a single project
export const getProject = async (id: number): Promise<Project> => {
  const response = await fetch(`${API_URL}/projects/${id}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch project');
  }
  
  return response.json();
};

// Create a new project
export const createProject = async (project: Project): Promise<{ id: number }> => {
  const response = await fetch(`${API_URL}/projects`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ...project,
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create project');
  }
  
  return response.json();
};

// Update a project
export const updateProject = async (id: number, project: Project): Promise<void> => {
  const response = await fetch(`${API_URL}/projects/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ...project,
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update project');
  }
};

// Delete a project
export const deleteProject = async (id: number): Promise<void> => {
  const response = await fetch(`${API_URL}/projects/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete project');
  }
};

// ===== SITES FUNCTIONS =====

// Get all sites
export const getSites = async (): Promise<Site[]> => {
  const response = await fetch(`${API_URL}/sites`);

  if (!response.ok) {
    throw new Error('Failed to fetch sites');
  }

  return response.json();
};

// Get a single site
export const getSite = async (id: number): Promise<Site> => {
  const response = await fetch(`${API_URL}/sites/${id}`);

  if (!response.ok) {
    throw new Error('Failed to fetch site');
  }

  return response.json();
};

// Create a new site
export const createSite = async (site: Site): Promise<{ id: number }> => {
  const response = await fetch(`${API_URL}/sites`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ...site,
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create site');
  }

  return response.json();
};

// Update a site
export const updateSite = async (id: number, site: Site): Promise<void> => {
  const response = await fetch(`${API_URL}/sites/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ...site,
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update site');
  }
};

// Delete a site
export const deleteSite = async (id: number): Promise<void> => {
  const response = await fetch(`${API_URL}/sites/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: currentUser?.email,
      password: 'Element@qaz' // This is not secure, but for demo purposes
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete site');
  }
};
