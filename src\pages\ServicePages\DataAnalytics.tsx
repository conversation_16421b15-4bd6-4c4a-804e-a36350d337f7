import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tren<PERSON>Up } from 'lucide-react';

const DataAnalytics = () => {
  const features = [
    {
      icon: <Bar<PERSON>hart className="text-purple-400" size={24} />,
      title: "Data Visualization",
      description: "Interactive dashboards and visual analytics"
    },
    {
      icon: <LineChart className="text-purple-400" size={24} />,
      title: "Predictive Analytics",
      description: "Advanced forecasting and trend analysis"
    },
    {
      icon: <PieChart className="text-purple-400" size={24} />,
      title: "Business Intelligence",
      description: "Comprehensive BI solutions and reporting"
    },
    {
      icon: <TrendingUp className="text-purple-400" size={24} />,
      title: "Performance Metrics",
      description: "KPI tracking and performance analytics"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Basic Dashboard",
        "Standard Reports",
        "Data Integration",
        "Email Support",
        "Monthly Updates"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Dashboard",
        "Custom Reports",
        "Predictive Analytics",
        "Real-time Updates",
        "Priority Support",
        "API Integration"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Analytics Solution",
        "AI/ML Integration",
        "Big Data Processing",
        "Custom Integration",
        "Dedicated Support",
        "Training & Workshops"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Data Analytics & BI"
      description="Transform your data into actionable insights with advanced analytics"
      features={features}
      pricing={pricing}
      technologies={['Python', 'R', 'Tableau', 'Power BI', 'TensorFlow', 'Pandas']}
      codeExample={`# Data Analysis with Python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression

# Load and prepare data
def prepare_data(data_path):
    df = pd.read_csv(data_path)
    
    # Data cleaning
    df = df.dropna()
    df = df.drop_duplicates()
    
    # Feature engineering
    df['year'] = pd.to_datetime(df['date']).dt.year
    df['month'] = pd.to_datetime(df['date']).dt.month
    
    return df

# Train prediction model
def train_model(X, y):
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    return model, X_test, y_test

# Generate insights
def generate_insights(df):
    insights = {
        'total_revenue': df['revenue'].sum(),
        'avg_monthly_growth': df.groupby('month')['growth'].mean(),
        'top_performers': df.nlargest(5, 'performance')
    }
    
    return insights`}
    />
  );
};

export default DataAnalytics;
