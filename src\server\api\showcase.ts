import express from 'express';
import { query, queryOne, queryAll } from '../database-postgres';
import { Project, ProjectFeature } from '../../types/showcase';

const router = express.Router();

// Middleware to authenticate admin
const authenticateAdmin = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    const user = await queryOne(
      'SELECT * FROM users WHERE email = $1 AND password = $2 AND is_admin = $3',
      [email, password, true]
    );

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ error: 'Authentication failed' });
  }
};

// Get all projects with their features
router.get('/projects', async (req: express.Request, res: express.Response) => {
  try {
    const projects = await queryAll(`
      SELECT
        p.id, p.name, p.url, p.description, p.category, p.created_at as "createdAt", p.updated_at as "updatedAt",
        json_agg(
          CASE
            WHEN f.id IS NOT NULL THEN
              json_build_object(
                'id', f.id,
                'feature', f.feature,
                'isHighlighted', f.is_highlighted
              )
            ELSE NULL
          END
        ) as features
      FROM projects p
      LEFT JOIN project_features f ON p.id = f.project_id
      GROUP BY p.id, p.name, p.url, p.description, p.category, p.created_at, p.updated_at
      ORDER BY p.updated_at DESC
    `);

    // Filter out null features
    const formattedProjects = projects.map((project: any) => ({
      ...project,
      features: project.features.filter((f: any) => f !== null)
    }));

    res.json(formattedProjects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ error: 'Failed to fetch projects' });
  }
});

// Get a single project by ID
router.get('/projects/:id', async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;

    const project = await queryOne(`
      SELECT
        p.id, p.name, p.url, p.description, p.category, p.created_at as "createdAt", p.updated_at as "updatedAt"
      FROM projects p
      WHERE p.id = $1
    `, [id]);

    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    const features = await queryAll(`
      SELECT id, feature, is_highlighted as "isHighlighted"
      FROM project_features
      WHERE project_id = $1
    `, [id]);

    res.json({
      ...project,
      features
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ error: 'Failed to fetch project' });
  }
});

// Create a new project
router.post('/projects', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { name, description, url, category, features } = req.body as {
      name: string;
      description: string;
      url: string;
      category: string;
      features: Array<{
        feature: string;
        isHighlighted: boolean;
      }>;
    };

    if (!name || !url) {
      return res.status(400).json({ error: 'Name and URL are required' });
    }

    // Insert project
    const result = await query(
      'INSERT INTO projects (name, description, url, category) VALUES ($1, $2, $3, $4) RETURNING id',
      [name, description, url, category]
    );

    const projectId = result.rows[0].id;

    // Insert features
    if (features && Array.isArray(features)) {
      for (const feature of features) {
        await query(
          'INSERT INTO project_features (project_id, feature, is_highlighted) VALUES ($1, $2, $3)',
          [projectId, feature.feature, feature.isHighlighted]
        );
      }
    }

    res.status(201).json({ id: projectId });
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

// Update a project
router.put('/projects/:id', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;
    const { name, description, url, category, features } = req.body as {
      name: string;
      description: string;
      url: string;
      category: string;
      features: Array<{
        id?: number;
        feature: string;
        isHighlighted: boolean;
      }>;
    };

    if (!name || !url) {
      return res.status(400).json({ error: 'Name and URL are required' });
    }

    // Check if project exists
    const existingProject = await queryOne('SELECT * FROM projects WHERE id = $1', [id]);

    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Update project
    await query(
      'UPDATE projects SET name = $1, description = $2, url = $3, category = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $5',
      [name, description, url, category, id]
    );

    // Delete existing features
    await query('DELETE FROM project_features WHERE project_id = $1', [id]);

    // Insert new features
    if (features && Array.isArray(features)) {
      for (const feature of features) {
        await query(
          'INSERT INTO project_features (project_id, feature, is_highlighted) VALUES ($1, $2, $3)',
          [id, feature.feature, feature.isHighlighted]
        );
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

// Delete a project
router.delete('/projects/:id', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;

    const project = await queryOne('SELECT id FROM projects WHERE id = $1', [id]);

    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Delete project (features will be deleted due to CASCADE constraint)
    await query('DELETE FROM projects WHERE id = $1', [id]);

    res.json({ message: 'Project deleted successfully' });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ error: 'Failed to delete project' });
  }
});

// Authenticate admin
router.post('/auth', async (req: express.Request, res: express.Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // For debugging
    console.log('Login attempt:', { email, password });

    // Use a direct query to find the user
    const user = await queryOne('SELECT * FROM users WHERE email = $1 AND password = $2', [email, password]);

    if (!user) {
      console.log('Authentication failed: Invalid credentials');
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    console.log('Authentication successful:', user);

    // Return the user data
    res.json({
      user: {
        id: user.id,
        email: user.email,
        isAdmin: Boolean(user.is_admin)
      }
    });
  } catch (error) {
    console.error('Error authenticating:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// ===== SITES ENDPOINTS =====

// Get all sites
router.get('/sites', async (req: express.Request, res: express.Response) => {
  try {
    const sites = await queryAll(`
      SELECT id, name, url, description, category, featured, created_at as "createdAt"
      FROM sites
      ORDER BY created_at DESC
    `);

    res.json(sites);
  } catch (error) {
    console.error('Error fetching sites:', error);
    res.status(500).json({ error: 'Failed to fetch sites' });
  }
});

// Get a single site by ID
router.get('/sites/:id', async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;

    const site = await queryOne(`
      SELECT id, name, url, description, category, featured, created_at as "createdAt"
      FROM sites
      WHERE id = $1
    `, [id]);

    if (!site) {
      return res.status(404).json({ error: 'Site not found' });
    }

    res.json(site);
  } catch (error) {
    console.error('Error fetching site:', error);
    res.status(500).json({ error: 'Failed to fetch site' });
  }
});

// Create a new site
router.post('/sites', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { name, description, url, category, featured } = req.body as {
      name: string;
      description: string;
      url: string;
      category: string;
      featured: boolean;
    };

    if (!name || !url) {
      return res.status(400).json({ error: 'Name and URL are required' });
    }

    // Insert site
    const result = await query(
      'INSERT INTO sites (name, description, url, category, featured) VALUES ($1, $2, $3, $4, $5) RETURNING id',
      [name, description, url, category, featured || false]
    );

    const siteId = result.rows[0].id;

    res.status(201).json({ id: siteId });
  } catch (error) {
    console.error('Error creating site:', error);
    res.status(500).json({ error: 'Failed to create site' });
  }
});

// Update a site
router.put('/sites/:id', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;
    const { name, description, url, category, featured } = req.body as {
      name: string;
      description: string;
      url: string;
      category: string;
      featured: boolean;
    };

    if (!name || !url) {
      return res.status(400).json({ error: 'Name and URL are required' });
    }

    // Check if site exists
    const existingSite = await queryOne('SELECT * FROM sites WHERE id = $1', [id]);

    if (!existingSite) {
      return res.status(404).json({ error: 'Site not found' });
    }

    // Update site
    await query(
      'UPDATE sites SET name = $1, description = $2, url = $3, category = $4, featured = $5 WHERE id = $6',
      [name, description, url, category, featured || false, id]
    );

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating site:', error);
    res.status(500).json({ error: 'Failed to update site' });
  }
});

// Delete a site
router.delete('/sites/:id', authenticateAdmin, async (req: express.Request, res: express.Response) => {
  try {
    const { id } = req.params;

    const site = await queryOne('SELECT id FROM sites WHERE id = $1', [id]);

    if (!site) {
      return res.status(404).json({ error: 'Site not found' });
    }

    // Delete site
    await query('DELETE FROM sites WHERE id = $1', [id]);

    res.json({ message: 'Site deleted successfully' });
  } catch (error) {
    console.error('Error deleting site:', error);
    res.status(500).json({ error: 'Failed to delete site' });
  }
});

export default router;
