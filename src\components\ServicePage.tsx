import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, Check, Terminal, Shield, Zap, Server, Code, Cpu, GitBranch,
  Lock, Brain, Database, Cloud, Network, Monitor, Workflow, Settings, Globe,
  Key, LineChart, HardDrive, Webhook, BarChart, Bell, <PERSON>ert<PERSON>gle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import ContactForm from './ContactForm';

interface PricingTier {
  name: string;
  price: string;
  features: string[];
  recommended?: boolean;
}

interface ServicePageProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  pricing: PricingTier[];
  slug: string;
}

const ServicePage: React.FC<ServicePageProps> = ({ title, description, icon, pricing = [], slug }) => {
  // Get service-specific features
  const getServiceFeatures = () => {
    switch(slug) {
      case 'code-generation':
        return {
          title: "AI-Powered Code Generation",
          description: "Execute AI-generated code securely in your applications",
          features: [
            {
              icon: <Terminal size={24} className="text-purple-400" />,
              title: "Secure Execution",
              description: "Run untrusted code in isolated sandboxes with resource limits and security policies"
            },
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Language Support",
              description: "Support for multiple programming languages including Python, Node.js, and more"
            },
            {
              icon: <Zap size={24} className="text-purple-400" />,
              title: "Real-time Processing",
              description: "Execute code in real-time with low latency and high performance"
            },
            {
              icon: <Server size={24} className="text-purple-400" />,
              title: "Scalable Infrastructure",
              description: "Automatically scale resources based on your needs"
            }
          ],
          codeExample: `// Initialize E2B sandbox
const sandbox = await e2b.Sandbox.create()

// Execute code securely
const result = await sandbox.run(\`
  def calculate(x, y):
      return x + y
  
  result = calculate(5, 3)
  print(result)
\`)

// Clean up resources
await sandbox.close()`
        };

      case 'secure-sandbox':
        return {
          title: "Secure Code Execution Environment",
          description: "Run untrusted code safely in isolated containers",
          features: [
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Isolation",
              description: "Complete isolation between processes with containerization"
            },
            {
              icon: <Terminal size={24} className="text-purple-400" />,
              title: "Resource Control",
              description: "Fine-grained control over CPU, memory, and network usage"
            },
            {
              icon: <Zap size={24} className="text-purple-400" />,
              title: "Fast Startup",
              description: "Quick sandbox initialization with pre-warmed containers"
            },
            {
              icon: <Server size={24} className="text-purple-400" />,
              title: "Monitoring",
              description: "Real-time monitoring of sandbox performance and status"
            }
          ],
          codeExample: `// Create a secure sandbox
const sandbox = new E2BSandbox({
  timeout: 30000,
  memory: "512MB",
  cpu: 1
});

// Execute untrusted code
const result = await sandbox.execute(userCode, {
  permissions: ["filesystem:read"],
  env: { API_KEY: process.env.API_KEY }
});`
        };

      case 'cloud-computing':
        return {
          title: "Cloud Computing Solutions",
          description: "Scalable and flexible cloud computing resources",
          features: [
            {
              icon: <Cpu size={24} className="text-purple-400" />,
              title: "Auto-scaling",
              description: "Automatically adjust resources based on demand"
            },
            {
              icon: <Globe size={24} className="text-purple-400" />,
              title: "Global Distribution",
              description: "Deploy across multiple regions for optimal performance"
            },
            {
              icon: <Settings size={24} className="text-purple-400" />,
              title: "Resource Management",
              description: "Efficient allocation and monitoring of computing resources"
            },
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Security",
              description: "Enterprise-grade security for cloud resources"
            }
          ],
          codeExample: `// Initialize cloud resources
const cloud = new E2BCloud({
  region: 'us-east-1',
  resources: {
    cpu: 'auto',
    memory: 'auto'
  }
});

// Deploy application
await cloud.deploy({
  app: './my-app',
  scaling: {
    min: 1,
    max: 10,
    target: 'cpu-utilization'
  }
});`
        };

      case 'version-control':
        return {
          title: "Advanced Version Control",
          description: "Comprehensive version control and collaboration tools",
          features: [
            {
              icon: <GitBranch size={24} className="text-purple-400" />,
              title: "Branch Management",
              description: "Advanced branching strategies and workflows"
            },
            {
              icon: <Workflow size={24} className="text-purple-400" />,
              title: "CI/CD Integration",
              description: "Seamless integration with CI/CD pipelines"
            },
            {
              icon: <Lock size={24} className="text-purple-400" />,
              title: "Access Control",
              description: "Fine-grained permissions and code review processes"
            },
            {
              icon: <Terminal size={24} className="text-purple-400" />,
              title: "Command Line Tools",
              description: "Powerful CLI tools for version control operations"
            }
          ],
          codeExample: `// Initialize version control
const repo = await e2b.Repository.create({
  name: 'my-project',
  type: 'git'
});

// Create branch and commit changes
await repo.branch('feature/new-api');
await repo.commit({
  message: 'Add new API endpoint',
  files: ['api.js', 'tests/'],
  reviewers: ['<EMAIL>']
});`
        };

      case 'security':
        return {
          title: "Enterprise Security Solutions",
          description: "Comprehensive security and compliance features",
          features: [
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Threat Detection",
              description: "Advanced threat detection and prevention"
            },
            {
              icon: <Lock size={24} className="text-purple-400" />,
              title: "Access Control",
              description: "Role-based access control and authentication"
            },
            {
              icon: <Key size={24} className="text-purple-400" />,
              title: "Encryption",
              description: "End-to-end encryption for data protection"
            },
            {
              icon: <AlertTriangle size={24} className="text-purple-400" />,
              title: "Audit Logging",
              description: "Comprehensive audit trails and compliance reporting"
            }
          ],
          codeExample: `// Configure security settings
const security = new E2BSecurity({
  encryption: 'AES-256',
  audit: true,
  compliance: ['SOC2', 'GDPR']
});

// Monitor security events
security.on('threat', async (event) => {
  await security.mitigate(event);
  await notifySecurityTeam(event);
});`
        };

      case 'access-control':
        return {
          title: "Access Control Management",
          description: "Fine-grained access control and user management",
          features: [
            {
              icon: <Lock size={24} className="text-purple-400" />,
              title: "Role Management",
              description: "Define and manage user roles and permissions"
            },
            {
              icon: <Key size={24} className="text-purple-400" />,
              title: "Authentication",
              description: "Multi-factor authentication and SSO integration"
            },
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Policy Enforcement",
              description: "Implement and enforce access policies"
            },
            {
              icon: <LineChart size={24} className="text-purple-400" />,
              title: "Access Analytics",
              description: "Monitor and analyze access patterns"
            }
          ],
          codeExample: `// Define access control policies
const access = new E2BAccess({
  roles: {
    admin: ['read', 'write', 'delete'],
    user: ['read', 'write'],
    guest: ['read']
  }
});

// Check permissions
const canAccess = await access.check({
  user: currentUser,
  resource: 'documents/secret.pdf',
  action: 'read'
});`
        };

      case 'ai-integration':
        return {
          title: "AI Model Integration",
          description: "Seamless integration with AI and ML models",
          features: [
            {
              icon: <Brain size={24} className="text-purple-400" />,
              title: "Model Management",
              description: "Deploy and manage AI models efficiently"
            },
            {
              icon: <Zap size={24} className="text-purple-400" />,
              title: "Real-time Inference",
              description: "Fast and reliable model inference"
            },
            {
              icon: <Settings size={24} className="text-purple-400" />,
              title: "Model Optimization",
              description: "Optimize models for production deployment"
            },
            {
              icon: <LineChart size={24} className="text-purple-400" />,
              title: "Performance Monitoring",
              description: "Monitor model performance and accuracy"
            }
          ],
          codeExample: `// Initialize AI integration
const ai = new E2BAI({
  models: ['gpt-4', 'stable-diffusion'],
  optimization: true
});

// Run inference
const result = await ai.predict({
  model: 'gpt-4',
  input: 'Generate a React component',
  parameters: {
    temperature: 0.7,
    maxTokens: 500
  }
});`
        };

      case 'real-time-processing':
        return {
          title: "Real-time Data Processing",
          description: "High-performance real-time data processing",
          features: [
            {
              icon: <Zap size={24} className="text-purple-400" />,
              title: "Stream Processing",
              description: "Process data streams in real-time"
            },
            {
              icon: <Server size={24} className="text-purple-400" />,
              title: "Event Handling",
              description: "Handle and process events instantly"
            },
            {
              icon: <Settings size={24} className="text-purple-400" />,
              title: "Pipeline Management",
              description: "Create and manage processing pipelines"
            },
            {
              icon: <LineChart size={24} className="text-purple-400" />,
              title: "Performance Analytics",
              description: "Monitor processing performance"
            }
          ],
          codeExample: `// Set up real-time processing
const processor = new E2BProcessor({
  capacity: '10k-events/sec',
  scaling: 'auto'
});

// Process data stream
processor.stream('user-events')
  .filter(event => event.type === 'purchase')
  .transform(event => ({
    ...event,
    timestamp: Date.now()
  }))
  .store('purchases');`
        };

      case 'data-management':
        return {
          title: "Data Management Solutions",
          description: "Efficient data storage and management",
          features: [
            {
              icon: <Database size={24} className="text-purple-400" />,
              title: "Data Storage",
              description: "Scalable and secure data storage solutions"
            },
            {
              icon: <HardDrive size={24} className="text-purple-400" />,
              title: "Backup & Recovery",
              description: "Automated backup and disaster recovery"
            },
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Data Security",
              description: "Secure data storage and transmission"
            },
            {
              icon: <Settings size={24} className="text-purple-400" />,
              title: "Data Optimization",
              description: "Optimize data storage and retrieval"
            }
          ],
          codeExample: `// Initialize data management
const data = new E2BData({
  storage: 'distributed',
  backup: {
    frequency: 'daily',
    retention: '30-days'
  }
});

// Store and retrieve data
await data.store('users', {
  id: 'user123',
  data: userData,
  encryption: true
});

const user = await data.get('users', 'user123');`
        };

      case 'cloud-storage':
        return {
          title: "Cloud Storage Solutions",
          description: "Secure and scalable cloud storage",
          features: [
            {
              icon: <Cloud size={24} className="text-purple-400" />,
              title: "File Storage",
              description: "Secure cloud-based file storage"
            },
            {
              icon: <Globe size={24} className="text-purple-400" />,
              title: "Global CDN",
              description: "Content delivery network integration"
            },
            {
              icon: <Lock size={24} className="text-purple-400" />,
              title: "Access Control",
              description: "Granular file access permissions"
            },
            {
              icon: <Settings size={24} className="text-purple-400" />,
              title: "Storage Management",
              description: "Efficient storage allocation and monitoring"
            }
          ],
          codeExample: `// Initialize cloud storage
const storage = new E2BStorage({
  region: 'global',
  redundancy: true
});

// Upload and manage files
await storage.upload('documents/report.pdf', fileBuffer, {
  access: 'private',
  encryption: true
});

const url = await storage.getSignedUrl('documents/report.pdf', {
  expires: '24h'
});`
        };

      case 'api-integration':
        return {
          title: "API Integration Services",
          description: "Seamless API integration and management",
          features: [
            {
              icon: <Webhook size={24} className="text-purple-400" />,
              title: "API Gateway",
              description: "Centralized API management and routing"
            },
            {
              icon: <Shield size={24} className="text-purple-400" />,
              title: "Security",
              description: "API authentication and authorization"
            },
            {
              icon: <Network size={24} className="text-purple-400" />,
              title: "Rate Limiting",
              description: "Traffic control and rate limiting"
            },
            {
              icon: <LineChart size={24} className="text-purple-400" />,
              title: "Analytics",
              description: "API usage analytics and monitoring"
            }
          ],
          codeExample: `// Set up API integration
const api = new E2BAPI({
  gateway: true,
  security: {
    auth: ['JWT', 'API-Key'],
    rateLimit: '1000/hour'
  }
});

// Create API endpoint
api.route('/data', {
  method: 'POST',
  handler: async (req) => {
    const result = await processData(req.body);
    return { status: 'success', data: result };
  }
});`
        };

      case 'monitoring':
        return {
          title: "System Monitoring",
          description: "Comprehensive system monitoring and analytics",
          features: [
            {
              icon: <Monitor size={24} className="text-purple-400" />,
              title: "Real-time Monitoring",
              description: "Monitor system metrics in real-time"
            },
            {
              icon: <BarChart size={24} className="text-purple-400" />,
              title: "Analytics",
              description: "Advanced analytics and reporting"
            },
            {
              icon: <Bell size={24} className="text-purple-400" />,
              title: "Alerts",
              description: "Customizable alerting system"
            },
            {
              icon: <LineChart size={24} className="text-purple-400" />,
              title: "Performance Tracking",
              description: "Track system performance metrics"
            }
          ],
          codeExample: `// Initialize monitoring
const monitor = new E2BMonitor({
  metrics: ['cpu', 'memory', 'network'],
  interval: '1m'
});

// Set up alerts
monitor.alert('high-cpu', {
  condition: 'cpu > 80%',
  duration: '5m',
  actions: ['notify-team', 'scale-up']
});

// Get performance report
const report = await monitor.generateReport({
  timeframe: 'last-24h',
  metrics: ['all']
});`
        };

      default:
        return {
          title,
          description,
          features: [],
          codeExample: ""
        };
    }
  };

  const serviceDetails = getServiceFeatures();

  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-20"
        >
          <div className="w-16 h-16 mx-auto bg-white/10 rounded-lg flex items-center justify-center mb-6">
            {icon}
          </div>
          <h1 className="text-4xl md:text-5xl font-mono font-bold mb-6 glitch-text">
            {serviceDetails.title}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto font-mono">
            {serviceDetails.description}
          </p>
        </motion.div>

        {/* Features Grid */}
        {serviceDetails.features.length > 0 && (
          <div className="grid md:grid-cols-2 gap-8 mb-20">
            {serviceDetails.features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg"
              >
                <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-mono font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-400 font-mono">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        )}

        {/* Code Example */}
        {serviceDetails.codeExample && (
          <div className="mb-20">
            <div className="terminal-window">
              <div className="terminal-header">
                <div className="terminal-dot bg-red-500"></div>
                <div className="terminal-dot bg-yellow-500"></div>
                <div className="terminal-dot bg-green-500"></div>
                <span className="text-xs ml-2 font-mono">example.js</span>
              </div>
              <div className="p-6">
                <pre className="text-gray-300 font-mono text-sm">
                  {serviceDetails.codeExample}
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* Pricing Tiers */}
        {pricing.length > 0 && (
          <div className="grid md:grid-cols-3 gap-8 mb-20">
            {pricing.map((tier, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`terminal-window ${
                  tier.recommended ? 'border-purple-500/50' : ''
                }`}
              >
                <div className="terminal-header">
                  <div className="terminal-dot bg-red-500"></div>
                  <div className="terminal-dot bg-yellow-500"></div>
                  <div className="terminal-dot bg-green-500"></div>
                  <span className="text-xs ml-2 font-mono">{tier.name.toUpperCase()}</span>
                </div>
                <div className="p-6">
                  <div className="text-3xl font-mono mb-6">{tier.price}</div>
                  <ul className="space-y-4">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-start gap-3 font-mono">
                        <Check className="text-green-400 mt-1 flex-shrink-0" size={16} />
                        <span className="text-gray-400">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button className="w-full mt-8 bg-white text-black font-mono py-2 rounded hover:bg-white/90 transition-colors">
                    GET STARTED
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
      <ContactForm />
    </div>
  );
};

export default ServicePage;