import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Check } from 'lucide-react';
import { Link } from 'react-router-dom';
import ContactForm from '../components/ContactForm';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface PricingTier {
  name: string;
  price: string;
  features: string[];
  recommended?: boolean;
}

interface ServiceLayoutProps {
  title: string;
  description: string;
  features: Feature[];
  pricing: PricingTier[];
  technologies: string[];
  codeExample: string;
}

const ServiceLayout: React.FC<ServiceLayoutProps> = ({
  title,
  description,
  features,
  pricing,
  technologies,
  codeExample
}) => {
  return (
    <div className="pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link
          to="/"
          className="inline-flex items-center text-gray-400 hover:text-white font-mono group mb-12"
        >
          <ArrowLeft className="mr-2 w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Back to Home
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-20"
        >
          <h1 className="text-4xl md:text-5xl font-mono font-bold mb-6 glitch-text">
            {title}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto font-mono">
            {description}
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg"
            >
              <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-mono font-semibold mb-2">{feature.title}</h3>
              <p className="text-gray-400 font-mono">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Technologies */}
        <div className="mb-20">
          <h2 className="text-2xl font-mono font-bold mb-6 text-center">Technologies We Use</h2>
          <div className="flex flex-wrap justify-center gap-4">
            {technologies.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="px-4 py-2 bg-white/5 rounded-full border border-white/10 text-sm font-mono"
              >
                {tech}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Code Example */}
        <div className="mb-20">
          <div className="terminal-window">
            <div className="terminal-header">
              <div className="terminal-dot bg-red-500"></div>
              <div className="terminal-dot bg-yellow-500"></div>
              <div className="terminal-dot bg-green-500"></div>
              <span className="text-xs ml-2 font-mono">example.js</span>
            </div>
            <div className="p-6">
              <pre className="text-gray-300 font-mono text-sm overflow-x-auto">
                {codeExample}
              </pre>
            </div>
          </div>
        </div>

        {/* Pricing Tiers */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {pricing.map((tier, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`terminal-window ${
                tier.recommended ? 'border-purple-500/50' : ''
              }`}
            >
              <div className="terminal-header">
                <div className="terminal-dot bg-red-500"></div>
                <div className="terminal-dot bg-yellow-500"></div>
                <div className="terminal-dot bg-green-500"></div>
                <span className="text-xs ml-2 font-mono">{tier.name.toUpperCase()}</span>
              </div>
              <div className="p-6">
                <div className="text-3xl font-mono mb-6">{tier.price}</div>
                <ul className="space-y-4">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-3 font-mono">
                      <Check className="text-green-400 mt-1 flex-shrink-0" size={16} />
                      <span className="text-gray-400">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link to="/contact" className="block w-full mt-8">
                  <button className="w-full bg-white text-black font-mono py-2 rounded hover:bg-white/90 transition-colors">
                    GET STARTED
                  </button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
      <ContactForm />
    </div>
  );
};

export default ServiceLayout;
