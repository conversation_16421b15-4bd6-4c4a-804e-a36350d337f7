import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Code, Smartphone, Globe, Database } from 'lucide-react';

const CustomSoftware = () => {
  const features = [
    {
      icon: <Code className="text-purple-400" size={24} />,
      title: "Full-Stack Development",
      description: "End-to-end web and mobile application development"
    },
    {
      icon: <Smartphone className="text-purple-400" size={24} />,
      title: "Mobile Apps",
      description: "Native and cross-platform mobile applications"
    },
    {
      icon: <Globe className="text-purple-400" size={24} />,
      title: "Web Applications",
      description: "Modern and responsive web applications"
    },
    {
      icon: <Database className="text-purple-400" size={24} />,
      title: "API Development",
      description: "RESTful and GraphQL API development"
    }
  ];

  const pricing = [
    {
      name: "Starter",
      price: "Contact Us",
      features: [
        "Basic Web Application",
        "Essential Features",
        "Standard UI/UX",
        "Basic API Integration",
        "3 Months Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Full-Featured Application",
        "Advanced Features",
        "Premium UI/UX",
        "Complete API Suite",
        "Mobile App Integration",
        "12 Months Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Enterprise Solution",
        "Unlimited Features",
        "Custom Integration",
        "High Scalability",
        "Dedicated Team",
        "24/7 Premium Support"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Custom Software Development"
      description="Tailored software solutions to meet your unique business requirements"
      features={features}
      pricing={pricing}
      technologies={['React', 'Node.js', 'Python', 'React Native', 'AWS', 'MongoDB']}
      codeExample={`// Modern React Component Example
import { useState, useEffect } from 'react';

const DataDashboard = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await api.getData();
        setData(response.data);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="dashboard">
      {loading ? (
        <LoadingSpinner />
      ) : (
        <DataGrid
          data={data}
          onSort={handleSort}
          onFilter={handleFilter}
        />
      )}
    </div>
  );
};`}
    />
  );
};

export default CustomSoftware;
