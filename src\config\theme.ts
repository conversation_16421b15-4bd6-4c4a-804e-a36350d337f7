export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: {
    primary: string;
    secondary: string;
  };
  button: {
    primary: {
      background: string;
      text: string;
      hover: string;
    };
    secondary: {
      background: string;
      text: string;
      border: string;
      hover: string;
    };
  };
  card: {
    background: string;
    border: string;
    hoverBorder: string;
  };
  icon: string;
  highlight: string;
}

export interface ThemeConfig {
  colors: ThemeColors;
  fontFamily: {
    primary: string;
    secondary: string;
  };
}

const themeConfig: ThemeConfig = {
  colors: {
    // Main theme colors
    primary: '#a855f7', // Purple-500
    secondary: '#ec4899', // Pink-500
    accent: '#f97316', // Orange-500
    background: '#000000', // Black
    
    // Text colors
    text: {
      primary: '#ffffff', // White
      secondary: '#9ca3af', // Gray-400
    },
    
    // Button styles
    button: {
      primary: {
        background: '#ffffff', // White
        text: '#000000', // Black
        hover: '#f3f4f6', // Gray-100
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(255, 255, 255, 0.2)', // White with opacity
        hover: 'rgba(255, 255, 255, 0.1)', // White with opacity
      },
    },
    
    // Card styles
    card: {
      background: 'rgba(255, 255, 255, 0.05)', // White with opacity
      border: 'rgba(255, 255, 255, 0.1)', // White with opacity
      hoverBorder: 'rgba(168, 85, 247, 0.5)', // Purple with opacity
    },
    
    // Icon color
    icon: '#a855f7', // Purple-500
    
    // Highlight color (for gradients, glows, etc.)
    highlight: '#a855f7', // Purple-500
  },
  
  // Font families
  fontFamily: {
    primary: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    secondary: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  },
};

export default themeConfig;
