import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import './statParticles.css';

interface StatParticle {
  id: number;
  x: number;
  y: number;
  value: string;
  size: number;
  speed: number;
  opacity: number;
  direction: { x: number; y: number };
}

const StatParticles: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<StatParticle[]>([]);
  const animationRef = useRef<number | null>(null);
  
  // List of tech stats that will randomly appear
  const statValues = [
    "99.9% Uptime",
    "24/7 Support",
    "50+ Clients",
    "100+ Projects",
    "5 Star Rating",
    "10+ Years",
    "15+ Industries",
    "30+ Experts",
    "Global Reach",
    "200+ Servers",
    "AI Powered",
    "Cloud Native",
    "DevOps Ready",
    "Secure by Design",
    "Real-time Data",
    "Scalable",
    "Resilient",
    "Agile",
    "ISO Certified",
    "Enterprise Grade"
  ];

  // Initialize particles
  useEffect(() => {
    if (!containerRef.current) return;
    
    // Create initial particles
    const initialParticles: StatParticle[] = [];
    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;
    
    // Create 10 random stat particles
    for (let i = 0; i < 10; i++) {
      initialParticles.push(createRandomParticle(i, containerWidth, containerHeight));
    }
    
    particlesRef.current = initialParticles;
    
    // Start animation loop
    startAnimationLoop();
    
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);
  
  // Create a random particle
  const createRandomParticle = (id: number, containerWidth: number, containerHeight: number): StatParticle => {
    // Random position within container
    const x = Math.random() * containerWidth;
    const y = Math.random() * containerHeight;
    
    // Random stat value
    const value = statValues[Math.floor(Math.random() * statValues.length)];
    
    // Random size between 6.6 and 9.9 (45% smaller than original 12-18 range)
    const size = (Math.floor(Math.random() * 6) + 12) * 0.55;
    
    // Random speed between 0.5 and 2
    const speed = Math.random() * 1.5 + 0.5;
    
    // Random opacity between 0.3 and 0.8
    const opacity = Math.random() * 0.5 + 0.3;
    
    // Random direction
    const direction = {
      x: (Math.random() - 0.5) * 2,
      y: (Math.random() - 0.5) * 2
    };
    
    return { id, x, y, value, size, speed, opacity, direction };
  };
  
  // Animation loop
  const startAnimationLoop = () => {
    if (!containerRef.current) return;
    
    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;
    
    const updateParticles = () => {
      particlesRef.current = particlesRef.current.map(particle => {
        // Update position based on direction and speed
        let newX = particle.x + particle.direction.x * particle.speed;
        let newY = particle.y + particle.direction.y * particle.speed;
        
        // Bounce off edges
        if (newX < 0 || newX > containerWidth) {
          particle.direction.x *= -1;
          newX = particle.x + particle.direction.x * particle.speed;
        }
        
        if (newY < 0 || newY > containerHeight) {
          particle.direction.y *= -1;
          newY = particle.y + particle.direction.y * particle.speed;
        }
        
        // Small random direction changes occasionally
        if (Math.random() < 0.02) {
          particle.direction.x += (Math.random() - 0.5) * 0.5;
          particle.direction.y += (Math.random() - 0.5) * 0.5;
          
          // Normalize direction vector
          const magnitude = Math.sqrt(
            particle.direction.x * particle.direction.x + 
            particle.direction.y * particle.direction.y
          );
          
          particle.direction.x /= magnitude;
          particle.direction.y /= magnitude;
        }
        
        // Replace particle with new one occasionally
        if (Math.random() < 0.002) {
          return createRandomParticle(particle.id, containerWidth, containerHeight);
        }
        
        return { ...particle, x: newX, y: newY };
      });
      
      // Force re-render
      forceUpdate();
      
      // Continue animation loop
      animationRef.current = requestAnimationFrame(updateParticles);
    };
    
    animationRef.current = requestAnimationFrame(updateParticles);
  };
  
  // Force re-render (since we're not using state for performance reasons)
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  
  return (
    <div 
      ref={containerRef} 
      className="stat-particles-container"
      // Using the CSS class instead of inline styles
    >
      {particlesRef.current.map(particle => (
        <motion.div
          key={particle.id}
          className="stat-particle"
          style={{
            left: particle.x,
            top: particle.y,
            fontSize: `${particle.size}px`,
            opacity: particle.opacity,
            color: 'var(--color-primary, #ff3250)'
          }}
          initial={{ scale: 0, rotate: -10 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.5 }}
        >
          {particle.value}
        </motion.div>
      ))}
    </div>
  );
};

export default StatParticles;
