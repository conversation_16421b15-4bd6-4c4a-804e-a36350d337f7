import React, { useState, useEffect, useRef } from 'react';
import { X, Send, Mic, MicOff, Volume2, VolumeX, Sparkles } from 'lucide-react';
import chatService from '../services/chatService';
import { ttsService } from '../services/ttsService';
import { cleanTextForTTS, cleanTextForDisplay } from '../utils/textUtils';
import { ChatCompletionMessageParam } from 'openai/resources';
import './FloatingAIAssistant.css';

// Speech Recognition interfaces
interface SpeechRecognitionEvent {
  results: {
    [key: number]: {
      [key: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onstart: (() => void) | null;
  onend: (() => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const FloatingAIAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI assistant. How can I help you with our IT solutions today?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechEnabled, setSpeechEnabled] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      
      if (recognitionRef.current) {
        recognitionRef.current.continuous = false;
        recognitionRef.current.interimResults = false;
        recognitionRef.current.lang = 'en-US';

        recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
          const transcript = event.results[0][0].transcript;
          setInputText(transcript);
          setIsListening(false);
        };

        recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
        };

        recognitionRef.current.onend = () => {
          setIsListening(false);
        };
      }
    }
  }, []);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      const conversationHistory: ChatCompletionMessageParam[] = [
        ...messages.map(msg => ({
          role: msg.isUser ? 'user' as const : 'assistant' as const,
          content: msg.text
        })),
        { role: 'user', content: inputText }
      ];

      const response = await chatService.sendMessage(conversationHistory);
      const cleanedResponseForDisplay = cleanTextForDisplay(response.message);
      const cleanedResponseForTTS = cleanTextForTTS(response.message);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: cleanedResponseForDisplay,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);

      // Speak the response if speech is enabled
      if (speechEnabled && cleanedResponseForTTS) {
        setIsSpeaking(true);
        
        try {
          await ttsService.speak(cleanedResponseForTTS);
        } catch (error) {
          console.error('TTS Error:', error);
        } finally {
          setIsSpeaking(false);
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I apologize, but I'm having trouble connecting right now. Please try again in a moment.",
        isUser: false,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleListening = () => {
    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
    } else {
      recognitionRef.current?.start();
      setIsListening(true);
    }
  };

  const toggleSpeech = () => {
    if (isSpeaking) {
      ttsService.stop();
      setIsSpeaking(false);
    }
    setSpeechEnabled(!speechEnabled);
  };

  const clearChat = () => {
    setMessages([{
      id: '1',
      text: "Hello! I'm your AI assistant. How can I help you with our IT solutions today?",
      isUser: false,
      timestamp: new Date()
    }]);
  };

  return (
    <div className="floating-ai-assistant">
      {/* Floating Avatar Trigger */}
      <div 
        className={`floating-avatar ${isOpen ? 'active' : ''} ${isSpeaking ? 'speaking' : ''} ${isListening ? 'listening' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="avatar-core">
          <div className="avatar-face">
            <div className="avatar-eyes">
              <div className="eye left-eye">
                <div className="eye-pupil" />
              </div>
              <div className="eye right-eye">
                <div className="eye-pupil" />
              </div>
            </div>
            <div className="avatar-mouth" />
          </div>
        </div>
        
        {/* Pulse rings */}
        <div className="pulse-ring" />
        <div className="pulse-ring delay-1" />
        <div className="pulse-ring delay-2" />
        
        {/* Status indicator */}
        <div className="status-dot" />
        
        {/* Sparkle effect */}
        <Sparkles className="sparkle-icon" />
      </div>

      {/* Chat Popup */}
      {isOpen && (
        <div className="chat-popup">
          {/* Header */}
          <div className="chat-header">
            <div className="header-info">
              <div className="ai-status">
                <div className="status-indicator" />
                <span>AI Assistant</span>
              </div>
              <div className="voice-status">
                {ttsService.isElevenLabsAvailable() ? (
                  <span className="premium">🎙️ Premium Voice</span>
                ) : (
                  <span className="standard">🔊 Standard Voice</span>
                )}
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="close-btn"
              aria-label="Close chat"
            >
              <X size={16} />
            </button>
          </div>

          {/* Messages */}
          <div className="messages-area">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.isUser ? 'user' : 'ai'}`}
              >
                <div className="message-content">
                  {message.text}
                </div>
                <div className="message-time">
                  {message.timestamp.toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="message ai loading">
                <div className="message-content">
                  <div className="typing-dots">
                    <div className="dot" />
                    <div className="dot" />
                    <div className="dot" />
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="input-area">
            <div className="input-row">
              <div className="voice-controls">
                <button
                  onClick={toggleListening}
                  className={`voice-btn ${isListening ? 'active' : ''}`}
                  disabled={isLoading}
                  aria-label={isListening ? "Stop listening" : "Start voice input"}
                >
                  {isListening ? <MicOff size={16} /> : <Mic size={16} />}
                </button>
                
                <button
                  onClick={toggleSpeech}
                  className={`voice-btn ${speechEnabled ? 'active' : ''}`}
                  aria-label={speechEnabled ? "Disable speech" : "Enable speech"}
                >
                  {speechEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />}
                </button>
              </div>

              <div className="input-wrapper">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={isListening ? "Listening..." : "Ask me anything..."}
                  disabled={isLoading || isListening}
                  className="message-input"
                />
                
                <button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || isLoading}
                  className="send-btn"
                  aria-label="Send message"
                >
                  <Send size={16} />
                </button>
              </div>
            </div>

            <button onClick={clearChat} className="clear-btn">
              Clear Chat
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingAIAssistant;
