import React, { useState } from 'react';
import { updateTheme } from '../utils/themeManager';
import themeConfig from '../themeConfig';

// Predefined themes
const themes = {
  royalRed: {
    primary: '#c41e3a', // Royal Red
    secondary: '#8b0000', // Dark Red
    accent: '#ffd700', // Gold
    icon: '#c41e3a', // Royal Red
    highlight: '#ffd700', // Gold
    button: {
      primary: {
        background: '#c41e3a', // Royal Red
        text: '#ffffff', // White
        hover: '#8b0000', // Dark Red
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(196, 30, 58, 0.5)', // Royal Red with opacity
        hover: 'rgba(196, 30, 58, 0.2)', // Royal Red with opacity
      },
    },
  },
  purple: {
    primary: '#a855f7', // Purple-500
    secondary: '#ec4899', // Pink-500
    accent: '#f97316', // Orange-500
    icon: '#a855f7', // Purple-500
    highlight: '#a855f7', // Purple-500
    button: {
      primary: {
        background: '#a855f7', // Purple-500
        text: '#ffffff', // White
        hover: '#9333ea', // Purple-600
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(168, 85, 247, 0.5)', // Purple with opacity
        hover: 'rgba(168, 85, 247, 0.2)', // Purple with opacity
      },
    },
  },
  blue: {
    primary: '#3b82f6', // Blue-500
    secondary: '#06b6d4', // Cyan-500
    accent: '#10b981', // Emerald-500
    icon: '#3b82f6', // Blue-500
    highlight: '#3b82f6', // Blue-500
    button: {
      primary: {
        background: '#3b82f6', // Blue-500
        text: '#ffffff', // White
        hover: '#2563eb', // Blue-600
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(59, 130, 246, 0.5)', // Blue with opacity
        hover: 'rgba(59, 130, 246, 0.2)', // Blue with opacity
      },
    },
  },
  green: {
    primary: '#22c55e', // Green-500
    secondary: '#84cc16', // Lime-500
    accent: '#eab308', // Yellow-500
    icon: '#22c55e', // Green-500
    highlight: '#22c55e', // Green-500
    button: {
      primary: {
        background: '#22c55e', // Green-500
        text: '#ffffff', // White
        hover: '#16a34a', // Green-600
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(34, 197, 94, 0.5)', // Green with opacity
        hover: 'rgba(34, 197, 94, 0.2)', // Green with opacity
      },
    },
  },
  red: {
    primary: '#ef4444', // Red-500
    secondary: '#f97316', // Orange-500
    accent: '#eab308', // Yellow-500
    icon: '#ef4444', // Red-500
    highlight: '#ef4444', // Red-500
    button: {
      primary: {
        background: '#ef4444', // Red-500
        text: '#ffffff', // White
        hover: '#dc2626', // Red-600
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(239, 68, 68, 0.5)', // Red with opacity
        hover: 'rgba(239, 68, 68, 0.2)', // Red with opacity
      },
    },
  },
};

const ThemeSwitcher: React.FC = () => {
  const [activeTheme, setActiveTheme] = useState('royalRed');

  const handleThemeChange = (themeName: keyof typeof themes) => {
    const selectedTheme = themes[themeName];
    updateTheme({
      colors: {
        ...themeConfig.colors,
        primary: selectedTheme.primary,
        secondary: selectedTheme.secondary,
        accent: selectedTheme.accent,
        icon: selectedTheme.icon,
        highlight: selectedTheme.highlight,
        button: {
          primary: {
            background: selectedTheme.button.primary.background,
            text: selectedTheme.button.primary.text,
            hover: selectedTheme.button.primary.hover,
          },
          secondary: {
            background: selectedTheme.button.secondary.background,
            text: selectedTheme.button.secondary.text,
            border: selectedTheme.button.secondary.border,
            hover: selectedTheme.button.secondary.hover,
          },
        },
      },
    });
    setActiveTheme(themeName);
  };

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 backdrop-blur-md p-3 rounded-lg border border-white/10">
      <div className="flex flex-col gap-2">
        <div className="text-xs font-mono mb-1" style={{ color: 'var(--color-text-secondary)' }}>
          THEME
        </div>
        <div className="flex gap-2">
          {Object.entries(themes).map(([name, colors]) => (
            <button
              key={name}
              onClick={() => handleThemeChange(name as keyof typeof themes)}
              className="w-6 h-6 rounded-full transition-all duration-200 flex items-center justify-center"
              style={{
                backgroundColor: colors.primary,
                border: activeTheme === name ? '2px solid white' : '2px solid transparent',
                transform: activeTheme === name ? 'scale(1.1)' : 'scale(1)',
              }}
              title={`${name.charAt(0).toUpperCase() + name.slice(1)} theme`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ThemeSwitcher;
