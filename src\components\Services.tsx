import { Link } from 'react-router-dom';
import { Code, Shield, Headphones, Cloud, Terminal, BarChart, Cog, Brain, Network, Monitor, Server } from 'lucide-react';

const services = [
  {
    icon: <Code className="w-8 h-8 theme-icon" />,
    title: "CMS Development",
    description: "Custom WordPress themes and plugins development",
    link: "/services/cms-development"
  },
  {
    icon: <Cloud className="w-8 h-8 theme-icon" />,
    title: "Cloud Infrastructure",
    description: "Secure and scalable cloud infrastructure setup",
    link: "/services/cloud-infrastructure"
  },
  {
    icon: <Shield className="w-8 h-8 theme-icon" />,
    title: "Cybersecurity",
    description: "Advanced threat detection and prevention",
    link: "/services/cybersecurity"
  },
  {
    icon: <Headphones className="w-8 h-8 theme-icon" />,
    title: "Remote IT Support",
    description: "24/7 technical assistance and system maintenance",
    link: "/services/remote-it-support"
  },
  {
    icon: <Code className="w-8 h-8 theme-icon" />,
    title: "Custom Software",
    description: "Tailored software solutions for your business",
    link: "/services/custom-software"
  },
  {
    icon: <Terminal className="w-8 h-8 theme-icon" />,
    title: "DevOps & CI/CD",
    description: "Streamlined deployment and integration processes",
    link: "/services/devops-cicd"
  },
  {
    icon: <BarChart className="w-8 h-8 theme-icon" />,
    title: "Data Analytics",
    description: "Advanced analytics and reporting solutions",
    link: "/services/data-analytics"
  },
  {
    icon: <Cog className="w-8 h-8 theme-icon" />,
    title: "Managed IT",
    description: "Complete IT infrastructure oversight",
    link: "/services/managed-it"
  },
  {
    icon: <Brain className="w-8 h-8 theme-icon" />,
    title: "Digital Transformation",
    description: "Strategic digital transformation planning",
    link: "/services/digital-transformation"
  },
  {
    icon: <Network className="w-8 h-8 theme-icon" />,
    title: "Network Solutions",
    description: "Network security and performance optimization",
    link: "/services/network-solutions"
  },
  {
    icon: <Server className="w-8 h-8 theme-icon" />,
    title: "E-commerce Solutions",
    description: "Custom online store development",
    link: "/services/ecommerce"
  },
  {
    icon: <Monitor className="w-8 h-8 theme-icon" />,
    title: "VDI Solutions",
    description: "Virtual desktop infrastructure setup",
    link: "/services/vdi-solutions"
  }
];

const Services = () => {
  return (
    <div className="py-20" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4" style={{ color: 'var(--color-text-primary)' }}>
            <span className="relative inline-block">
              <span className="relative z-10">Our Services</span>
              <span className="absolute bottom-0 left-0 w-full h-3 -mb-2 transform -skew-x-12" 
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.2 }}></span>
            </span>
          </h2>
          <p className="text-xl max-w-3xl mx-auto" style={{ color: 'var(--color-text-secondary)' }}>
            We provide a wide range of IT services to help businesses grow and succeed. 
            Built for modern development needs.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <Link 
              to={service.link}
              key={index}
              className="backdrop-blur-sm rounded-lg p-6 transition-all duration-300"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: 'var(--color-card-border)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-hover-border)';
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(196, 30, 58, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = 'var(--color-card-border)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div className="rounded-lg p-3 inline-block mb-4" style={{ backgroundColor: 'rgba(196, 30, 58, 0.1)' }}>
                {service.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text-primary)' }}>
                {service.title}
              </h3>
              <p className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                {service.description}
              </p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Services;
