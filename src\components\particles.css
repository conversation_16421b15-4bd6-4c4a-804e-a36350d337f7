.tech-particles {
  position: absolute;
  inset: 0;
  z-index: 0;
  background: linear-gradient(0deg,
    var(--color-background) 0%,
    color-mix(in srgb, var(--color-primary) 15%, var(--color-background) 85%) 50%,
    color-mix(in srgb, var(--color-secondary) 10%, var(--color-background) 90%) 100%);
  overflow: hidden;
}

/* Add glow effect to particles */
#tsparticles canvas {
  filter: drop-shadow(0 0 10px color-mix(in srgb, var(--color-primary) 30%, transparent 70%));
}

/* Add digital circuit pattern overlay */
.tech-particles::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10h10v10H10zM30 10h10v10H30zM50 10h10v10H50zM70 10h10v10H70zM90 10h10v10H90zM10 30h10v10H10zM30 30h10v10H30zM50 30h10v10H50zM70 30h10v10H70zM90 30h10v10H90zM10 50h10v10H10zM30 50h10v10H30zM50 50h10v10H50zM70 50h10v10H70zM90 50h10v10H90zM10 70h10v10H10zM30 70h10v10H30zM50 70h10v10H50zM70 70h10v10H70zM90 70h10v10H90zM10 90h10v10H10zM30 90h10v10H30zM50 90h10v10H50zM70 90h10v10H70zM90 90h10v10H90z' fill='rgba(0, 100, 255, 0.03)' fill-rule='evenodd'/%3E%3C/svg%3E");
  background-size: 100px 100px;
  opacity: 0.5;
  z-index: -1;
  animation: circuitMove 120s linear infinite;
}

/* Add digital data flow animation */
.tech-particles::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0, 0, 0, 0) 0%,
    color-mix(in srgb, var(--color-primary) 5%, transparent 95%) 25%,
    rgba(0, 0, 0, 0) 50%,
    color-mix(in srgb, var(--color-primary) 5%, transparent 95%) 75%,
    rgba(0, 0, 0, 0) 100%);
  background-size: 200% 100%;
  z-index: -1;
  animation: dataFlow 5s linear infinite;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Digital Binary Code Overlay */
.digital-overlay {
  position: absolute;
  inset: 0;
  z-index: -1;
  opacity: 0.15;
  overflow: hidden;
}

.digital-overlay::before {
  content: '01001001 01010100 00100000 01010011 01000101 01010010 01010110 01001001 01000011 01000101 01010011 00100000 01000100 01000001 01010100 01000001 00100000 01000011 01001100 01001111 01010101 01000100 00100000 01000001 01001001 00100000 01001101 01001100 00100000 01000100 01000101 01010110 01001111 01010000 01010011 00100000 01010011 01000101 01000011 01010101 01010010 01001001 01010100 01011001';
  position: absolute;
  top: -10%;
  left: 0;
  right: 0;
  bottom: 0;
  color: color-mix(in srgb, var(--color-primary) 20%, transparent 80%);
  font-family: monospace;
  font-size: 12px;
  line-height: 1.2;
  white-space: nowrap;
  animation: binaryRain 15s linear infinite;
}

/* Add multiple binary streams with different speeds */
.digital-overlay::after {
  content: '01010100 01000101 01000011 01001000 01001110 01001111 01001100 01001111 01000111 01011001 00100000 01001001 01001110 01001110 01001111 01010110 01000001 01010100 01001001 01001111 01001110 00100000 01000110 01010101 01010100 01010101 01010010 01000101';
  position: absolute;
  top: -30%;
  left: 30%;
  right: 0;
  bottom: 0;
  color: color-mix(in srgb, var(--color-secondary) 15%, transparent 85%);
  font-family: monospace;
  font-size: 14px;
  line-height: 1.2;
  white-space: nowrap;
  animation: binaryRain 10s linear infinite;
  animation-delay: 2s;
}

/* Add flowing data streams */
.tech-particles .data-stream {
  position: absolute;
  height: 100%;
  width: 2px;
  background: linear-gradient(to bottom,
    color-mix(in srgb, var(--color-primary) 0%, transparent 100%) 0%,
    color-mix(in srgb, var(--color-primary) 40%, transparent 60%) 50%,
    color-mix(in srgb, var(--color-primary) 0%, transparent 100%) 100%);
  opacity: 0.3;
  z-index: -2;
  filter: blur(1px);
  animation: dataStreamFlow 3s infinite;
}

/* Create multiple data streams */
.tech-particles .data-stream:nth-child(1) { left: 5%; animation-delay: 0s; height: 30%; top: 0; }
.tech-particles .data-stream:nth-child(2) { left: 15%; animation-delay: 0.5s; height: 50%; top: 20%; }
.tech-particles .data-stream:nth-child(3) { left: 25%; animation-delay: 1s; height: 40%; top: 10%; }
.tech-particles .data-stream:nth-child(4) { left: 35%; animation-delay: 1.5s; height: 60%; top: 5%; }
.tech-particles .data-stream:nth-child(5) { left: 45%; animation-delay: 2s; height: 45%; top: 15%; }
.tech-particles .data-stream:nth-child(6) { left: 55%; animation-delay: 2.5s; height: 35%; top: 25%; }
.tech-particles .data-stream:nth-child(7) { left: 65%; animation-delay: 3s; height: 55%; top: 0; }
.tech-particles .data-stream:nth-child(8) { left: 75%; animation-delay: 0.2s; height: 40%; top: 15%; }
.tech-particles .data-stream:nth-child(9) { left: 85%; animation-delay: 0.7s; height: 30%; top: 25%; }
.tech-particles .data-stream:nth-child(10) { left: 95%; animation-delay: 1.2s; height: 50%; top: 10%; }
.tech-particles .data-stream:nth-child(11) { left: 10%; animation-delay: 1.7s; height: 45%; top: 30%; }
.tech-particles .data-stream:nth-child(12) { left: 20%; animation-delay: 2.2s; height: 35%; top: 40%; }
.tech-particles .data-stream:nth-child(13) { left: 30%; animation-delay: 2.7s; height: 25%; top: 50%; }
.tech-particles .data-stream:nth-child(14) { left: 40%; animation-delay: 3.2s; height: 40%; top: 35%; }
.tech-particles .data-stream:nth-child(15) { left: 50%; animation-delay: 3.7s; height: 50%; top: 25%; }

/* Add glowing nodes with pulse effect */
.tech-particles .pulse-node {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: color-mix(in srgb, var(--color-accent) 70%, transparent 30%);
  box-shadow: 0 0 12px 3px color-mix(in srgb, var(--color-accent) 40%, transparent 60%);
  z-index: -1;
  opacity: 0;
  transform: scale(0);
  animation: nodePulseExpand 4s ease-out infinite;
}

/* Position pulse nodes */
.tech-particles .pulse-node:nth-child(1) { left: 10%; top: 20%; animation-delay: 0s; }
.tech-particles .pulse-node:nth-child(2) { left: 25%; top: 40%; animation-delay: 0.7s; }
.tech-particles .pulse-node:nth-child(3) { left: 40%; top: 60%; animation-delay: 1.4s; }
.tech-particles .pulse-node:nth-child(4) { left: 55%; top: 30%; animation-delay: 2.1s; }
.tech-particles .pulse-node:nth-child(5) { left: 70%; top: 50%; animation-delay: 2.8s; }
.tech-particles .pulse-node:nth-child(6) { left: 85%; top: 70%; animation-delay: 3.5s; }
.tech-particles .pulse-node:nth-child(7) { left: 15%; top: 80%; animation-delay: 0.3s; }
.tech-particles .pulse-node:nth-child(8) { left: 30%; top: 10%; animation-delay: 1s; }
.tech-particles .pulse-node:nth-child(9) { left: 45%; top: 90%; animation-delay: 1.7s; }
.tech-particles .pulse-node:nth-child(10) { left: 60%; top: 15%; animation-delay: 2.4s; }
.tech-particles .pulse-node:nth-child(11) { left: 75%; top: 85%; animation-delay: 3.1s; }
.tech-particles .pulse-node:nth-child(12) { left: 90%; top: 25%; animation-delay: 3.8s; }
.tech-particles .pulse-node:nth-child(13) { left: 20%; top: 65%; animation-delay: 0.5s; }
.tech-particles .pulse-node:nth-child(14) { left: 50%; top: 45%; animation-delay: 1.2s; }
.tech-particles .pulse-node:nth-child(15) { left: 80%; top: 35%; animation-delay: 1.9s; }

@keyframes circuitMove {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 1000px 1000px;
  }
}

@keyframes dataFlow {
  0% {
    background-position: 0% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes binaryRain {
  0% {
    transform: translateY(-10%);
  }
  100% {
    transform: translateY(110%);
  }
}

@keyframes nodePulseExpand {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(3);
  }
}

@keyframes dataStreamFlow {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.4;
  }
  90% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* Add floating tech icons animation */
.tech-icon {
  position: absolute;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.2;
  filter: brightness(0) invert(1);
  z-index: -1;
  animation: floatIcon 15s ease-in-out infinite;
}

.tech-icon:nth-child(1) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 0L1.5 6v12L12 24l10.5-6V6L12 0zm-1 19.8L2.5 16V8l8.5 5v6.8zm1-8.3L3.5 6 12 1l8.5 5-8.5 5.5zm9 4.5L12.5 21v-6.8l8.5-5v6.8z'/%3E%3C/svg%3E");
  left: 15%;
  top: 20%;
  animation-delay: 0s;
}

.tech-icon:nth-child(2) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M21 16.5C21 19.54 16.97 22 12 22C7.03 22 3 19.54 3 16.5C3 13.46 7.03 11 12 11C16.97 11 21 13.46 21 16.5ZM12 13C9.24 13 7 14.79 7 17C7 19.21 9.24 21 12 21C14.76 21 17 19.21 17 17C17 14.79 14.76 13 12 13ZM12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9H3V7H21V9Z'/%3E%3C/svg%3E");
  left: 75%;
  top: 30%;
  animation-delay: 1s;
}

.tech-icon:nth-child(3) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M4 4h16v16H4V4zm2 2v12h12V6H6zm10 2H8v2h8V8zm-8 4h8v2H8v-2zm8 4H8v2h8v-2z'/%3E%3C/svg%3E");
  left: 40%;
  top: 70%;
  animation-delay: 2s;
}

.tech-icon:nth-child(4) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z'/%3E%3C/svg%3E");
  left: 65%;
  top: 50%;
  animation-delay: 3s;
}

.tech-icon:nth-child(5) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7v2H8v2h8v-2h-2v-2h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H3V4h18v12z'/%3E%3C/svg%3E");
  left: 25%;
  top: 35%;
  animation-delay: 1.5s;
}

.tech-icon:nth-child(6) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z'/%3E%3C/svg%3E");
  left: 55%;
  top: 75%;
  animation-delay: 2.5s;
}

.tech-icon:nth-child(7) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.31-2.69-6-6-6s-6 2.69-6 6c0 2.22 1.21 4.15 3 5.19l1-1.74c-1.19-.7-2-1.97-2-3.45 0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.48-.81 2.75-2 3.45l1 1.74c1.79-1.04 3-2.97 3-5.19zM12 3C6.48 3 2 7.48 2 13c0 3.7 2.01 6.92 4.99 8.65l1-1.73C5.61 18.53 4 15.96 4 13c0-4.42 3.58-8 8-8s8 3.58 8 8c0 2.96-1.61 5.53-4 6.92l1 1.73c2.99-1.73 5-4.95 5-8.65 0-5.52-4.48-10-10-10z'/%3E%3C/svg%3E");
  left: 85%;
  top: 15%;
  animation-delay: 0.5s;
}

.tech-icon:nth-child(8) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'/%3E%3C/svg%3E");
  left: 40%;
  top: 85%;
  animation-delay: 3.5s;
}

@keyframes floatIcon {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(5deg);
  }
  50% {
    transform: translateY(0) rotate(0deg);
  }
  75% {
    transform: translateY(15px) rotate(-5deg);
  }
}
