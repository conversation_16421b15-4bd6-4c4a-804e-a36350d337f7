// Enhanced Text-to-Speech service with ElevenLabs integration
export interface TTSOptions {
  voice?: string;
  speed?: number;
  useElevenLabs?: boolean;
}

export class TTSService {
  private currentAudio: HTMLAudioElement | null = null;
  private speechSynthesis: SpeechSynthesis | null = null;
  private currentUtterance: SpeechSynthesisUtterance | null = null;

  constructor() {
    this.speechSynthesis = window.speechSynthesis;
  }

  // Stop any currently playing speech
  async stop(): Promise<void> {
    // Stop ElevenLabs audio if playing
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }

    // Stop browser speech synthesis
    if (this.speechSynthesis && this.currentUtterance) {
      this.speechSynthesis.cancel();
      this.currentUtterance = null;
    }
  }

  // Speak text using ElevenLabs API (realistic voice)
  async speakWithElevenLabs(text: string, options: TTSOptions = {}): Promise<void> {
    const apiKey = import.meta.env.VITE_ELEVENLABS_API_KEY;
    
    if (!apiKey || apiKey === 'your_elevenlabs_api_key_here') {
      console.warn('ElevenLabs API key not configured, falling back to browser TTS');
      return this.speakWithBrowser(text, options);
    }

    try {
      // Stop any current playback
      await this.stop();

      // Use a high-quality voice (Rachel is a popular choice)
      const voiceId = options.voice || 'tnSpp4vdxKPjI9w0GnoV'; // Custom voice ID
      
      const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': apiKey,
        },
        body: JSON.stringify({
          text: text,
          model_id: 'eleven_monolingual_v1',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            style: 0.0,
            use_speaker_boost: true
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      this.currentAudio = new Audio(audioUrl);
      
      // Set playback speed if specified
      if (options.speed) {
        this.currentAudio.playbackRate = options.speed;
      }

      // Play the audio
      await this.currentAudio.play();

      // Clean up when finished
      this.currentAudio.addEventListener('ended', () => {
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        this.currentAudio = null;
      });

    } catch (error) {
      console.error('ElevenLabs TTS error:', error);
      // Fallback to browser TTS
      return this.speakWithBrowser(text, options);
    }
  }

  // Fallback to browser speech synthesis
  async speakWithBrowser(text: string, options: TTSOptions = {}): Promise<void> {
    if (!this.speechSynthesis) {
      console.error('Speech synthesis not supported');
      return;
    }

    // Stop any current speech
    await this.stop();

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Get available voices
    const voices = this.speechSynthesis.getVoices();
    
    // Find the best quality voice
    const preferredVoice = voices.find(voice => 
      voice.name.includes('Google') || 
      voice.name.includes('Microsoft') ||
      voice.name.includes('Natural') ||
      voice.name.includes('Neural')
    ) || voices.find(voice => voice.lang.startsWith('en')) || voices[0];

    if (preferredVoice) {
      utterance.voice = preferredVoice;
    }

    // Set speech parameters for better quality
    utterance.rate = options.speed || 0.9;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;

    this.currentUtterance = utterance;
    this.speechSynthesis.speak(utterance);
  }

  // Main speak method that chooses the best available option
  async speak(text: string, options: TTSOptions = {}): Promise<void> {
    const useElevenLabs = options.useElevenLabs !== false; // Default to true
    
    if (useElevenLabs) {
      return this.speakWithElevenLabs(text, options);
    } else {
      return this.speakWithBrowser(text, options);
    }
  }

  // Check if ElevenLabs is available
  isElevenLabsAvailable(): boolean {
    const apiKey = import.meta.env.VITE_ELEVENLABS_API_KEY;
    return apiKey && apiKey !== 'your_elevenlabs_api_key_here';
  }

  // Get available ElevenLabs voices
  async getElevenLabsVoices(): Promise<any[]> {
    const apiKey = import.meta.env.VITE_ELEVENLABS_API_KEY;
    
    if (!apiKey || apiKey === 'your_elevenlabs_api_key_here') {
      return [];
    }

    try {
      const response = await fetch('https://api.elevenlabs.io/v1/voices', {
        headers: {
          'xi-api-key': apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch voices: ${response.status}`);
      }

      const data = await response.json();
      return data.voices || [];
    } catch (error) {
      console.error('Error fetching ElevenLabs voices:', error);
      return [];
    }
  }
}

// Export singleton instance
export const ttsService = new TTSService();
