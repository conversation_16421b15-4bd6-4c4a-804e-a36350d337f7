export interface ProjectFeature {
  id?: number;
  feature: string;
  isHighlighted: boolean;
}

export interface Project {
  id?: number;
  name: string;
  url: string;
  description: string;
  category: string;
  features: ProjectFeature[];
  createdAt?: string;
  updatedAt?: string;
}

export interface User {
  id: number;
  email: string;
  isAdmin: boolean;
}

export interface Site {
  id?: number;
  name: string;
  url: string;
  description: string;
  category: string;
  featured: boolean;
  createdAt?: string;
}
