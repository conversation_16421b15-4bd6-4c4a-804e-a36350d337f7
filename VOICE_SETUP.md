# Realistic Voice Setup Guide

Your chatbot now supports realistic AI-generated voices using ElevenLabs! Follow these steps to enable it:

## Option 1: Use ElevenLabs (Recommended - Realistic Voice)

1. **Get a free ElevenLabs account:**
   - Go to https://elevenlabs.io/
   - Sign up for a free account
   - You get 10,000 characters per month for free

2. **Get your API key:**
   - Go to your ElevenLabs dashboard
   - Click on your profile icon → "Profile"
   - Copy your API key

3. **Update your .env file:**
   - Open `.env` file in your project root
   - Replace `your_elevenlabs_api_key_here` with your actual API key:
   ```
   ELEVENLABS_API_KEY=your_actual_api_key_here
   ```

4. **Restart your development server:**
   ```bash
   npm run dev
   ```

## Option 2: Use Browser Voice (Fallback)

If you don't want to use ElevenLabs, the chatbot will automatically fall back to your browser's built-in text-to-speech. This works without any setup but sounds more robotic.

## Voice Features

- **Realistic Speech**: ElevenLabs provides natural-sounding AI voices
- **Automatic Fallback**: If ElevenLabs isn't available, uses browser TTS
- **Speed Control**: Optimized speech rate for clarity
- **Smart Filtering**: No "thinking" text is spoken aloud
- **Easy Toggle**: Click the volume icon to enable/disable speech

## Available Voices

The chatbot uses "Rachel" voice by default (warm, professional female voice). You can modify the voice in `src/services/ttsService.ts` by changing the `voiceId` parameter.

Popular ElevenLabs voice IDs:
- Rachel (default): `EXAVITQu4vr4xnSDxMaL`
- Josh (male): `TxGEqnHWrfWFTfGW9XjX`
- Bella (female): `EXAVITQu4vr4xnSDxMaL`

## Troubleshooting

- **No sound**: Check if your browser allows audio playback
- **Robotic voice**: Make sure your ElevenLabs API key is correctly set
- **API errors**: Check your ElevenLabs account usage limits
- **CORS errors**: ElevenLabs API should work from localhost during development

Enjoy your realistic AI chatbot voice! 🎙️
