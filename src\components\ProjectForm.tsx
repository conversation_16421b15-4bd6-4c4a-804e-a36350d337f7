import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Project, ProjectFeature } from '../types/showcase';
import { createProject, updateProject } from '../services/showcaseService';
import { X, Plus, Check, Trash2 } from 'lucide-react';

interface ProjectFormProps {
  project: Project | null;
  onClose: () => void;
}

const ProjectForm: React.FC<ProjectFormProps> = ({ project, onClose }) => {
  const [formData, setFormData] = useState<Project>({
    name: '',
    url: '',
    description: '',
    category: '',
    features: []
  });
  
  const [newFeature, setNewFeature] = useState('');
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  useEffect(() => {
    if (project) {
      setFormData(project);
    }
  }, [project]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleAddFeature = () => {
    if (!newFeature.trim()) return;
    
    const feature: ProjectFeature = {
      feature: newFeature.trim(),
      isHighlighted
    };
    
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, feature]
    }));
    
    setNewFeature('');
    setIsHighlighted(false);
  };
  
  const handleRemoveFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };
  
  const handleToggleHighlight = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((feature, i) => 
        i === index 
          ? { ...feature, isHighlighted: !feature.isHighlighted }
          : feature
      )
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.url || !formData.description || !formData.category) {
      setError('Please fill in all required fields.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      if (project && project.id) {
        await updateProject(project.id, formData);
        setSuccess('Project updated successfully!');
      } else {
        await createProject(formData);
        setSuccess('Project created successfully!');
      }
      
      // Close the form after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      setError('Failed to save project. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gray-900 rounded-lg border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center border-b border-gray-800 p-4">
          <h2 className="text-xl font-bold text-white">
            {project ? 'Edit Project' : 'Add New Project'}
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-900/30 border border-green-800 text-green-300 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-gray-400 text-sm mb-2" htmlFor="name">
                Project Name *
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleChange}
                className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-400 text-sm mb-2" htmlFor="category">
                Category *
              </label>
              <input
                id="category"
                name="category"
                type="text"
                value={formData.category}
                onChange={handleChange}
                className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                required
              />
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-400 text-sm mb-2" htmlFor="url">
              Website URL *
            </label>
            <input
              id="url"
              name="url"
              type="url"
              value={formData.url}
              onChange={handleChange}
              placeholder="https://example.com"
              className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-400 text-sm mb-2" htmlFor="description">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-400 text-sm mb-2">
              Features
            </label>
            
            <div className="flex mb-2">
              <input
                type="text"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                placeholder="Add a feature..."
                className="flex-1 bg-gray-800 border border-gray-700 rounded-l px-3 py-2 text-white focus:outline-none focus:border-purple-500"
              />
              <button
                type="button"
                onClick={() => setIsHighlighted(!isHighlighted)}
                className={`px-3 py-2 ${
                  isHighlighted 
                    ? 'bg-purple-600 text-white' 
                    : 'bg-gray-700 text-gray-300'
                }`}
              >
                <Check className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={handleAddFeature}
                className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-r"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
            
            {isHighlighted && (
              <p className="text-xs text-purple-400 mb-4">
                This feature will be highlighted as a key feature
              </p>
            )}
            
            {formData.features.length > 0 ? (
              <ul className="space-y-2 mt-3">
                {formData.features.map((feature, index) => (
                  <li 
                    key={index}
                    className={`flex items-center justify-between p-2 rounded ${
                      feature.isHighlighted 
                        ? 'bg-purple-900/20 border border-purple-800/50' 
                        : 'bg-gray-800/50 border border-gray-700/50'
                    }`}
                  >
                    <span className="text-gray-300">{feature.feature}</span>
                    <div className="flex items-center">
                      <button
                        type="button"
                        onClick={() => handleToggleHighlight(index)}
                        className={`p-1 rounded-full mr-2 ${
                          feature.isHighlighted 
                            ? 'bg-purple-600 text-white' 
                            : 'bg-gray-700 text-gray-400'
                        }`}
                      >
                        <Check className="w-3 h-3" />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleRemoveFeature(index)}
                        className="text-red-400 hover:text-red-300 p-1"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 text-sm">No features added yet.</p>
            )}
          </div>
          
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded flex items-center"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                  Saving...
                </>
              ) : (
                'Save Project'
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default ProjectForm;
