import themeConfig from '../themeConfig';

/**
 * Updates the theme by setting CSS variables on the document root
 * @param newTheme Partial theme configuration to apply
 */
export function updateTheme(newTheme: Partial<typeof themeConfig> = {}) {
  // Merge the new theme with the default theme
  const mergedTheme = {
    ...themeConfig,
    ...newTheme,
    colors: {
      ...themeConfig.colors,
      ...(newTheme.colors || {}),
      text: {
        ...themeConfig.colors.text,
        ...(newTheme.colors?.text || {}),
      },
      button: {
        ...themeConfig.colors.button,
        ...(newTheme.colors?.button || {}),
        primary: {
          ...themeConfig.colors.button.primary,
          ...(newTheme.colors?.button?.primary || {}),
        },
        secondary: {
          ...themeConfig.colors.button.secondary,
          ...(newTheme.colors?.button?.secondary || {}),
        },
      },
      card: {
        ...themeConfig.colors.card,
        ...(newTheme.colors?.card || {}),
      },
    },
    fontFamily: {
      ...themeConfig.fontFamily,
      ...(newTheme.fontFamily || {}),
    },
  };

  // Apply theme to CSS variables
  const root = document.documentElement;
  
  // Set color variables
  root.style.setProperty('--color-primary', mergedTheme.colors.primary);
  root.style.setProperty('--color-secondary', mergedTheme.colors.secondary);
  root.style.setProperty('--color-accent', mergedTheme.colors.accent);
  root.style.setProperty('--color-background', mergedTheme.colors.background);
  root.style.setProperty('--color-text-primary', mergedTheme.colors.text.primary);
  root.style.setProperty('--color-text-secondary', mergedTheme.colors.text.secondary);
  
  // Button styles
  root.style.setProperty('--color-button-primary-bg', mergedTheme.colors.button.primary.background);
  root.style.setProperty('--color-button-primary-text', mergedTheme.colors.button.primary.text);
  root.style.setProperty('--color-button-primary-hover', mergedTheme.colors.button.primary.hover);
  root.style.setProperty('--color-button-secondary-bg', mergedTheme.colors.button.secondary.background);
  root.style.setProperty('--color-button-secondary-text', mergedTheme.colors.button.secondary.text);
  root.style.setProperty('--color-button-secondary-border', mergedTheme.colors.button.secondary.border);
  root.style.setProperty('--color-button-secondary-hover', mergedTheme.colors.button.secondary.hover);
  
  // Card styles
  root.style.setProperty('--color-card-bg', mergedTheme.colors.card.background);
  root.style.setProperty('--color-card-border', mergedTheme.colors.card.border);
  root.style.setProperty('--color-card-hover-border', mergedTheme.colors.card.hoverBorder);
  
  // Icon color
  root.style.setProperty('--color-icon', mergedTheme.colors.icon);
  
  // Highlight color
  root.style.setProperty('--color-highlight', mergedTheme.colors.highlight);
  
  // Font families
  root.style.setProperty('--font-family-primary', mergedTheme.fontFamily.primary);
  root.style.setProperty('--font-family-secondary', mergedTheme.fontFamily.secondary);

  // Save the theme to localStorage for persistence
  localStorage.setItem('spirelab-theme', JSON.stringify(mergedTheme));
  
  return mergedTheme;
}

/**
 * Loads the theme from localStorage or uses the default theme
 */
export function loadTheme() {
  try {
    const savedTheme = localStorage.getItem('spirelab-theme');
    if (savedTheme) {
      const parsedTheme = JSON.parse(savedTheme);
      return updateTheme(parsedTheme);
    }
  } catch (error) {
    console.error('Failed to load theme from localStorage:', error);
  }
  
  // If no theme in localStorage or error occurred, use default theme
  return updateTheme();
}

/**
 * Initializes the theme system
 */
export function initializeTheme() {
  // Apply the theme when the DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadTheme);
  } else {
    loadTheme();
  }
}

/**
 * Creates a new theme with specified colors
 * @param colors Object containing color overrides
 */
export function createTheme(colors: Partial<typeof themeConfig.colors>) {
  // Type assertion to handle the partial nature of the colors object
  return updateTheme({ colors: colors as typeof themeConfig.colors });
}
