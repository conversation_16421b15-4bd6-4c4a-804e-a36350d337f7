import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Shield, Code, Network, Settings, Lock } from 'lucide-react';

const NetgateFirewall = () => {
  const features = [
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "pfSense Plus",
      description: "Enterprise-grade firewall software with advanced security features"
    },
    {
      icon: <Code className="text-purple-400" size={24} />,
      title: "Open Source Foundation",
      description: "Built on trusted open-source technology with community support"
    },
    {
      icon: <Network className="text-purple-400" size={24} />,
      title: "Advanced Routing",
      description: "Powerful routing capabilities with multi-WAN and VPN support"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Package System",
      description: "Extensible functionality through verified package repository"
    }
  ];

  const pricing = [
    {
      name: "Standard",
      price: "Contact Us",
      features: [
        "pfSense Plus Software",
        "Basic Hardware",
        "Community Support",
        "Standard Updates",
        "Basic VPN Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Enterprise Hardware",
        "TAC Pro Support",
        "Priority Updates",
        "Advanced VPN Features",
        "High Availability",
        "Custom Configurations"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Hardware Options",
        "24/7 Premium Support",
        "Enterprise Integration",
        "Custom Development",
        "Dedicated Engineer",
        "Custom SLA"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Netgate pfSense Solutions"
      description="Enterprise-grade open source firewall with professional support"
      features={features}
      pricing={pricing}
      technologies={['pfSense Plus', 'FreeBSD', 'OpenVPN', 'IPSec', 'VLAN', 'HAProxy']}
      codeExample={`// pfSense Firewall Configuration Manager
class PfSenseManager {
  constructor() {
    this.interfaces = new Map();
    this.rules = [];
    this.vpnConfigs = new Map();
    this.packages = new Set();
  }

  async configureInterface(config) {
    const { name, type, ip, subnet, gateway } = config;
    
    // Validate interface configuration
    this.validateInterfaceConfig(config);
    
    // Configure interface
    const interfaceConfig = {
      name,
      type,
      ip,
      subnet,
      gateway,
      flags: {
        enabled: true,
        blockPrivate: true,
        blockBogon: true
      },
      mtu: this.calculateOptimalMTU(type)
    };
    
    await this.applyInterfaceConfig(interfaceConfig);
    this.interfaces.set(name, interfaceConfig);
    
    return {
      status: 'configured',
      interface: name,
      timestamp: new Date()
    };
  }

  async setupVPN(vpnConfig) {
    const { type, crypto, auth, network } = vpnConfig;
    
    // Generate VPN certificates and keys
    const certificates = await this.generateVPNCertificates();
    
    // Configure VPN tunnel
    const tunnelConfig = {
      type, // OpenVPN or IPSec
      certificates,
      crypto: {
        cipher: crypto.cipher,
        digest: crypto.digest,
        dh: crypto.dhGroup
      },
      authentication: {
        method: auth.method,
        local: auth.local,
        remote: auth.remote
      },
      network: {
        local: network.local,
        remote: network.remote,
        tunnel: network.tunnel
      }
    };
    
    await this.applyVPNConfig(tunnelConfig);
    this.vpnConfigs.set(tunnelConfig.name, tunnelConfig);
    
    return {
      status: 'configured',
      type: type,
      network: network.tunnel
    };
  }

  async installPackage(packageName) {
    // Verify package in official repository
    await this.verifyPackage(packageName);
    
    // Install package and dependencies
    const result = await this.executePackageInstall(packageName);
    
    if (result.success) {
      this.packages.add(packageName);
      await this.configurePackage(packageName);
    }
    
    return {
      status: result.success ? 'installed' : 'failed',
      package: packageName,
      version: result.version
    };
  }
}`}
    />
  );
};

export default NetgateFirewall;
