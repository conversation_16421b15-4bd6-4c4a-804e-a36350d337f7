import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import './spireParticles.css';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  opacity: number;
  rotation: number;
  rotationSpeed: number;
  type: 'star' | 'spire';
  // For orbital movement
  orbitRadius: number;
  orbitSpeed: number;
  orbitAngle: number;
  centerX: number;
  centerY: number;
  // For pulsing
  pulseSpeed: number;
  pulsePhase: number;
  // For spires
  pathType: 'orbit' | 'zigzag' | 'spiral';
  zigzagAmplitude?: number;
  zigzagFrequency?: number;
  spiralTightness?: number;
  spiralExpansion?: number;
}

const SpireParticles: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number | null>(null);
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  
  // Create a random particle
  const createRandomParticle = (id: number, containerWidth: number, containerHeight: number, type: 'star' | 'spire'): Particle => {
    // Center points for orbits
    const centerX = containerWidth * (0.3 + Math.random() * 0.4);
    const centerY = containerHeight * (0.3 + Math.random() * 0.4);
    
    // Random position within container
    const x = type === 'star' ? Math.random() * containerWidth : centerX;
    const y = type === 'star' ? Math.random() * containerHeight : centerY;
    
    // Random size based on type
    const size = type === 'star' 
      ? Math.random() * 6 + 3 // Stars: 3-9px
      : Math.random() * 10 + 6; // Spires: 6-16px
    
    // Random opacity between 0.2 and 0.7
    const opacity = Math.random() * 0.5 + 0.2;
    
    // Random rotation (0-360 degrees)
    const rotation = Math.random() * 360;
    
    // Random rotation speed (-1 to 1 degrees per frame)
    const rotationSpeed = (Math.random() * 2 - 1) * 0.5;
    
    // Orbital parameters
    const orbitRadius = type === 'star' 
      ? Math.random() * 100 + 50 // Stars have larger orbits
      : Math.random() * 50 + 20; // Spires have smaller orbits
    
    const orbitSpeed = type === 'star'
      ? (Math.random() * 0.002 + 0.001) // Stars move slower
      : (Math.random() * 0.004 + 0.002); // Spires move faster
    
    const orbitAngle = Math.random() * Math.PI * 2; // Random start angle
    
    // Pulsing parameters
    const pulseSpeed = Math.random() * 0.05 + 0.02;
    const pulsePhase = Math.random() * Math.PI * 2;
    
    // For spires, choose a random path type
    const pathType = type === 'star' ? 'orbit' : (['orbit', 'zigzag', 'spiral'][Math.floor(Math.random() * 3)] as 'orbit' | 'zigzag' | 'spiral');
    
    // Additional parameters for zigzag and spiral paths
    const zigzagAmplitude = Math.random() * 30 + 10;
    const zigzagFrequency = Math.random() * 0.1 + 0.05;
    const spiralTightness = Math.random() * 0.1 + 0.05;
    const spiralExpansion = Math.random() * 0.2 + 0.1;
    
    return { 
      id, x, y, size, opacity, rotation, rotationSpeed, type,
      orbitRadius, orbitSpeed, orbitAngle, centerX, centerY,
      pulseSpeed, pulsePhase, pathType,
      zigzagAmplitude, zigzagFrequency, spiralTightness, spiralExpansion
    };
  };
  
  // Animation loop
  const startAnimationLoop = () => {
    if (!containerRef.current) return;
    
    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;
    
    const updateParticles = () => {
      particlesRef.current = particlesRef.current.map(particle => {
        // Update rotation for all particles
        const newRotation = particle.rotation + particle.rotationSpeed;
        
        // Calculate new position based on particle type and path
        let newX = particle.x;
        let newY = particle.y;
        
        // Update orbit angle
        const newOrbitAngle = particle.orbitAngle + particle.orbitSpeed;
        
        if (particle.type === 'star') {
          // Stars follow orbital paths around their center points
          newX = particle.centerX + Math.cos(newOrbitAngle) * particle.orbitRadius;
          newY = particle.centerY + Math.sin(newOrbitAngle) * particle.orbitRadius;
          
          // Add subtle pulsing to the orbit radius
          const pulseEffect = Math.sin(Date.now() * particle.pulseSpeed + particle.pulsePhase) * 5;
          
          // Apply the pulse effect to the position
          newX += Math.cos(newOrbitAngle) * pulseEffect;
          newY += Math.sin(newOrbitAngle) * pulseEffect;
          
          // Check if star is out of bounds and adjust if needed
          if (newX < -20 || newX > containerWidth + 20 || 
              newY < -20 || newY > containerHeight + 20) {
            // Reset to a new orbital center
            const newCenterX = containerWidth * (0.3 + Math.random() * 0.4);
            const newCenterY = containerHeight * (0.3 + Math.random() * 0.4);
            
            return {
              ...particle,
              centerX: newCenterX,
              centerY: newCenterY,
              x: newCenterX,
              y: newCenterY,
              orbitAngle: Math.random() * Math.PI * 2,
              rotation: Math.random() * 360
            };
          }
        } else {
          // Spires have different movement patterns based on pathType
          switch (particle.pathType) {
            case 'orbit':
              // Orbital motion with different eccentricity
              newX = particle.centerX + Math.cos(newOrbitAngle) * particle.orbitRadius * 1.2;
              newY = particle.centerY + Math.sin(newOrbitAngle) * particle.orbitRadius * 0.8;
              break;
              
            case 'zigzag':
              // Zigzag pattern - combination of orbital and sine wave
              const baseX = particle.centerX + Math.cos(newOrbitAngle) * particle.orbitRadius;
              const baseY = particle.centerY + Math.sin(newOrbitAngle) * particle.orbitRadius;
              
              // Add zigzag effect
              newX = baseX + Math.sin(Date.now() * particle.zigzagFrequency!) * particle.zigzagAmplitude!;
              newY = baseY;
              break;
              
            case 'spiral':
              // Spiral pattern - gradually changing radius
              const timeOffset = Date.now() * particle.spiralTightness!;
              const radiusChange = Math.sin(timeOffset) * particle.spiralExpansion! * particle.orbitRadius;
              const currentRadius = particle.orbitRadius + radiusChange;
              
              newX = particle.centerX + Math.cos(newOrbitAngle) * currentRadius;
              newY = particle.centerY + Math.sin(newOrbitAngle) * currentRadius;
              break;
          }
          
          // Check if spire is out of bounds and adjust if needed
          if (newX < -20 || newX > containerWidth + 20 || 
              newY < -20 || newY > containerHeight + 20) {
            // Reset to a new position and path
            const newCenterX = containerWidth * (0.3 + Math.random() * 0.4);
            const newCenterY = containerHeight * (0.3 + Math.random() * 0.4);
            const newPathType = ['orbit', 'zigzag', 'spiral'][Math.floor(Math.random() * 3)] as 'orbit' | 'zigzag' | 'spiral';
            
            return {
              ...particle,
              centerX: newCenterX,
              centerY: newCenterY,
              x: newCenterX,
              y: newCenterY,
              pathType: newPathType,
              orbitAngle: Math.random() * Math.PI * 2,
              rotation: Math.random() * 360
            };
          }
        }
        
        // Apply the new position and rotation
        return { 
          ...particle, 
          x: newX, 
          y: newY, 
          rotation: newRotation,
          orbitAngle: newOrbitAngle
        };
      });
      
      // Force re-render
      forceUpdate();
      
      // Continue animation loop
      animationRef.current = requestAnimationFrame(updateParticles);
    };
    
    animationRef.current = requestAnimationFrame(updateParticles);
  };
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    // Create initial particles
    const initialParticles: Particle[] = [];
    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;
    
    // Create 15 star particles
    for (let i = 0; i < 15; i++) {
      initialParticles.push(createRandomParticle(i, containerWidth, containerHeight, 'star'));
    }
    
    // Create 10 spire particles
    for (let i = 15; i < 25; i++) {
      initialParticles.push(createRandomParticle(i, containerWidth, containerHeight, 'spire'));
    }
    
    particlesRef.current = initialParticles;
    
    // Start animation loop
    startAnimationLoop();
    
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);
  
  return (
    <div 
      ref={containerRef} 
      className="spire-particles-container"
    >
      {particlesRef.current.map(particle => (
        <motion.div
          key={particle.id}
          className={`particle ${particle.type}-particle`}
          style={{
            left: particle.x,
            top: particle.y,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            opacity: particle.opacity,
            transform: `rotate(${particle.rotation}deg) scale(${1 + Math.sin(Date.now() * particle.pulseSpeed + particle.pulsePhase) * 0.2})`,
            transition: 'transform 0.1s ease-out'
          }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
        />
      ))}
    </div>
  );
};

export default SpireParticles;
