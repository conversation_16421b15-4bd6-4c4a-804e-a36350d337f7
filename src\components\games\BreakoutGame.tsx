import React, { useEffect, useRef } from 'react';

interface Brick {
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  active: boolean;
}

interface GameState {
  paddle: {
    x: number;
    width: number;
    height: number;
    speed: number;
  };
  ball: {
    x: number;
    y: number;
    radius: number;
    dx: number;
    dy: number;
    speed: number;
  };
  bricks: Brick[];
  score: number;
  lives: number;
  gameOver: boolean;
}

class BreakoutGameLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  state: GameState;
  isRunning: boolean = false;
  mouseX: number = 0;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    const paddleWidth = 80;
    const paddleHeight = 10;

    this.state = {
      paddle: {
        x: (this.width - paddleWidth) / 2,
        width: paddleWidth,
        height: paddleHeight,
        speed: 8
      },
      ball: {
        x: this.width / 2,
        y: this.height - paddleHeight - 10,
        radius: 6,
        dx: 4,
        dy: -4,
        speed: 4
      },
      bricks: this.createBricks(),
      score: 0,
      lives: 3,
      gameOver: false
    };

    canvas.addEventListener('mousemove', (e) => {
      const rect = canvas.getBoundingClientRect();
      this.mouseX = (e.clientX - rect.left) * (canvas.width / rect.width);
    });
  }

  createBricks(): Brick[] {
    const bricks: Brick[] = [];
    const rows = 4;
    const cols = 8;
    const padding = 10;
    const brickWidth = (this.width - (cols + 1) * padding) / cols;
    const colors = ['#ef4444', '#f97316', '#84cc16', '#06b6d4'];

    for (let r = 0; r < rows; r++) {
      for (let c = 0; c < cols; c++) {
        bricks.push({
          x: c * (brickWidth + padding) + padding,
          y: r * (20 + padding) + padding + 30,
          width: brickWidth,
          height: 20,
          color: colors[r],
          active: true
        });
      }
    }

    return bricks;
  }

  update() {
    if (!this.isRunning || this.state.gameOver) return;

    // Update paddle position based on mouse
    const newX = this.mouseX - this.state.paddle.width / 2;
    this.state.paddle.x = Math.max(0, Math.min(newX, this.width - this.state.paddle.width));

    // Update ball position
    this.state.ball.x += this.state.ball.dx;
    this.state.ball.y += this.state.ball.dy;

    // Ball collision with walls
    if (this.state.ball.x + this.state.ball.radius > this.width || 
        this.state.ball.x - this.state.ball.radius < 0) {
      this.state.ball.dx *= -1;
    }
    if (this.state.ball.y - this.state.ball.radius < 0) {
      this.state.ball.dy *= -1;
    }

    // Ball collision with paddle
    if (this.state.ball.y + this.state.ball.radius > this.height - this.state.paddle.height) {
      if (this.state.ball.x > this.state.paddle.x && 
          this.state.ball.x < this.state.paddle.x + this.state.paddle.width) {
        this.state.ball.dy = -this.state.ball.speed;
        // Adjust angle based on where ball hits paddle
        const hitPos = (this.state.ball.x - this.state.paddle.x) / this.state.paddle.width;
        this.state.ball.dx = this.state.ball.speed * (hitPos * 2 - 1);
      } else if (this.state.ball.y > this.height) {
        // Ball missed paddle
        this.state.lives--;
        if (this.state.lives <= 0) {
          this.state.gameOver = true;
        } else {
          // Reset ball
          this.state.ball.x = this.width / 2;
          this.state.ball.y = this.height - this.state.paddle.height - 10;
          this.state.ball.dx = this.state.ball.speed;
          this.state.ball.dy = -this.state.ball.speed;
        }
      }
    }

    // Ball collision with bricks
    this.state.bricks.forEach(brick => {
      if (!brick.active) return;

      if (this.state.ball.x + this.state.ball.radius > brick.x &&
          this.state.ball.x - this.state.ball.radius < brick.x + brick.width &&
          this.state.ball.y + this.state.ball.radius > brick.y &&
          this.state.ball.y - this.state.ball.radius < brick.y + brick.height) {
        brick.active = false;
        this.state.ball.dy *= -1;
        this.state.score += 100;

        // Check win condition
        if (this.state.bricks.every(b => !b.active)) {
          this.state.gameOver = true;
        }
      }
    });
  }

  draw() {
    // Clear canvas
    this.ctx.fillStyle = '#1a1a1a';
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw bricks
    this.state.bricks.forEach(brick => {
      if (!brick.active) return;
      this.ctx.fillStyle = brick.color;
      this.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);
    });

    // Draw paddle
    this.ctx.fillStyle = '#4ade80';
    this.ctx.fillRect(
      this.state.paddle.x,
      this.height - this.state.paddle.height,
      this.state.paddle.width,
      this.state.paddle.height
    );

    // Draw ball
    this.ctx.beginPath();
    this.ctx.arc(
      this.state.ball.x,
      this.state.ball.y,
      this.state.ball.radius,
      0,
      Math.PI * 2
    );
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fill();
    this.ctx.closePath();

    // Draw score and lives
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '16px Arial';
    this.ctx.fillText(`Score: ${this.state.score}`, 10, 20);
    this.ctx.fillText(`Lives: ${this.state.lives}`, this.width - 70, 20);

    if (this.state.gameOver) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.width, this.height);
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '30px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        this.state.lives > 0 ? 'YOU WIN!' : 'GAME OVER',
        this.width/2,
        this.height/2
      );
      this.ctx.font = '20px Arial';
      this.ctx.fillText(`Final Score: ${this.state.score}`, this.width/2, this.height/2 + 40);
    }
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = () => {
    if (!this.isRunning) return;

    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface BreakoutGameProps {
  isPreview?: boolean;
}

const BreakoutGame: React.FC<BreakoutGameProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<BreakoutGameLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new BreakoutGameLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default BreakoutGame;
