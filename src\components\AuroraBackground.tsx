import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';

const AuroraBackground: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const materialRef = useRef<THREE.ShaderMaterial | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const [themeColors, setThemeColors] = useState({
    primary: [0.8, 0.2, 0.1], // Default red
    secondary: [0.5, 0.0, 0.0], // Default dark red
    accent: [1.0, 0.8, 0.0] // Default gold
  });

  // Function to convert hex color to RGB array
  const hexToRgb = (hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    ] : [0.8, 0.2, 0.1];
  };

  // Function to get CSS variable value
  const getCSSVariable = (variable: string) => {
    return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
  };

  // Function to update theme colors
  const updateThemeColors = () => {
    const primary = getCSSVariable('--color-primary') || '#c41e3a';
    const secondary = getCSSVariable('--color-secondary') || '#8b0000';
    const accent = getCSSVariable('--color-accent') || '#ffd700';

    setThemeColors({
      primary: hexToRgb(primary),
      secondary: hexToRgb(secondary),
      accent: hexToRgb(accent)
    });
  };

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    mountRef.current.appendChild(renderer.domElement);

    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // Update theme colors initially
    updateThemeColors();

    // Shader material with aurora comet effect
    const material = new THREE.ShaderMaterial({
      uniforms: {
        iTime: { value: 0 },
        iResolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) },
        primaryColor: { value: new THREE.Vector3(...themeColors.primary) },
        secondaryColor: { value: new THREE.Vector3(...themeColors.secondary) },
        accentColor: { value: new THREE.Vector3(...themeColors.accent) }
      },
      vertexShader: `
        void main() {
          gl_Position = vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float iTime;
        uniform vec2 iResolution;
        uniform vec3 primaryColor;
        uniform vec3 secondaryColor;
        uniform vec3 accentColor;

        #define NUM_OCTAVES 3

        float rand(vec2 n) {
          return fract(sin(dot(n, vec2(12.9898, 4.1414))) * 43758.5453);
        }

        float noise(vec2 p){
          vec2 ip = floor(p);
          vec2 u = fract(p);
          u = u*u*(3.0-2.0*u);

          float res = mix(
            mix(rand(ip),rand(ip+vec2(1.0,0.0)),u.x),
            mix(rand(ip+vec2(0.0,1.0)),rand(ip+vec2(1.0,1.0)),u.x),u.y);
          return res*res;
        }

        float fbm(vec2 x) {
          float v = 0.0;
          float a = 0.3;
          vec2 shift = vec2(100);
          mat2 rot = mat2(cos(0.5), sin(0.5), -sin(0.5), cos(0.50));
          for (int i = 0; i < NUM_OCTAVES; ++i) {
            v += a * noise(x);
            x = rot * x * 2.0 + shift;
            a *= 0.4;
          }
          return v;
        }

        void main() {
          vec2 shake = vec2(sin(iTime * 1.2) * 0.005, cos(iTime * 2.1) * 0.005);
          
          vec2 p = ((gl_FragCoord.xy + shake * iResolution.xy) - iResolution.xy * 0.5) / iResolution.y * mat2(6.0, -4.0, 4.0, 6.0);
          vec2 v;
          vec4 o = vec4(0.0);
          
          float f = 2.0 + fbm(p + vec2(iTime * 5.0, 0.0)) * 0.5; 
          
          for(float i = 0.0; i++ < 35.0;)
          {
            v = p + cos(i * i + (iTime + p.x * 0.08) * 0.025 + i * vec2(13.0, 11.0)) * 3.5 + vec2(sin(iTime * 3.0 + i) * 0.003, cos(iTime * 3.5 - i) * 0.003);
            
            float tailNoise = fbm(v + vec2(iTime * 0.5, i)) * 0.3 * (1.0 - (i / 35.0));

            // Dynamic aurora colors based on theme
            float colorMix1 = sin(i * 0.2 + iTime * 0.4) * 0.5 + 0.5;
            float colorMix2 = cos(i * 0.3 + iTime * 0.5) * 0.5 + 0.5;
            float colorMix3 = sin(i * 0.4 + iTime * 0.3) * 0.5 + 0.5;

            vec3 color1 = mix(primaryColor, secondaryColor, colorMix1);
            vec3 color2 = mix(secondaryColor, accentColor, colorMix2);
            vec3 finalColor = mix(color1, color2, colorMix3);

            vec4 auroraColors = vec4(finalColor, 1.0);
            
            vec4 currentContribution = auroraColors * exp(sin(i * i + iTime * 0.8)) / length(max(v, vec2(v.x * f * 0.015, v.y * 1.5)));
            
            // Thinner comets
            float thinnessFactor = smoothstep(0.0, 1.0, i / 35.0) * 0.6; 
            o += currentContribution * (1.0 + tailNoise * 0.8) * thinnessFactor;
          }
          
          // Brightness adjustment
          o = tanh(pow(o / 100.0, vec4(1.6)));
          gl_FragColor = o * 1.2; // Slightly dimmer for background use
        }
      `,
      transparent: true
    });

    materialRef.current = material;

    // Create plane geometry
    const geometry = new THREE.PlaneGeometry(2, 2);
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);

    // Animation loop
    const animate = () => {
      if (materialRef.current) {
        materialRef.current.uniforms.iTime.value += 0.016;
      }
      if (rendererRef.current && sceneRef.current) {
        rendererRef.current.render(sceneRef.current, camera);
      }
      animationIdRef.current = requestAnimationFrame(animate);
    };

    // Handle window resize
    const handleResize = () => {
      if (rendererRef.current && materialRef.current) {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        rendererRef.current.setSize(width, height);
        materialRef.current.uniforms.iResolution.value.set(width, height);
      }
    };

    window.addEventListener('resize', handleResize);
    animate();

    // Listen for theme changes
    const observer = new MutationObserver(() => {
      updateThemeColors();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    // Cleanup function
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      observer.disconnect();

      if (rendererRef.current && mountRef.current) {
        mountRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }

      if (materialRef.current) {
        materialRef.current.dispose();
      }

      if (sceneRef.current) {
        sceneRef.current.clear();
      }
    };
  }, []);

  // Update shader uniforms when theme colors change
  useEffect(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.primaryColor.value.set(...themeColors.primary);
      materialRef.current.uniforms.secondaryColor.value.set(...themeColors.secondary);
      materialRef.current.uniforms.accentColor.value.set(...themeColors.accent);
    }
  }, [themeColors]);

  // Create dynamic background gradient based on theme colors
  const backgroundGradient = `linear-gradient(0deg,
    rgb(${Math.floor(themeColors.primary[0] * 255 * 0.1)}, ${Math.floor(themeColors.primary[1] * 255 * 0.1)}, ${Math.floor(themeColors.primary[2] * 255 * 0.1)}) 0%,
    rgb(${Math.floor(themeColors.secondary[0] * 255 * 0.2)}, ${Math.floor(themeColors.secondary[1] * 255 * 0.2)}, ${Math.floor(themeColors.secondary[2] * 255 * 0.2)}) 100%)`;

  return (
    <div
      ref={mountRef}
      className="aurora-background"
      style={{
        position: 'absolute',
        inset: 0,
        zIndex: 0,
        overflow: 'hidden',
        background: backgroundGradient
      }}
    />
  );
};

export default AuroraBackground;
