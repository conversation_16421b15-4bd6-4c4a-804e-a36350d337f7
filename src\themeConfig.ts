export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: {
    primary: string;
    secondary: string;
  };
  button: {
    primary: {
      background: string;
      text: string;
      hover: string;
    };
    secondary: {
      background: string;
      text: string;
      border: string;
      hover: string;
    };
  };
  card: {
    background: string;
    border: string;
    hoverBorder: string;
  };
  icon: string;
  highlight: string;
}

export interface ThemeConfig {
  colors: ThemeColors;
  fontFamily: {
    primary: string;
    secondary: string;
  };
}

const themeConfig: ThemeConfig = {
  colors: {
    // Main theme colors
    primary: '#c41e3a', // Royal Red
    secondary: '#8b0000', // Dark Red
    accent: '#ffd700', // Gold
    background: '#000000', // Black
    
    // Text colors
    text: {
      primary: '#ffffff', // White
      secondary: '#e0e0e0', // Light Gray
    },
    
    // Button styles
    button: {
      primary: {
        background: '#c41e3a', // Royal Red
        text: '#ffffff', // White
        hover: '#8b0000', // Dark Red
      },
      secondary: {
        background: 'transparent',
        text: '#ffffff', // White
        border: 'rgba(196, 30, 58, 0.5)', // Royal Red with opacity
        hover: 'rgba(196, 30, 58, 0.2)', // Royal Red with opacity
      },
    },
    
    // Card styles
    card: {
      background: 'rgba(0, 0, 0, 0.8)', // Black with opacity
      border: 'rgba(196, 30, 58, 0.3)', // Royal Red with opacity
      hoverBorder: 'rgba(196, 30, 58, 0.8)', // Royal Red with opacity
    },
    
    // Icon color
    icon: '#c41e3a', // Royal Red
    
    // Highlight color (for gradients, glows, etc.)
    highlight: '#ffd700', // Gold
  },
  
  // Font families
  fontFamily: {
    primary: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    secondary: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  },
};

export default themeConfig;
