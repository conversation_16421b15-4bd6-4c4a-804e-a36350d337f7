import React, { useEffect, useRef } from 'react';

interface Square {
  x: number;
  y: number;
  vx: number;
  vy: number;
  scale: number;
  rotation: number;
  rotationSpeed: number;
  color: [number, number, number];
  life: number;
  maxLife: number;
  type: 'normal' | 'explosive' | 'magnetic' | 'rainbow';
  energy: number;
  trail: { x: number; y: number; alpha: number }[];
}

class WebGLGameLogic {
  canvas: HTMLCanvasElement;
  gl: WebGLRenderingContext | null = null;
  program: WebGLProgram | null = null;
  uniforms: {
    uOffset: WebGLUniformLocation | null;
    uScale: WebGLUniformLocation | null;
    uRotation: WebGLUniformLocation | null;
    uColor: WebGLUniformLocation | null;
  } = {
    uOffset: null,
    uScale: null,
    uRotation: null,
    uColor: null
  };
  squares: Square[] = [];
  isRunning: boolean = false;
  score: number = 0;
  mouseX: number = 0;
  mouseY: number = 0;
  combo: number = 0;
  maxCombo: number = 0;
  gameTime: number = 0;
  powerMode: boolean = false;
  powerModeTimer: number = 0;
  backgroundHue: number = 0;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.gl = canvas.getContext('webgl', { alpha: false }) || canvas.getContext('experimental-webgl', { alpha: false });
    
    if (!this.gl) {
      console.error('WebGL not supported');
      return;
    }

    this.initShaders();
    this.initBuffers();
    this.initSquares();

    // Add mouse move listener
    canvas.addEventListener('mousemove', (e) => {
      const rect = canvas.getBoundingClientRect();
      this.mouseX = (e.clientX - rect.left) / rect.width * 2 - 1;
      this.mouseY = -((e.clientY - rect.top) / rect.height * 2 - 1);
    });

    // Add click listener to add new squares
    canvas.addEventListener('click', () => {
      if (this.squares.length < 20) {
        this.addSquare(this.mouseX, this.mouseY);
      }
    });
  }

  initShaders() {
    if (!this.gl) return;

    const vsSource = `
      attribute vec4 aVertexPosition;
      uniform vec2 uOffset;
      uniform float uScale;
      uniform float uRotation;
      
      void main() {
        float c = cos(uRotation);
        float s = sin(uRotation);
        mat2 rotation = mat2(c, -s, s, c);
        vec2 rotated = rotation * (aVertexPosition.xy * uScale);
        gl_Position = vec4(rotated + uOffset, 0.0, 1.0);
      }
    `;

    const fsSource = `
      precision mediump float;
      uniform vec3 uColor;
      
      void main() {
        gl_FragColor = vec4(uColor, 1.0);
      }
    `;

    const vertexShader = this.compileShader(vsSource, this.gl.VERTEX_SHADER);
    const fragmentShader = this.compileShader(fsSource, this.gl.FRAGMENT_SHADER);

    if (!vertexShader || !fragmentShader) return;

    this.program = this.gl.createProgram();
    if (!this.program) return;

    this.gl.attachShader(this.program, vertexShader);
    this.gl.attachShader(this.program, fragmentShader);
    this.gl.linkProgram(this.program);

    if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
      console.error('Unable to initialize shader program');
      return;
    }

    this.gl.useProgram(this.program);

    // Get uniform locations
    this.uniforms.uOffset = this.gl.getUniformLocation(this.program, 'uOffset');
    this.uniforms.uScale = this.gl.getUniformLocation(this.program, 'uScale');
    this.uniforms.uRotation = this.gl.getUniformLocation(this.program, 'uRotation');
    this.uniforms.uColor = this.gl.getUniformLocation(this.program, 'uColor');
  }

  compileShader(source: string, type: number) {
    if (!this.gl) return null;

    const shader = this.gl.createShader(type);
    if (!shader) return null;

    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error('Shader compile error:', this.gl.getShaderInfoLog(shader));
      this.gl.deleteShader(shader);
      return null;
    }

    return shader;
  }

  initBuffers() {
    if (!this.gl || !this.program) return;

    const positions = [
      -0.1,  0.1,
       0.1,  0.1,
      -0.1, -0.1,
       0.1, -0.1,
    ];

    const positionBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, positionBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(positions), this.gl.STATIC_DRAW);

    const vertexPosition = this.gl.getAttribLocation(this.program, 'aVertexPosition');
    this.gl.enableVertexAttribArray(vertexPosition);
    this.gl.vertexAttribPointer(vertexPosition, 2, this.gl.FLOAT, false, 0, 0);
  }

  initSquares() {
    // Create initial squares with random properties
    for (let i = 0; i < 5; i++) {
      this.addSquare(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1
      );
    }
  }

  addSquare(x: number, y: number) {
    const square: Square = {
      x,
      y,
      vx: (Math.random() - 0.5) * 0.02,
      vy: (Math.random() - 0.5) * 0.02,
      scale: Math.random() * 0.5 + 0.5,
      rotation: Math.random() * Math.PI * 2,
      rotationSpeed: (Math.random() - 0.5) * 0.1,
      color: [
        Math.random(),
        Math.random(),
        Math.random()
      ]
    };
    this.squares.push(square);
    this.score += 100;
  }

  update() {
    if (!this.isRunning) return;
    
    this.squares.forEach(square => {
      // Update position
      square.x += square.vx;
      square.y += square.vy;

      // Bounce off walls
      if (Math.abs(square.x) > 0.9) square.vx *= -1;
      if (Math.abs(square.y) > 0.9) square.vy *= -1;

      // Update rotation
      square.rotation += square.rotationSpeed;

      // Attract squares to mouse position
      const dx = this.mouseX - square.x;
      const dy = this.mouseY - square.y;
      const dist = Math.sqrt(dx * dx + dy * dy);
      if (dist < 0.5) {
        square.vx += dx * 0.001;
        square.vy += dy * 0.001;
      }
    });
  }

  draw() {
    if (!this.gl || !this.program) return;

    this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
    this.gl.clearColor(0.1, 0.1, 0.1, 1.0);
    this.gl.clear(this.gl.COLOR_BUFFER_BIT);

    // Draw each square
    this.squares.forEach(square => {
      this.gl!.uniform2f(this.uniforms.uOffset!, square.x, square.y);
      this.gl!.uniform1f(this.uniforms.uScale!, square.scale);
      this.gl!.uniform1f(this.uniforms.uRotation!, square.rotation);
      this.gl!.uniform3f(this.uniforms.uColor!, ...square.color);
      this.gl!.drawArrays(this.gl!.TRIANGLE_STRIP, 0, 4);
    });

    // Draw score
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '20px Arial';
      ctx.fillText(`Score: ${this.score}`, 10, 30);
      ctx.fillText('Click to add squares!', 10, 60);
    }
  }

  start() {
    if (!this.gl) return;
    this.isRunning = true;
    this.animate();
  }

  stop() {
    this.isRunning = false;
  }

  animate = () => {
    if (!this.isRunning) return;

    this.update();
    this.draw();
    requestAnimationFrame(this.animate);
  }

  resize(width: number, height: number) {
    if (!this.gl) return;
    this.canvas.width = width;
    this.canvas.height = height;
    this.gl.viewport(0, 0, width, height);
  }
}

interface WebGLGameProps {
  isPreview?: boolean;
}

const WebGLGame: React.FC<WebGLGameProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<WebGLGameLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new WebGLGameLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default WebGLGame;
