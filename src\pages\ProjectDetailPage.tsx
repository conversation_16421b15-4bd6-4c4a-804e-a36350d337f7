import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getProject } from '../services/showcaseService';
import { Project } from '../types/showcase';
import { ArrowLeft, ExternalLink, Tag, Code, Layers } from 'lucide-react';
import Iframe from 'react-iframe';

const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchProject = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const data = await getProject(parseInt(id, 10));
        setProject(data);
        setError(null);
      } catch (err) {
        setError('Failed to load project details. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProject();
  }, [id]);
  
  if (loading) {
    return (
      <div className="bg-black min-h-screen py-20 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }
  
  if (error || !project) {
    return (
      <div className="bg-black min-h-screen py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-10">
            <p className="text-red-500">{error || 'Project not found'}</p>
            <Link to="/showcase" className="text-purple-400 hover:text-purple-300 mt-4 inline-block">
              ← Back to Showcase
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-black min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Link 
          to="/showcase"
          className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Showcase
        </Link>
        
        <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800">
          <div className="p-8">
            <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 mb-8">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{project.name}</h1>
                <div className="flex items-center gap-2 mb-4">
                  <span className="bg-purple-900/50 text-purple-300 text-xs px-2 py-1 rounded">
                    {project.category}
                  </span>
                  <a 
                    href={project.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-purple-400 hover:text-purple-300 text-sm"
                  >
                    Visit Website <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </div>
                <p className="text-gray-400">{project.description}</p>
              </div>
            </div>
            
            <div className="aspect-video w-full bg-black rounded-lg overflow-hidden mb-8">
              <Iframe
                url={project.url}
                width="100%"
                height="100%"
                id={`project-frame-${project.id}`}
                className="border-0"
                display="block"
                position="relative"
                allowFullScreen
              />
            </div>
            
            {project.features && project.features.length > 0 && (
              <div>
                <h2 className="text-xl font-bold text-white mb-4">Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {project.features.map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-4 rounded-lg ${
                        feature.isHighlighted 
                          ? 'bg-purple-900/20 border border-purple-800/50' 
                          : 'bg-gray-800/50 border border-gray-700/50'
                      }`}
                    >
                      <div className="flex items-start">
                        <span className={`mr-3 ${feature.isHighlighted ? 'text-purple-400' : 'text-gray-400'}`}>
                          {feature.isHighlighted ? <Layers className="w-5 h-5" /> : <Code className="w-5 h-5" />}
                        </span>
                        <span className="text-gray-300">{feature.feature}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailPage;
