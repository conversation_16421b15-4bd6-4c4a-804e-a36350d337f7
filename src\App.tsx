// React is used implicitly for JSX
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ThemeSwitcher from './components/ThemeSwitcher';
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import FuturisticHumanoidChatbot from './components/FuturisticHumanoidChatbot';
import HomePage from './pages/HomePage';
import ServicePage from './pages/ServicePage';
import ContactPage from './pages/ContactPage';
import ClientPortfolioPage from './pages/ClientPortfolioPage';
import ShowcasePage from './pages/ShowcasePage';
import ProjectDetailPage from './pages/ProjectDetailPage';
import AdminDashboard from './pages/AdminDashboard';
import TermsOfServicePage from './pages/TermsOfServicePage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import ProductPrivacyPolicyPage from './pages/ProductPrivacyPolicyPage';
import BrowserGaming from './pages/ServicePages/BrowserGaming';
import WebGLCanvas from './pages/ServicePages/WebGLCanvas';
import MultiplayerGames from './pages/ServicePages/MultiplayerGames';
import GameLeads from './pages/ServicePages/GameLeads';
import CMSDevelopment from './pages/ServicePages/CMSDevelopment';
import CloudInfrastructure from './pages/ServicePages/CloudInfrastructure';
import Cybersecurity from './pages/ServicePages/Cybersecurity';
import RemoteITSupport from './pages/ServicePages/RemoteITSupport';
import CustomSoftware from './pages/ServicePages/CustomSoftware';
import DevOpsCICD from './pages/ServicePages/DevOpsCICD';
import DataAnalytics from './pages/ServicePages/DataAnalytics';
import ManagedIT from './pages/ServicePages/ManagedIT';
import DigitalTransformation from './pages/ServicePages/DigitalTransformation';
import NetworkSolutions from './pages/ServicePages/NetworkSolutions';
import EcommerceSolutions from './pages/ServicePages/EcommerceSolutions';
import VDISolutions from './pages/ServicePages/VDISolutions';
import SophosFirewall from './pages/ServicePages/SophosFirewall';
import FortiGateFirewall from './pages/ServicePages/FortiGateFirewall';
import NetgateFirewall from './pages/ServicePages/NetgateFirewall';
import SonicWallFirewall from './pages/ServicePages/SonicWallFirewall';

function App() {
  return (
    <Router>
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        <Navigation />
        <ThemeSwitcher />
        <div className="pt-[80px] md:pt-[128px] lg:pt-[64px] transition-all duration-300">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/clients" element={<ClientPortfolioPage />} />
            <Route path="/showcase" element={<ShowcasePage />} />
            <Route path="/showcase/:id" element={<ProjectDetailPage />} />
            <Route path="/showcase/admin" element={<AdminDashboard />} />
            <Route path="/terms-of-service" element={<TermsOfServicePage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/product-privacy-policy" element={<ProductPrivacyPolicyPage />} />
            <Route path="/services/:id" element={<ServicePage />} />
            <Route path="/services/browser-gaming" element={<BrowserGaming />} />
            <Route path="/services/webgl-canvas" element={<WebGLCanvas />} />
            <Route path="/services/multiplayer-games" element={<MultiplayerGames />} />
            <Route path="/services/game-leads" element={<GameLeads />} />
            <Route path="/services/cms-development" element={<CMSDevelopment />} />
            <Route path="/services/cloud-infrastructure" element={<CloudInfrastructure />} />
            <Route path="/services/cybersecurity" element={<Cybersecurity />} />
            <Route path="/services/remote-it-support" element={<RemoteITSupport />} />
            <Route path="/services/custom-software" element={<CustomSoftware />} />
            <Route path="/services/devops-cicd" element={<DevOpsCICD />} />
            <Route path="/services/data-analytics" element={<DataAnalytics />} />
            <Route path="/services/managed-it" element={<ManagedIT />} />
            <Route path="/services/digital-transformation" element={<DigitalTransformation />} />
            <Route path="/services/network-solutions" element={<NetworkSolutions />} />
            <Route path="/services/ecommerce" element={<EcommerceSolutions />} />
            <Route path="/services/vdi-solutions" element={<VDISolutions />} />
            <Route path="/services/sophos-firewall" element={<SophosFirewall />} />
            <Route path="/services/fortigate-firewall" element={<FortiGateFirewall />} />
            <Route path="/services/netgate-firewall" element={<NetgateFirewall />} />
            <Route path="/services/sonicwall-firewall" element={<SonicWallFirewall />} />
          </Routes>
        </div>
        <Footer />
        <FuturisticHumanoidChatbot />
      </div>
    </Router>
  );
}

export default App;
