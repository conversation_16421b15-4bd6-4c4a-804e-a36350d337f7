import React from 'react';
import { Globe, Code2, Cpu, Target } from 'lucide-react';

interface Service {
  icon: React.ReactNode;
  title: string;
  description: string;
  slug: string;
}

export const services: Service[] = [
  {
    icon: React.createElement(Globe, { className: "w-8 h-8 text-purple-500" }),
    title: "Browser-Based Gaming",
    description: "Cross-platform HTML5 games that run seamlessly in modern browsers without installation",
    slug: "browser-gaming"
  },
  {
    icon: React.createElement(Code2, { className: "w-8 h-8 text-purple-500" }),
    title: "WebGL & Canvas",
    description: "High-performance 2D and 3D game rendering using cutting-edge web technologies",
    slug: "webgl-canvas"
  },
  {
    icon: React.createElement(Cpu, { className: "w-8 h-8 text-purple-500" }),
    title: "Real-Time Multiplayer",
    description: "Engaging multiplayer experiences with WebSocket integration and state synchronization",
    slug: "multiplayer-games"
  },
  {
    icon: React.createElement(Target, { className: "w-8 h-8 text-purple-500" }),
    title: "Game-Based Lead Generation",
    description: "Interactive gaming experiences designed to capture and convert qualified leads",
    slug: "game-leads"
  }
];
