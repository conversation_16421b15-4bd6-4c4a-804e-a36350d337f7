import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Brain, Lightbulb, Workflow, Zap } from 'lucide-react';

const DigitalTransformation = () => {
  const features = [
    {
      icon: <Brain className="text-purple-400" size={24} />,
      title: "Digital Strategy",
      description: "Comprehensive digital transformation roadmap"
    },
    {
      icon: <Lightbulb className="text-purple-400" size={24} />,
      title: "Innovation Planning",
      description: "Cutting-edge technology adoption strategies"
    },
    {
      icon: <Workflow className="text-purple-400" size={24} />,
      title: "Process Optimization",
      description: "Business process digitization and automation"
    },
    {
      icon: <Zap className="text-purple-400" size={24} />,
      title: "Technology Integration",
      description: "Seamless integration of modern technologies"
    }
  ];

  const pricing = [
    {
      name: "Essential",
      price: "Contact Us",
      features: [
        "Basic Digital Assessment",
        "Technology Roadmap",
        "Process Analysis",
        "Basic Training",
        "3 Months Support"
      ]
    },
    {
      name: "Advanced",
      price: "Contact Us",
      recommended: true,
      features: [
        "Comprehensive Assessment",
        "Detailed Strategy",
        "Process Automation",
        "Change Management",
        "Staff Training",
        "12 Months Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Enterprise-wide Strategy",
        "Custom Solutions",
        "Full Digital Integration",
        "Advanced Automation",
        "Ongoing Consulting",
        "Dedicated Support"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Digital Transformation"
      description="Strategic digital transformation consulting for business growth"
      features={features}
      pricing={pricing}
      technologies={['AI/ML', 'Cloud Computing', 'IoT', 'Blockchain', 'RPA', 'Big Data']}
      codeExample={`// Digital Transformation Framework
class DigitalTransformation {
  constructor(organization) {
    this.organization = organization;
    this.currentState = this.assessCurrentState();
    this.targetState = this.defineTargetState();
    this.roadmap = new TransformationRoadmap();
  }

  async planTransformation() {
    const gaps = this.analyzeGaps();
    const priorities = this.prioritizeInitiatives(gaps);
    
    return {
      quickWins: priorities.filter(p => p.effort < 3 && p.impact > 7),
      strategicProjects: priorities.filter(p => p.effort > 7 && p.impact > 7),
      timeline: this.createTimeline(priorities)
    };
  }

  async implementInitiative(initiative) {
    const team = await this.assembleTeam(initiative);
    const plan = await this.createActionPlan(initiative);
    
    return {
      milestones: plan.milestones,
      metrics: this.defineSuccessMetrics(initiative),
      risks: this.assessRisks(initiative)
    };
  }

  monitorProgress(initiative) {
    return {
      kpis: this.trackKPIs(initiative),
      adoption: this.measureAdoption(),
      impact: this.measureBusinessImpact()
    };
  }
}`}
    />
  );
};

export default DigitalTransformation;
