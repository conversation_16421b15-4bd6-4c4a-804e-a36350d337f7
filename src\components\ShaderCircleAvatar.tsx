import React, { useEffect, useRef } from 'react';

interface ShaderCircleAvatarProps {
  isListening?: boolean;
  isSpeaking?: boolean;
  isThinking?: boolean;
  emotion?: string;
  size?: number;
}

const ShaderCircleAvatar: React.FC<ShaderCircleAvatarProps> = ({
  isListening = false,
  isSpeaking = false,
  isThinking = false,
  emotion = 'idle',
  size = 200
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const glRef = useRef<WebGLRenderingContext | null>(null);
  const programRef = useRef<WebGLProgram | null>(null);
  const animationRef = useRef<number | null>(null);
  const timeRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const gl = canvas.getContext('webgl');
    if (!gl) {
      console.error('WebGL not supported');
      return;
    }

    glRef.current = gl;

    // Set canvas size
    canvas.width = size;
    canvas.height = size;
    gl.viewport(0, 0, size, size);

    const vertexShaderSource = `
      attribute vec2 aPosition;
      void main() {
        gl_Position = vec4(aPosition, 0.0, 1.0);
      }
    `;

    const fragmentShaderSource = `
      precision highp float;
      uniform float iTime;
      uniform vec2 iResolution;
      uniform float uIntensity;
      uniform float uSpeed;
      uniform vec3 uColor;

      mat2 rotate2d(float angle){
        float c = cos(angle), s = sin(angle);
        return mat2(c, -s, s, c);
      }

      float variation(vec2 v1, vec2 v2, float strength, float speed) {
        return sin(
            dot(normalize(v1), normalize(v2)) * strength + iTime * speed
        ) / 100.0;
      }

      vec3 paintCircle (vec2 uv, vec2 center, float rad, float width) {
        vec2 diff = center-uv;
        float len = length(diff);

        len += variation(diff, vec2(0.0, 1.0), 5.0 * uIntensity, 2.0 * uSpeed);
        len -= variation(diff, vec2(1.0, 0.0), 5.0 * uIntensity, 2.0 * uSpeed);

        float circle = smoothstep(rad-width, rad, len) - smoothstep(rad, rad+width, len);
        return vec3(circle);
      }

      void main() {
        vec2 uv = gl_FragCoord.xy / iResolution.xy;
        uv.x *= 1.5;
        uv.x -= 0.25;

        vec3 color = vec3(0.0);
        float radius = 0.35;
        vec2 center = vec2(0.5);

        // Main thin line
        color += paintCircle(uv, center, radius, 0.035 * uIntensity);

        // First super thin overlapping line (slightly smaller)
        color += paintCircle(uv, center, radius - 0.018, 0.01 * uIntensity);

        // Second super thin overlapping line (slightly larger)
        color += paintCircle(uv, center, radius + 0.018, 0.005 * uIntensity);

        // Colorize with gradient and custom color
        vec2 v = rotate2d(iTime * uSpeed) * uv;
        color *= uColor * vec3(v.x, v.y, 0.7-v.y*v.x);

        // Paint white circle (now even thinner for highlight)
        color += paintCircle(uv, center, radius, 0.003 * uIntensity);

        gl_FragColor = vec4(color, 1.0);
      }
    `;

    function compileShader(type: number, source: string): WebGLShader | null {
      const shader = gl.createShader(type);
      if (!shader) return null;
      
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }
      return shader;
    }

    const vertexShader = compileShader(gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = compileShader(gl.FRAGMENT_SHADER, fragmentShaderSource);

    if (!vertexShader || !fragmentShader) {
      console.error('Failed to compile shaders');
      return;
    }

    const program = gl.createProgram();
    if (!program) return;

    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program linking error:', gl.getProgramInfoLog(program));
      return;
    }

    gl.useProgram(program);
    programRef.current = program;

    // Fullscreen quad
    const buffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
      -1, -1,
       1, -1,
      -1,  1,
      -1,  1,
       1, -1,
       1,  1,
    ]), gl.STATIC_DRAW);

    const aPosition = gl.getAttribLocation(program, 'aPosition');
    gl.enableVertexAttribArray(aPosition);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);

    // Start animation
    const render = (time: number) => {
      if (!gl || !programRef.current) return;

      timeRef.current = time * 0.001;

      // Get uniform locations
      const iTimeLoc = gl.getUniformLocation(programRef.current, 'iTime');
      const iResLoc = gl.getUniformLocation(programRef.current, 'iResolution');
      const uIntensityLoc = gl.getUniformLocation(programRef.current, 'uIntensity');
      const uSpeedLoc = gl.getUniformLocation(programRef.current, 'uSpeed');
      const uColorLoc = gl.getUniformLocation(programRef.current, 'uColor');

      // Set uniforms based on state
      let intensity = 1.0;
      let speed = 1.0;
      let color = [1.0, 1.0, 1.0]; // Default white

      if (isListening) {
        intensity = 1.5;
        speed = 2.0;
        color = [0.3, 0.8, 1.0]; // Blue for listening
      } else if (isSpeaking) {
        intensity = 2.0;
        speed = 1.5;
        color = [0.2, 1.0, 0.4]; // Green for speaking
      } else if (isThinking) {
        intensity = 1.2;
        speed = 0.8;
        color = [1.0, 0.6, 0.2]; // Orange for thinking
      } else {
        // Idle state with subtle animation
        color = [0.6, 0.4, 1.0]; // Purple for idle
      }

      gl.uniform1f(iTimeLoc, timeRef.current);
      gl.uniform2f(iResLoc, size, size);
      gl.uniform1f(uIntensityLoc, intensity);
      gl.uniform1f(uSpeedLoc, speed);
      gl.uniform3f(uColorLoc, color[0], color[1], color[2]);

      gl.drawArrays(gl.TRIANGLES, 0, 6);
      animationRef.current = requestAnimationFrame(render);
    };

    animationRef.current = requestAnimationFrame(render);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [size, isListening, isSpeaking, isThinking, emotion]);

  return (
    <div 
      className="shader-circle-avatar"
      style={{
        width: size,
        height: size,
        borderRadius: '50%',
        overflow: 'hidden',
        background: '#111',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: 'block'
        }}
      />
    </div>
  );
};

export default ShaderCircleAvatar;
