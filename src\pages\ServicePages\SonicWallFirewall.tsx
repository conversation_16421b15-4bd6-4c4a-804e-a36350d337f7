import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Shield, Zap, Cloud, Lock, Network } from 'lucide-react';

const SonicWallFirewall = () => {
  const features = [
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Capture ATP",
      description: "Multi-engine sandbox for real-time breach detection and prevention"
    },
    {
      icon: <Zap className="text-purple-400" size={24} />,
      title: "Real-Time Protection",
      description: "Deep memory inspection for zero-day threat prevention"
    },
    {
      icon: <Cloud className="text-purple-400" size={24} />,
      title: "Cloud Edge",
      description: "Unified security management across all network boundaries"
    },
    {
      icon: <Network className="text-purple-400" size={24} />,
      title: "SD-Branch",
      description: "Integrated networking and security for branch offices"
    }
  ];

  const pricing = [
    {
      name: "TZ Series",
      price: "Contact Us",
      features: [
        "Essential Security Features",
        "Basic Threat Protection",
        "SSL/TLS Inspection",
        "Basic VPN",
        "8x5 Support"
      ]
    },
    {
      name: "NSa Series",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Threat Protection",
        "Capture ATP",
        "Cloud Management",
        "SD-WAN Features",
        "24/7 Support",
        "Priority Response"
      ]
    },
    {
      name: "NSsp Series",
      price: "Contact Us",
      features: [
        "Enterprise Performance",
        "Custom Integration",
        "High Availability",
        "Advanced SD-Branch",
        "Dedicated Support",
        "Custom SLA"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="SonicWall Firewall Solutions"
      description="Next-generation security with real-time breach detection and prevention"
      features={features}
      pricing={pricing}
      technologies={['Capture ATP', 'RTDMI', 'SD-Branch', 'TLS 1.3', 'Zero Trust', 'VPN']}
      codeExample={`// SonicWall Security Configuration Manager
class SonicWallManager {
  constructor() {
    this.securityServices = new Map();
    this.zones = new Map();
    this.policies = [];
    this.captureATP = null;
  }

  async configureCaptureATP() {
    // Initialize Capture ATP service
    this.captureATP = {
      engines: ['sandbox', 'rtdmi', 'ai'],
      realTimeInspection: true,
      cloudIntelligence: true
    };

    // Configure scanning profiles
    const profiles = {
      standard: {
        fileTypes: ['exe', 'dll', 'pdf', 'office'],
        behavior: {
          duration: 60,
          depth: 'thorough'
        }
      },
      intensive: {
        fileTypes: ['all'],
        behavior: {
          duration: 120,
          depth: 'extreme'
        }
      }
    };

    await this.applyCaptureConfig({
      service: this.captureATP,
      profiles,
      policy: {
        action: 'block_until_verdict',
        exceptions: this.getWhitelistedApplications()
      }
    });

    return {
      status: 'configured',
      activeEngines: this.captureATP.engines,
      profiles: Object.keys(profiles)
    };
  }

  async implementZeroTrust() {
    const zeroTrustConfig = {
      authentication: {
        methods: ['certificate', 'biometric', 'mfa'],
        sso: true
      },
      access: {
        deviceCompliance: true,
        locationAwareness: true,
        contextualAccess: true
      },
      inspection: {
        ssl: true,
        deepPacket: true,
        behavioral: true
      }
    };

    // Apply Zero Trust policies
    await this.applyZeroTrustConfig(zeroTrustConfig);
    
    return {
      status: 'implemented',
      policies: this.policies.length,
      securityZones: this.zones.size
    };
  }

  async configureSdBranch() {
    const branchConfig = {
      networking: {
        sdWan: true,
        qos: true,
        failover: true
      },
      security: {
        unified: true,
        zoneDefense: true,
        threatPrevention: true
      },
      management: {
        central: true,
        analytics: true,
        reporting: true
      }
    };

    // Deploy SD-Branch configuration
    await this.applySdBranchConfig(branchConfig);
    
    return {
      status: 'configured',
      features: Object.keys(branchConfig),
      timestamp: new Date()
    };
  }
}`}
    />
  );
};

export default SonicWallFirewall;
