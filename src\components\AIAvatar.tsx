import React, { useState, useEffect } from 'react';
import './AIAvatar.css';

export type AIEmotion = 'idle' | 'thinking' | 'speaking' | 'happy' | 'curious' | 'excited' | 'listening';

interface AIAvatarProps {
  emotion: AIEmotion;
  isListening: boolean;
  isSpeaking: boolean;
  isThinking: boolean;
  voiceLevel?: number; // 0-1 for voice visualization
}

const AIAvatar: React.FC<AIAvatarProps> = ({ 
  emotion, 
  isListening, 
  isSpeaking, 
  isThinking,
  voiceLevel = 0 
}) => {
  const [blinkState, setBlinkState] = useState(false);
  const [breathePhase, setBreathePhase] = useState(0);

  // Blinking animation
  useEffect(() => {
    const blinkInterval = setInterval(() => {
      setBlinkState(true);
      setTimeout(() => setBlinkState(false), 150);
    }, 2000 + Math.random() * 3000);

    return () => clearInterval(blinkInterval);
  }, []);

  // Breathing animation
  useEffect(() => {
    const breatheInterval = setInterval(() => {
      setBreathePhase(prev => (prev + 1) % 100);
    }, 50);

    return () => clearInterval(breatheInterval);
  }, []);

  const getEmotionColors = () => {
    switch (emotion) {
      case 'thinking':
        return {
          primary: '#a855f7',
          secondary: '#c084fc',
          glow: '#a855f7'
        };
      case 'speaking':
        return {
          primary: '#06b6d4',
          secondary: '#67e8f9',
          glow: '#06b6d4'
        };
      case 'happy':
        return {
          primary: '#f59e0b',
          secondary: '#fbbf24',
          glow: '#f59e0b'
        };
      case 'excited':
        return {
          primary: '#ef4444',
          secondary: '#f87171',
          glow: '#ef4444'
        };
      case 'curious':
        return {
          primary: '#10b981',
          secondary: '#34d399',
          glow: '#10b981'
        };
      case 'listening':
        return {
          primary: '#8b5cf6',
          secondary: '#a78bfa',
          glow: '#8b5cf6'
        };
      default:
        return {
          primary: '#3b82f6',
          secondary: '#60a5fa',
          glow: '#3b82f6'
        };
    }
  };

  const colors = getEmotionColors();
  const breatheScale = 1 + Math.sin(breathePhase * 0.1) * 0.02;
  const voiceScale = 1 + (voiceLevel * 0.3);

  return (
    <div className="ai-avatar-container">
      {/* Particle Background */}
      <div className="ai-particles">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              '--delay': `${i * 0.2}s`,
              '--color': colors.secondary,
            } as React.CSSProperties}
          />
        ))}
      </div>

      {/* Main Avatar */}
      <div 
        className={`ai-avatar ${emotion} ${isListening ? 'listening' : ''} ${isSpeaking ? 'speaking' : ''}`}
        style={{
          '--primary-color': colors.primary,
          '--secondary-color': colors.secondary,
          '--glow-color': colors.glow,
          '--breathe-scale': breatheScale,
          '--voice-scale': isSpeaking ? voiceScale : 1,
        } as React.CSSProperties}
      >
        {/* Outer Glow Ring */}
        <div className="avatar-glow-ring" />
        
        {/* Middle Ring */}
        <div className="avatar-middle-ring" />
        
        {/* Core Avatar */}
        <div className="avatar-core">
          {/* Face Container */}
          <div className="avatar-face">
            {/* Eyes */}
            <div className="avatar-eyes">
              <div className={`eye left-eye ${blinkState ? 'blink' : ''}`}>
                <div className="eye-pupil" />
                <div className="eye-shine" />
              </div>
              <div className={`eye right-eye ${blinkState ? 'blink' : ''}`}>
                <div className="eye-pupil" />
                <div className="eye-shine" />
              </div>
            </div>

            {/* Mouth */}
            <div className={`avatar-mouth ${emotion}`}>
              {isSpeaking && (
                <div className="voice-waves">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className="voice-wave"
                      style={{
                        '--delay': `${i * 0.1}s`,
                        '--intensity': voiceLevel,
                      } as React.CSSProperties}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Thinking Indicator */}
            {isThinking && (
              <div className="thinking-dots">
                <div className="dot" />
                <div className="dot" />
                <div className="dot" />
              </div>
            )}
          </div>

          {/* Neural Network Effect */}
          <div className="neural-network">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="neural-line"
                style={{
                  '--angle': `${i * 45}deg`,
                  '--delay': `${i * 0.3}s`,
                } as React.CSSProperties}
              />
            ))}
          </div>
        </div>

        {/* Voice Visualization */}
        {isSpeaking && (
          <div className="voice-visualization">
            {[...Array(12)].map((_, i) => (
              <div
                key={i}
                className="voice-bar"
                style={{
                  '--delay': `${i * 0.05}s`,
                  '--intensity': voiceLevel,
                  '--angle': `${i * 30}deg`,
                } as React.CSSProperties}
              />
            ))}
          </div>
        )}

        {/* Listening Pulse */}
        {isListening && (
          <div className="listening-pulse">
            <div className="pulse-ring" />
            <div className="pulse-ring" />
            <div className="pulse-ring" />
          </div>
        )}
      </div>

      {/* Holographic Grid */}
      <div className="holographic-grid">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="grid-line"
            style={{
              '--delay': `${i * 0.2}s`,
            } as React.CSSProperties}
          />
        ))}
      </div>

      {/* Status Indicator */}
      <div className="status-indicator">
        <div className="status-text">
          {isThinking ? 'PROCESSING...' : 
           isSpeaking ? 'SPEAKING' : 
           isListening ? 'LISTENING' : 
           'READY'}
        </div>
        <div className="status-bar">
          <div className="status-fill" />
        </div>
      </div>
    </div>
  );
};

export default AIAvatar;
