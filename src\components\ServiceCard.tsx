import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

interface ServiceProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  slug: string;
}

const ServiceCard: React.FC<ServiceProps> = ({ icon, title, description, slug }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.05 }}
      className="p-6 bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300 group"
    >
      <div className="w-12 h-12 rounded-full bg-purple-600/10 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-2 text-glow">{title}</h3>
      <p className="text-gray-400 mb-4 group-hover:text-gray-300 transition-colors duration-300">{description}</p>
      <Link 
        to={`/services/${slug}`}
        className="inline-flex items-center text-purple-400 hover:text-purple-300 font-medium group-hover:gap-2 transition-all duration-300"
      >
        Learn More <ArrowRight className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300" size={18} />
      </Link>
    </motion.div>
  );
};

export default ServiceCard;