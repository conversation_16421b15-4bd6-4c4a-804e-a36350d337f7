import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Settings, Server, Shield, Bell } from 'lucide-react';

const ManagedIT = () => {
  const features = [
    {
      icon: <Server className="text-purple-400" size={24} />,
      title: "Infrastructure Management",
      description: "Complete IT infrastructure monitoring and maintenance"
    },
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Security Management",
      description: "Comprehensive security and patch management"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "System Administration",
      description: "Proactive system maintenance and optimization"
    },
    {
      icon: <Bell className="text-purple-400" size={24} />,
      title: "24/7 Monitoring",
      description: "Continuous monitoring and alert management"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Basic Infrastructure Monitoring",
        "Essential Security",
        "System Updates",
        "Email Support",
        "Business Hours Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Infrastructure Management",
        "Proactive Maintenance",
        "Security Management",
        "24/7 Support",
        "Priority Response",
        "Performance Optimization"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom IT Management",
        "Dedicated Support Team",
        "Custom SLA",
        "Strategic Planning",
        "Technology Consulting",
        "Executive Reporting"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Managed IT Services"
      description="End-to-end IT infrastructure management and support services"
      features={features}
      pricing={pricing}
      technologies={['VMware', 'Microsoft Azure', 'AWS', 'ServiceNow', 'Nagios', 'SolarWinds']}
      codeExample={`// Infrastructure Monitoring System
class InfrastructureMonitor {
  constructor() {
    this.alerts = [];
    this.metrics = new Map();
    this.thresholds = this.loadThresholds();
  }

  async monitorSystem() {
    const metrics = await this.collectMetrics();
    
    metrics.forEach((value, key) => {
      if (this.isThresholdExceeded(key, value)) {
        this.createAlert({
          metric: key,
          value: value,
          threshold: this.thresholds.get(key),
          timestamp: new Date()
        });
      }
      
      this.metrics.set(key, {
        value,
        timestamp: new Date(),
        trend: this.calculateTrend(key, value)
      });
    });
  }

  async handleAlert(alert) {
    const severity = this.calculateSeverity(alert);
    
    if (severity === 'critical') {
      await this.notifyTeam(alert);
      await this.initiateIncidentResponse(alert);
    }
    
    await this.logAlert(alert);
  }
}`}
    />
  );
};

export default ManagedIT;
