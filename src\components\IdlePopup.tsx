import React, { useState, useEffect } from 'react';

interface IdlePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const IdlePopup: React.FC<IdlePopupProps> = ({ isOpen, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    organization: '',
    email: '',
    phone: '',
    description: ''
  });

  useEffect(() => {
    if (isOpen) {
      const timeoutId = setTimeout(() => setIsVisible(true), 50);
      return () => clearTimeout(timeoutId);
    } else {
      setIsVisible(false);
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black transition-opacity duration-300 z-50 ${isVisible ? 'bg-opacity-80 backdrop-blur-sm' : 'bg-opacity-0'}`}>
      <div className={`fixed inset-0 flex items-center justify-center p-4 transition-all duration-300 ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
        <div className="bg-[#0A0A0A] border border-purple-800 rounded-lg p-6 max-w-2xl w-full relative shadow-lg shadow-purple-900/20">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-purple-400 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Left side - Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-white mb-4">Get Started</h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      type="text"
                      placeholder="First Name"
                      className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                      value={formData.firstName}
                      onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                    />
                  </div>
                  <div>
                    <input
                      type="text"
                      placeholder="Last Name"
                      className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                      value={formData.lastName}
                      onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                    />
                  </div>
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Business / Organization"
                    className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                    value={formData.organization}
                    onChange={(e) => setFormData({...formData, organization: e.target.value})}
                  />
                </div>
                <div>
                  <input
                    type="email"
                    placeholder="Email"
                    className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />
                </div>
                <div>
                  <input
                    type="tel"
                    placeholder="Phone"
                    className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />
                </div>
                <div>
                  <textarea
                    placeholder="Project Description"
                    rows={4}
                    className="w-full bg-[#1E1E1E] border border-[#2D2D2D] rounded px-3 py-2 text-white placeholder-gray-500 focus:border-purple-500 focus:outline-none transition-colors"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-600 to-purple-400 text-white py-2 px-4 rounded hover:from-purple-500 hover:to-purple-300 transition-all duration-300 transform hover:scale-[1.02]"
                >
                  Submit Request
                </button>
              </form>
            </div>

            {/* Right side - What Happens Next */}
            <div className="bg-gradient-to-br from-[#1E1E1E] to-[#0A0A0A] p-6 rounded-lg text-white border border-purple-900">
              <h2 className="text-2xl font-bold mb-6">What Happens Next?</h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-3">
                  <svg className="w-6 h-6 mt-1 flex-shrink-0 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <p className="text-gray-300">Our sales whiz contacts you shortly after diving into your business needs.</p>
                </div>
                <div className="flex items-start space-x-3">
                  <svg className="w-6 h-6 mt-1 flex-shrink-0 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <p className="text-gray-300">A swift NDA is inked, securing your data with utmost discretion.</p>
                </div>
                <div className="flex items-start space-x-3">
                  <svg className="w-6 h-6 mt-1 flex-shrink-0 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <p className="text-gray-300">Our pre-sales wizard lays out project costs and a rough timeline, keeping you in the loop.</p>
                </div>
                <div className="flex items-start space-x-3">
                  <svg className="w-6 h-6 mt-1 flex-shrink-0 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <p className="text-gray-300">We kickstart your journey to success with a detailed project roadmap that outlines milestones, key deliverables, and anticipated project phases.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdlePopup;
