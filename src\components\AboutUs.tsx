import React from 'react';
import { Code, Gamepad2, Users, Rocket } from 'lucide-react';
import { motion } from 'framer-motion';
import ParticleEffect from './ParticleEffect';

const AboutUs = () => {
  const features = [
    {
      icon: <Gamepad2 className="text-purple-400" size={24} />,
      title: "Game Development Experts",
      description: "Specialized in creating immersive browser-based gaming experiences using cutting-edge web technologies."
    },
    {
      icon: <Code className="text-purple-400" size={24} />,
      title: "Technical Excellence",
      description: "Mastery in WebGL, Canvas, and modern web frameworks to deliver high-performance gaming solutions."
    },
    {
      icon: <Users className="text-purple-400" size={24} />,
      title: "Collaborative Approach",
      description: "Working closely with clients to transform their gaming visions into engaging digital experiences."
    },
    {
      icon: <Rocket className="text-purple-400" size={24} />,
      title: "Innovation Driven",
      description: "Constantly pushing boundaries in browser-based gaming technology and interactive experiences."
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-purple-900/20 to-black relative overflow-hidden">
      <ParticleEffect />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-mono font-bold mb-6 glitch-text">
            About Spirelab Solutions
          </h2>
          <p className="text-gray-400 max-w-3xl mx-auto font-mono">
            We are pioneers in browser-based game development, crafting immersive gaming experiences that push the boundaries of web technology. Our passion for innovation drives us to create engaging solutions that captivate and inspire.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="h-full p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg transition-all duration-300 hover:bg-white/10 hover:border-purple-500/50">
                <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center mb-6">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-mono font-semibold mb-4">{feature.title}</h3>
                <p className="text-gray-400 font-mono text-sm">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-16"
        >
          <div className="terminal-window">
            <div className="terminal-header">
              <div className="terminal-dot bg-red-500"></div>
              <div className="terminal-dot bg-yellow-500"></div>
              <div className="terminal-dot bg-green-500"></div>
              <span className="text-xs ml-2 font-mono">about.txt</span>
            </div>
            <div className="p-6">
              <p className="text-gray-400 font-mono leading-relaxed">
                With years of experience in game development and web technologies, we specialize in creating seamless, cross-platform gaming experiences that run directly in the browser. Our commitment to excellence and innovation has made us a trusted partner for businesses seeking to leverage the power of interactive gaming solutions.
              </p>
              <div className="mt-6 flex flex-wrap gap-4">
                {['WebGL', 'Canvas', 'React', 'Three.js', 'Node.js', 'TypeScript'].map((tech, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="px-4 py-2 bg-white/5 rounded-full border border-white/10 text-sm font-mono"
                  >
                    {tech}
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutUs;
