.stat-particles-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 5;
}

.stat-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  text-shadow: 0 0 4px rgba(255, 50, 80, 0.6);
  padding: 2px 4px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 50, 80, 0.3);
  backdrop-filter: blur(1px);
  white-space: nowrap;
  transform: translate(-50%, -50%) scale(0.55);
  z-index: 5;
  pointer-events: none;
  transition: transform 0.2s ease-out;
}

.stat-particle:hover {
  transform: translate(-50%, -50%) scale(0.6);
}

/* Pulse animation for particles */
@keyframes statParticlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 50, 80, 0.4);
  }
  70% {
    box-shadow: 0 0 0 3px rgba(255, 50, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 50, 80, 0);
  }
}

/* Apply the pulse animation to some particles */
.stat-particle:nth-child(3n) {
  animation: statParticlePulse 2s infinite;
}

/* Add subtle rotation to some particles */
.stat-particle:nth-child(5n) {
  animation: statParticleRotate 6s infinite linear;
}

@keyframes statParticleRotate {
  from {
    transform: translate(-50%, -50%) scale(0.55) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) scale(0.55) rotate(360deg);
  }
}

/* Add glow effect to some particles */
.stat-particle:nth-child(7n) {
  box-shadow: 0 0 8px rgba(255, 50, 80, 0.6);
}
