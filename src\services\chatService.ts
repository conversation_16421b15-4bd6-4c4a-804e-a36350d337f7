import { ChatCompletionMessageParam } from 'openai/resources';

interface ChatResponse {
  message: string;
  error?: string;
}

class ChatService {
  private apiUrl: string;

  constructor() {
    this.apiUrl = `${import.meta.env.VITE_API_HOSTNAME}/api/chat/message`;
  }

  async sendMessage(messages: ChatCompletionMessageParam[]): Promise<ChatResponse> {
    try {
      // Send messages to our server API endpoint
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API Error:', errorData);
        return { 
          message: '', 
          error: `Error: ${response.status} - ${errorData.error || 'Unknown error'}` 
        };
      }

      const data = await response.json();
      return { message: data.message };
    } catch (error) {
      console.error('Chat service error:', error);
      return { message: '', error: `Error: ${(error as Error).message}` };
    }
  }
}

export default new ChatService();
