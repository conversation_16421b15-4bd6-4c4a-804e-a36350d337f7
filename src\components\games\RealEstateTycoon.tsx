import React, { useEffect, useRef } from 'react';

interface Property {
  id: number;
  x: number;
  y: number;
  type: 'residential' | 'commercial' | 'industrial' | 'luxury' | 'office' | 'retail';
  price: number;
  value: number;
  owned: boolean;
  income: number;
  level: number;
  condition: 'poor' | 'fair' | 'good' | 'excellent';
  tenants: number;
  maxTenants: number;
  animationFrame: number;
  constructionProgress: number;
  isUnderConstruction: boolean;
}

interface GameState {
  money: number;
  properties: Property[];
  selectedProperty: Property | null;
  gameTime: number;
  gameOver: boolean;
  totalIncome: number;
  reputation: number;
  marketTrend: 'bull' | 'bear' | 'stable';
  seasonalMultiplier: number;
  cityPopulation: number;
  economicIndex: number;
  weatherCondition: 'sunny' | 'cloudy' | 'rainy' | 'snowy';
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
}

class RealEstateTycoonLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  state: GameState;
  isRunning: boolean = false;
  lastIncomeUpdate: number = 0;
  propertyIdCounter: number = 1;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    this.state = {
      money: 75000,
      properties: this.generateProperties(),
      selectedProperty: null,
      gameTime: 240,
      gameOver: false,
      totalIncome: 0,
      reputation: 50,
      marketTrend: 'stable',
      seasonalMultiplier: 1.0,
      cityPopulation: 100000,
      economicIndex: 100,
      weatherCondition: 'sunny',
      timeOfDay: 'morning'
    };

    canvas.addEventListener('click', this.handleClick);
    
    // Start game timer
    setInterval(() => {
      if (this.isRunning && !this.state.gameOver) {
        this.state.gameTime--;
        if (this.state.gameTime <= 0) {
          this.state.gameOver = true;
        }
      }
    }, 1000);

    // Income generation
    setInterval(() => {
      if (this.isRunning && !this.state.gameOver) {
        this.generateIncome();
      }
    }, 2000);
  }

  generateProperties(): Property[] {
    const properties: Property[] = [];
    const types: Property['type'][] = ['residential', 'commercial', 'industrial', 'luxury', 'office', 'retail'];
    const conditions: Property['condition'][] = ['poor', 'fair', 'good', 'excellent'];

    for (let i = 0; i < 15; i++) {
      const x = 60 + (i % 5) * 110;
      const y = 80 + Math.floor(i / 5) * 90;
      const type = types[Math.floor(Math.random() * types.length)];
      const condition = conditions[Math.floor(Math.random() * conditions.length)];

      let basePrice: number;
      let baseIncome: number;
      let maxTenants: number;

      switch (type) {
        case 'residential':
          basePrice = 25000 + Math.random() * 35000;
          baseIncome = 250 + Math.random() * 350;
          maxTenants = 4 + Math.floor(Math.random() * 8);
          break;
        case 'commercial':
          basePrice = 50000 + Math.random() * 70000;
          baseIncome = 600 + Math.random() * 800;
          maxTenants = 2 + Math.floor(Math.random() * 6);
          break;
        case 'industrial':
          basePrice = 80000 + Math.random() * 100000;
          baseIncome = 1000 + Math.random() * 1500;
          maxTenants = 1 + Math.floor(Math.random() * 3);
          break;
        case 'luxury':
          basePrice = 150000 + Math.random() * 200000;
          baseIncome = 2000 + Math.random() * 3000;
          maxTenants = 1 + Math.floor(Math.random() * 4);
          break;
        case 'office':
          basePrice = 60000 + Math.random() * 90000;
          baseIncome = 800 + Math.random() * 1200;
          maxTenants = 3 + Math.floor(Math.random() * 10);
          break;
        case 'retail':
          basePrice = 40000 + Math.random() * 60000;
          baseIncome = 500 + Math.random() * 700;
          maxTenants = 2 + Math.floor(Math.random() * 8);
          break;
      }

      // Condition affects price and income
      const conditionMultipliers = { poor: 0.7, fair: 0.85, good: 1.0, excellent: 1.3 };
      const multiplier = conditionMultipliers[condition];

      properties.push({
        id: this.propertyIdCounter++,
        x,
        y,
        type,
        price: Math.round(basePrice * multiplier),
        value: Math.round(basePrice * multiplier),
        owned: false,
        income: Math.round(baseIncome * multiplier),
        level: 1,
        condition,
        tenants: 0,
        maxTenants,
        animationFrame: 0,
        constructionProgress: 0,
        isUnderConstruction: false
      });
    }

    return properties;
  }

  handleClick = (e: MouseEvent) => {
    if (!this.isRunning || this.state.gameOver) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    // Check property clicks
    this.state.properties.forEach(property => {
      if (x > property.x - 40 && x < property.x + 40 &&
          y > property.y - 30 && y < property.y + 30) {
        this.handlePropertyClick(property);
      }
    });

    // Check action buttons in property details panel
    if (this.state.selectedProperty) {
      const panelY = this.height - 130;
      const buttonY = panelY + 80;

      if (y > buttonY && y < buttonY + 25) {
        const prop = this.state.selectedProperty;

        // Buy button
        if (x > 25 && x < 105 && !prop.owned && this.state.money >= prop.price) {
          this.buyProperty();
        }
        // Upgrade button
        else if (x > 115 && x < 215 && prop.owned && !prop.isUnderConstruction) {
          this.upgradeProperty();
        }
        // Sell button
        else if (x > 225 && x < 325 && prop.owned) {
          this.sellProperty();
        }
        // Renovate button
        else if (x > 335 && x < 435 && prop.owned && prop.condition !== 'excellent') {
          this.renovateProperty();
        }
      }
    }
  };

  handlePropertyClick(property: Property) {
    this.state.selectedProperty = property;
  }

  buyProperty() {
    const property = this.state.selectedProperty;
    if (!property || property.owned || this.state.money < property.price) return;

    this.state.money -= property.price;
    property.owned = true;
    property.tenants = Math.floor(property.maxTenants * 0.3); // Start with some tenants
    this.state.reputation += 8;
    this.state.cityPopulation += 200; // New properties attract residents
    this.state.selectedProperty = null;
  }

  upgradeProperty() {
    const property = this.state.selectedProperty;
    if (!property || !property.owned || property.isUnderConstruction) return;

    const upgradeCost = Math.round(property.price * 0.4 * property.level);
    if (this.state.money < upgradeCost) return;

    this.state.money -= upgradeCost;
    property.isUnderConstruction = true;
    property.constructionProgress = 0;
    this.state.reputation += 3;
    this.state.cityPopulation += 500; // Upgrades attract more people
  }

  renovateProperty() {
    const property = this.state.selectedProperty;
    if (!property || !property.owned || property.condition === 'excellent') return;

    const renovateCost = Math.round(property.price * 0.2);
    if (this.state.money < renovateCost) return;

    this.state.money -= renovateCost;

    // Improve condition
    const conditions: Property['condition'][] = ['poor', 'fair', 'good', 'excellent'];
    const currentIndex = conditions.indexOf(property.condition);
    if (currentIndex < conditions.length - 1) {
      property.condition = conditions[currentIndex + 1];
      property.value *= 1.15;
      property.income *= 1.1;
      property.maxTenants += 1;
    }

    this.state.reputation += 5;
  }

  sellProperty() {
    const property = this.state.selectedProperty;
    if (!property || !property.owned) return;

    const sellPrice = Math.round(property.value * 0.85); // Sell at 85% of current value
    this.state.money += sellPrice;
    property.owned = false;
    property.level = 1;
    property.tenants = 0;
    property.isUnderConstruction = false;
    property.constructionProgress = 0;

    // Reset to original stats
    const originalIncome = property.income / Math.pow(1.4, property.level - 1);
    property.income = originalIncome;
    property.value = property.price;

    this.state.selectedProperty = null;
  }

  generateIncome() {
    let totalIncome = 0;
    this.state.properties.forEach(property => {
      if (property.owned && !property.isUnderConstruction) {
        // Base income affected by tenants, condition, and market factors
        let income = property.income * (property.tenants / property.maxTenants);

        // Condition affects income
        const conditionMultipliers = { poor: 0.7, fair: 0.85, good: 1.0, excellent: 1.2 };
        income *= conditionMultipliers[property.condition];

        // Market trend affects income
        const trendMultipliers = { bull: 1.1, bear: 0.9, stable: 1.0 };
        income *= trendMultipliers[this.state.marketTrend];

        // Economic index affects income
        income *= (this.state.economicIndex / 100);

        // Weather affects certain property types
        if (property.type === 'retail' && this.state.weatherCondition === 'rainy') {
          income *= 0.8; // People shop less in rain
        } else if (property.type === 'luxury' && this.state.weatherCondition === 'sunny') {
          income *= 1.1; // Luxury properties do better in good weather
        }

        totalIncome += income;
        this.state.money += income;
      }
    });
    this.state.totalIncome += totalIncome;
  }

  update() {
    if (!this.isRunning || this.state.gameOver) return;

    // Update animations
    this.state.properties.forEach(property => {
      property.animationFrame += 0.05;

      // Update construction progress
      if (property.isUnderConstruction) {
        property.constructionProgress += 0.5;
        if (property.constructionProgress >= 100) {
          property.isUnderConstruction = false;
          property.constructionProgress = 0;
          property.level++;
          property.income *= 1.4;
          property.value *= 1.3;
        }
      }
    });

    // Update time of day cycle
    const timeProgress = (240 - this.state.gameTime) / 60;
    if (timeProgress < 1) this.state.timeOfDay = 'morning';
    else if (timeProgress < 2) this.state.timeOfDay = 'afternoon';
    else if (timeProgress < 3) this.state.timeOfDay = 'evening';
    else this.state.timeOfDay = 'night';

    // Weather changes
    if (Math.random() < 0.005) {
      const weathers: GameState['weatherCondition'][] = ['sunny', 'cloudy', 'rainy', 'snowy'];
      this.state.weatherCondition = weathers[Math.floor(Math.random() * weathers.length)];
    }

    // Market fluctuations
    if (Math.random() < 0.008) {
      const trends: GameState['marketTrend'][] = ['bull', 'bear', 'stable'];
      this.state.marketTrend = trends[Math.floor(Math.random() * trends.length)];
    }

    // Economic changes
    if (Math.random() < 0.01) {
      this.state.economicIndex += (Math.random() - 0.5) * 10;
      this.state.economicIndex = Math.max(50, Math.min(150, this.state.economicIndex));
    }

    // Property value fluctuations based on market conditions
    if (Math.random() < 0.02) {
      this.state.properties.forEach(property => {
        let change = (Math.random() - 0.5) * 0.05;

        // Market trend affects change
        if (this.state.marketTrend === 'bull') change += 0.02;
        else if (this.state.marketTrend === 'bear') change -= 0.02;

        // Economic index affects change
        change += (this.state.economicIndex - 100) / 10000;

        property.value *= (1 + change);
        property.price *= (1 + change);
      });
    }

    // Tenant management
    this.state.properties.forEach(property => {
      if (property.owned && !property.isUnderConstruction) {
        // Tenants move in/out based on property condition and market
        if (Math.random() < 0.01) {
          if (property.tenants < property.maxTenants && Math.random() < 0.7) {
            property.tenants++;
          } else if (property.tenants > 0 && Math.random() < 0.3) {
            property.tenants--;
          }
        }
      }
    });
  }

  drawBackground() {
    // Sky gradient based on time of day and weather
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);

    switch (this.state.timeOfDay) {
      case 'morning':
        if (this.state.weatherCondition === 'sunny') {
          gradient.addColorStop(0, '#87CEEB');
          gradient.addColorStop(1, '#E0F6FF');
        } else {
          gradient.addColorStop(0, '#708090');
          gradient.addColorStop(1, '#D3D3D3');
        }
        break;
      case 'afternoon':
        gradient.addColorStop(0, '#4169E1');
        gradient.addColorStop(1, '#87CEFA');
        break;
      case 'evening':
        gradient.addColorStop(0, '#FF6347');
        gradient.addColorStop(1, '#FFE4B5');
        break;
      case 'night':
        gradient.addColorStop(0, '#191970');
        gradient.addColorStop(1, '#483D8B');
        break;
    }

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw weather effects
    this.drawWeatherEffects();

    // Draw city background
    this.drawCityBackground();

    // Draw roads and infrastructure
    this.drawInfrastructure();
  }

  drawWeatherEffects() {
    switch (this.state.weatherCondition) {
      case 'rainy':
        this.ctx.strokeStyle = 'rgba(173, 216, 230, 0.6)';
        this.ctx.lineWidth = 1;
        for (let i = 0; i < 100; i++) {
          const x = Math.random() * this.width;
          const y = Math.random() * this.height;
          this.ctx.beginPath();
          this.ctx.moveTo(x, y);
          this.ctx.lineTo(x - 5, y + 15);
          this.ctx.stroke();
        }
        break;
      case 'snowy':
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        for (let i = 0; i < 50; i++) {
          const x = Math.random() * this.width;
          const y = Math.random() * this.height;
          this.ctx.beginPath();
          this.ctx.arc(x, y, Math.random() * 3 + 1, 0, Math.PI * 2);
          this.ctx.fill();
        }
        break;
      case 'cloudy':
        this.drawClouds();
        break;
    }
  }

  drawClouds() {
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    for (let i = 0; i < 5; i++) {
      const x = (i * 120 + 50) % this.width;
      const y = 20 + Math.random() * 40;

      // Draw cloud as multiple circles
      for (let j = 0; j < 4; j++) {
        this.ctx.beginPath();
        this.ctx.arc(x + j * 15, y, 20 - j * 2, 0, Math.PI * 2);
        this.ctx.fill();
      }
    }
  }

  drawCityBackground() {
    // Draw distant buildings
    this.ctx.fillStyle = 'rgba(105, 105, 105, 0.4)';
    for (let i = 0; i < 8; i++) {
      const x = i * 70;
      const height = 60 + Math.random() * 40;
      this.ctx.fillRect(x, this.height - height - 200, 60, height);
    }

    // Draw mountains in the distance
    this.ctx.fillStyle = 'rgba(139, 69, 19, 0.3)';
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.height - 150);
    for (let i = 0; i <= this.width; i += 50) {
      this.ctx.lineTo(i, this.height - 150 - Math.sin(i * 0.01) * 30);
    }
    this.ctx.lineTo(this.width, this.height);
    this.ctx.lineTo(0, this.height);
    this.ctx.closePath();
    this.ctx.fill();
  }

  drawInfrastructure() {
    // Draw main roads
    this.ctx.fillStyle = '#696969';

    // Horizontal roads
    for (let i = 1; i < 4; i++) {
      const y = 70 + i * 90;
      this.ctx.fillRect(0, y - 5, this.width, 10);

      // Road markings
      this.ctx.fillStyle = '#FFFF00';
      for (let x = 0; x < this.width; x += 30) {
        this.ctx.fillRect(x, y - 1, 15, 2);
      }
      this.ctx.fillStyle = '#696969';
    }

    // Vertical roads
    for (let i = 1; i < 6; i++) {
      const x = 50 + i * 110;
      this.ctx.fillRect(x - 5, 0, 10, this.height);

      // Road markings
      this.ctx.fillStyle = '#FFFF00';
      for (let y = 0; y < this.height; y += 30) {
        this.ctx.fillRect(x - 1, y, 2, 15);
      }
      this.ctx.fillStyle = '#696969';
    }

    // Draw sidewalks
    this.ctx.fillStyle = '#D3D3D3';
    for (let i = 1; i < 4; i++) {
      const y = 70 + i * 90;
      this.ctx.fillRect(0, y - 8, this.width, 3);
      this.ctx.fillRect(0, y + 5, this.width, 3);
    }
  }

  draw() {
    // Draw enhanced background
    this.drawBackground();

    // Draw properties with enhanced graphics
    this.state.properties.forEach(property => {
      this.drawProperty(property);
    });

    // Draw enhanced UI
    this.drawUI();

    // Draw enhanced game over screen
    if (this.state.gameOver) {
      this.drawGameOverScreen();
    }
  }

  drawProperty(property: Property) {
    const x = property.x;
    const y = property.y;
    const width = 90;
    const height = 70;

    // Property shadow
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
    this.ctx.fillRect(x - width/2 + 3, y - height/2 + 3, width, height);

    // Building base color based on type and ownership
    let baseColor: string;
    let roofColor: string;
    let windowColor: string;

    switch (property.type) {
      case 'residential':
        baseColor = property.owned ? '#8FBC8F' : '#DDA0DD';
        roofColor = '#8B4513';
        windowColor = '#87CEEB';
        break;
      case 'commercial':
        baseColor = property.owned ? '#4682B4' : '#B0C4DE';
        roofColor = '#2F4F4F';
        windowColor = '#F0F8FF';
        break;
      case 'industrial':
        baseColor = property.owned ? '#CD853F' : '#D2B48C';
        roofColor = '#696969';
        windowColor = '#FFFAF0';
        break;
      case 'luxury':
        baseColor = property.owned ? '#FFD700' : '#F0E68C';
        roofColor = '#8B0000';
        windowColor = '#FFF8DC';
        break;
      case 'office':
        baseColor = property.owned ? '#708090' : '#C0C0C0';
        roofColor = '#2F4F4F';
        windowColor = '#E6E6FA';
        break;
      case 'retail':
        baseColor = property.owned ? '#FF6347' : '#FFA07A';
        roofColor = '#8B4513';
        windowColor = '#FFFACD';
        break;
    }

    // Condition affects building appearance
    const conditionAlpha = { poor: 0.6, fair: 0.75, good: 0.9, excellent: 1.0 }[property.condition];

    // Main building structure
    this.ctx.fillStyle = baseColor;
    this.ctx.globalAlpha = conditionAlpha;
    this.ctx.fillRect(x - width/2, y - height/2, width, height - 15);

    // Roof
    this.ctx.fillStyle = roofColor;
    this.ctx.beginPath();
    this.ctx.moveTo(x - width/2 - 5, y - height/2);
    this.ctx.lineTo(x, y - height/2 - 15);
    this.ctx.lineTo(x + width/2 + 5, y - height/2);
    this.ctx.closePath();
    this.ctx.fill();

    this.ctx.globalAlpha = 1.0;

    // Windows based on property level and type
    this.drawWindows(property, x, y, width, height, windowColor);

    // Construction animation
    if (property.isUnderConstruction) {
      this.drawConstruction(property, x, y, width, height);
    }

    // Property border for selection
    if (this.state.selectedProperty?.id === property.id) {
      this.ctx.strokeStyle = '#FFD700';
      this.ctx.lineWidth = 4;
      this.ctx.strokeRect(x - width/2 - 2, y - height/2 - 17, width + 4, height + 19);

      // Glow effect
      this.ctx.shadowColor = '#FFD700';
      this.ctx.shadowBlur = 10;
      this.ctx.strokeRect(x - width/2 - 2, y - height/2 - 17, width + 4, height + 19);
      this.ctx.shadowBlur = 0;
    }

    // Property information display
    this.drawPropertyInfo(property, x, y, width, height);

    // Tenant indicators
    if (property.owned && property.tenants > 0) {
      this.drawTenantIndicators(property, x, y);
    }
  }

  drawWindows(property: Property, x: number, y: number, width: number, height: number, windowColor: string) {
    this.ctx.fillStyle = windowColor;

    const windowWidth = 8;
    const windowHeight = 10;
    const rows = Math.min(property.level + 1, 4);
    const cols = property.type === 'office' ? 6 : 4;

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const windowX = x - width/2 + 10 + col * (windowWidth + 4);
        const windowY = y - height/2 + 10 + row * (windowHeight + 6);

        this.ctx.fillRect(windowX, windowY, windowWidth, windowHeight);

        // Window frame
        this.ctx.strokeStyle = '#2F4F4F';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(windowX, windowY, windowWidth, windowHeight);

        // Light in windows (random)
        if (this.state.timeOfDay === 'evening' || this.state.timeOfDay === 'night') {
          if (Math.random() < 0.7) {
            this.ctx.fillStyle = '#FFFF99';
            this.ctx.fillRect(windowX + 1, windowY + 1, windowWidth - 2, windowHeight - 2);
            this.ctx.fillStyle = windowColor;
          }
        }
      }
    }
  }

  drawConstruction(property: Property, x: number, y: number, width: number, height: number) {
    // Construction overlay
    this.ctx.fillStyle = 'rgba(255, 165, 0, 0.7)';
    this.ctx.fillRect(x - width/2, y - height/2, width, height - 15);

    // Construction progress bar
    const progressWidth = width - 10;
    const progressHeight = 8;
    const progressX = x - progressWidth/2;
    const progressY = y + height/2 - 25;

    this.ctx.fillStyle = '#2F4F4F';
    this.ctx.fillRect(progressX, progressY, progressWidth, progressHeight);

    this.ctx.fillStyle = '#32CD32';
    this.ctx.fillRect(progressX, progressY, (property.constructionProgress / 100) * progressWidth, progressHeight);

    // Construction equipment
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillRect(x - 15, y + height/2 - 10, 8, 8);
    this.ctx.fillRect(x + 7, y + height/2 - 10, 8, 8);
  }

  drawPropertyInfo(property: Property, x: number, y: number, width: number, height: number) {
    // Info background
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(x - width/2, y + height/2 - 15, width, 15);

    this.ctx.fillStyle = '#FFF';
    this.ctx.font = 'bold 8px Arial';
    this.ctx.textAlign = 'center';

    // Property type icon
    const icons = {
      residential: '🏠', commercial: '🏢', industrial: '🏭',
      luxury: '🏰', office: '🏢', retail: '🏪'
    };
    this.ctx.fillText(`${icons[property.type]} ${property.type.toUpperCase()} PROPERTY`, x, y + height/2 - 5);

    // Price/Value
    this.ctx.font = '7px Arial';
    this.ctx.fillText(`💰 Price: $${Math.round(property.price / 1000)}k`, x, y + height/2 - 8);

    if (property.owned) {
      this.ctx.fillText(`🏗️ Level: ${property.level}`, x - 20, y + height/2 - 2);
      this.ctx.fillText(`💵 Income: $${Math.round(property.income)}/2s`, x + 20, y + height/2 - 2);
    }

    // Condition indicator
    const conditionColors = { poor: '#FF0000', fair: '#FFA500', good: '#32CD32', excellent: '#FFD700' };
    this.ctx.fillStyle = conditionColors[property.condition];
    this.ctx.fillRect(x + width/2 - 8, y + height/2 - 12, 6, 6);
    this.ctx.fillStyle = '#2C3E50';
    this.ctx.fillText(`Condition: ${property.condition}`, x + 220, y + height/2 - 2);

    // ROI calculation
    const roi = property.owned ? ((property.income * 12) / property.price * 100) : 0;
    if (property.owned) {
      this.ctx.fillText(`📈 ROI: ${roi.toFixed(1)}%/year`, x + 350, y + height/2 - 2);
    }
  }

  drawTenantIndicators(property: Property, x: number, y: number) {
    const indicatorSize = 4;
    const startX = x - (property.maxTenants * indicatorSize) / 2;

    for (let i = 0; i < property.maxTenants; i++) {
      this.ctx.fillStyle = i < property.tenants ? '#32CD32' : '#696969';
      this.ctx.fillRect(startX + i * (indicatorSize + 1), y - 45, indicatorSize, indicatorSize);
    }
  }

  drawUI() {
    // Main HUD background
    const gradient = this.ctx.createLinearGradient(0, 0, 0, 60);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0.9)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0.7)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, 60);

    // Money display with icon
    this.ctx.fillStyle = '#32CD32';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`💰 $${Math.round(this.state.money).toLocaleString()}`, 10, 25);

    // Income display
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillText(`📈 Income: $${Math.round(this.state.totalIncome).toLocaleString()}`, 10, 45);

    // Reputation with star rating
    const stars = '⭐'.repeat(Math.floor(this.state.reputation / 20));
    this.ctx.fillStyle = '#FF6B35';
    this.ctx.fillText(`${stars} Rep: ${Math.round(this.state.reputation)}`, 250, 25);

    // Time and weather
    this.ctx.fillStyle = '#87CEEB';
    const weatherIcons = { sunny: '☀️', cloudy: '☁️', rainy: '🌧️', snowy: '❄️' };
    this.ctx.fillText(`${weatherIcons[this.state.weatherCondition]} Time: ${this.state.gameTime}s`, 250, 45);

    // Market trend indicator
    const trendIcons = { bull: '📈', bear: '📉', stable: '➡️' };
    const trendColors = { bull: '#32CD32', bear: '#FF4500', stable: '#FFD700' };
    this.ctx.fillStyle = trendColors[this.state.marketTrend];
    this.ctx.fillText(`${trendIcons[this.state.marketTrend]} Market: ${this.state.marketTrend.toUpperCase()}`, 450, 25);

    // Economic index
    this.ctx.fillStyle = '#9370DB';
    this.ctx.fillText(`📊 Economy: ${Math.round(this.state.economicIndex)}`, 450, 45);

    // City population
    this.ctx.fillStyle = '#20B2AA';
    this.ctx.font = '12px Arial';
    this.ctx.fillText(`🏙️ Population: ${this.state.cityPopulation.toLocaleString()}`, 650, 25);

    // Time of day indicator
    const timeIcons = { morning: '🌅', afternoon: '☀️', evening: '🌆', night: '🌙' };
    this.ctx.fillText(`${timeIcons[this.state.timeOfDay]} ${this.state.timeOfDay}`, 650, 45);

    // Draw enhanced property details panel
    if (this.state.selectedProperty) {
      this.drawPropertyDetailsPanel();
    }
  }

  drawPropertyDetailsPanel() {
    const prop = this.state.selectedProperty!;
    const panelWidth = 500;
    const panelHeight = 120;
    const panelX = 10;
    const panelY = this.height - panelHeight - 10;

    // Panel background with gradient
    const gradient = this.ctx.createLinearGradient(panelX, panelY, panelX, panelY + panelHeight);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
    gradient.addColorStop(1, 'rgba(240, 240, 240, 0.95)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);

    // Panel border
    this.ctx.strokeStyle = '#2C3E50';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);

    // Property icon and title
    const icons = {
      residential: '🏠', commercial: '🏢', industrial: '🏭',
      luxury: '🏰', office: '🏢', retail: '🏪'
    };

    this.ctx.fillStyle = '#2C3E50';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`${icons[prop.type]} ${prop.type.toUpperCase()} PROPERTY`, panelX + 15, panelY + 25);

    // Property stats
    this.ctx.font = '12px Arial';
    this.ctx.fillText(`💰 Price: $${prop.price.toLocaleString()}`, panelX + 15, panelY + 45);
    this.ctx.fillText(`📊 Value: $${Math.round(prop.value).toLocaleString()}`, panelX + 180, panelY + 45);
    this.ctx.fillText(`💵 Income: $${Math.round(prop.income)}/2s`, panelX + 350, panelY + 45);

    this.ctx.fillText(`🏗️ Level: ${prop.level}`, panelX + 15, panelY + 65);
    this.ctx.fillText(`🏠 Tenants: ${prop.tenants}/${prop.maxTenants}`, panelX + 100, panelY + 65);

    // Condition indicator
    const conditionColors = { poor: '#FF4500', fair: '#FFA500', good: '#32CD32', excellent: '#FFD700' };
    this.ctx.fillStyle = conditionColors[prop.condition];
    this.ctx.fillRect(panelX + 200, panelY + 55, 12, 12);
    this.ctx.fillStyle = '#2C3E50';
    this.ctx.fillText(`Condition: ${prop.condition}`, panelX + 220, panelY + 65);

    // ROI calculation
    const roi = prop.owned ? ((prop.income * 12) / prop.price * 100) : 0;
    if (prop.owned) {
      this.ctx.fillText(`📈 ROI: ${roi.toFixed(1)}%/year`, panelX + 350, panelY + 65);
    }

    // Action buttons with enhanced styling
    const buttonY = panelY + 80;
    const buttonHeight = 25;

    if (!prop.owned && this.state.money >= prop.price) {
      // Buy button
      this.drawButton(panelX + 15, buttonY, 80, buttonHeight, '#27AE60', '#FFF', 'BUY');
    } else if (!prop.owned) {
      // Insufficient funds
      this.drawButton(panelX + 15, buttonY, 80, buttonHeight, '#95A5A6', '#FFF', 'NO FUNDS');
    }

    if (prop.owned) {
      const upgradeCost = Math.round(prop.price * 0.4 * prop.level);

      // Upgrade button
      if (this.state.money >= upgradeCost && !prop.isUnderConstruction) {
        this.drawButton(panelX + 105, buttonY, 100, buttonHeight, '#3498DB', '#FFF', `UPGRADE $${(upgradeCost/1000).toFixed(0)}k`);
      } else if (prop.isUnderConstruction) {
        this.drawButton(panelX + 105, buttonY, 100, buttonHeight, '#F39C12', '#FFF', 'BUILDING...');
      } else {
        this.drawButton(panelX + 105, buttonY, 100, buttonHeight, '#95A5A6', '#FFF', 'NO FUNDS');
      }

      // Sell button
      const sellPrice = Math.round(prop.value * 0.85);
      this.drawButton(panelX + 215, buttonY, 100, buttonHeight, '#E74C3C', '#FFF', `SELL $${(sellPrice/1000).toFixed(0)}k`);

      // Renovate button (improves condition)
      const renovateCost = Math.round(prop.price * 0.2);
      if (this.state.money >= renovateCost && prop.condition !== 'excellent') {
        this.drawButton(panelX + 325, buttonY, 100, buttonHeight, '#9B59B6', '#FFF', `RENOVATE $${(renovateCost/1000).toFixed(0)}k`);
      }
    }
  }

  drawButton(x: number, y: number, width: number, height: number, bgColor: string, textColor: string, text: string) {
    // Button background with gradient
    const gradient = this.ctx.createLinearGradient(x, y, x, y + height);
    gradient.addColorStop(0, bgColor);
    gradient.addColorStop(1, this.darkenColor(bgColor, 20));
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(x, y, width, height);

    // Button border
    this.ctx.strokeStyle = this.darkenColor(bgColor, 40);
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // Button text
    this.ctx.fillStyle = textColor;
    this.ctx.font = 'bold 10px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(text, x + width/2, y + height/2 + 3);
  }

  darkenColor(color: string, percent: number): string {
    // Simple color darkening function
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }

  drawGameOverScreen() {
    // Dark overlay
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.85)';
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Results panel
    const panelWidth = 500;
    const panelHeight = 400;
    const panelX = (this.width - panelWidth) / 2;
    const panelY = (this.height - panelHeight) / 2;

    // Panel background with gradient
    const gradient = this.ctx.createLinearGradient(panelX, panelY, panelX, panelY + panelHeight);
    gradient.addColorStop(0, '#2C3E50');
    gradient.addColorStop(1, '#34495E');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);

    // Panel border
    this.ctx.strokeStyle = '#F39C12';
    this.ctx.lineWidth = 4;
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);

    // Title
    this.ctx.fillStyle = '#F39C12';
    this.ctx.font = 'bold 28px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('🏙️ REAL ESTATE EMPIRE COMPLETE! 🏙️', this.width/2, panelY + 50);

    // Performance rating
    const rating = this.getPerformanceRating();
    this.ctx.fillStyle = '#E74C3C';
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillText(rating, this.width/2, panelY + 90);

    // Statistics
    this.ctx.fillStyle = '#ECF0F1';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'left';

    const stats = [
      `💰 Final Money: $${Math.round(this.state.money).toLocaleString()}`,
      `📈 Total Income Generated: $${Math.round(this.state.totalIncome).toLocaleString()}`,
      `🏠 Properties Owned: ${this.state.properties.filter(p => p.owned).length}/${this.state.properties.length}`,
      `⭐ Final Reputation: ${Math.round(this.state.reputation)}`,
      `🏗️ Properties Upgraded: ${this.state.properties.filter(p => p.level > 1).length}`,
      `🏙️ City Population: ${this.state.cityPopulation.toLocaleString()}`,
      `📊 Economic Index: ${Math.round(this.state.economicIndex)}`,
      `🎯 Market Trend: ${this.state.marketTrend.toUpperCase()}`
    ];

    stats.forEach((stat, index) => {
      this.ctx.fillText(stat, panelX + 30, panelY + 130 + index * 25);
    });

    // Portfolio breakdown
    this.ctx.fillStyle = '#3498DB';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillText('PORTFOLIO BREAKDOWN:', panelX + 30, panelY + 340);

    const portfolioStats = this.getPortfolioStats();
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = '#BDC3C7';
    portfolioStats.forEach((stat, index) => {
      this.ctx.fillText(stat, panelX + 30 + (index % 2) * 220, panelY + 360 + Math.floor(index / 2) * 15);
    });
  }

  getPerformanceRating(): string {
    const ownedProperties = this.state.properties.filter(p => p.owned).length;
    const totalValue = this.state.money + this.state.totalIncome;

    if (totalValue > 500000 && ownedProperties >= 10) return '🏆 REAL ESTATE MOGUL';
    if (totalValue > 300000 && ownedProperties >= 7) return '👑 PROPERTY TYCOON';
    if (totalValue > 200000 && ownedProperties >= 5) return '💎 SUCCESSFUL INVESTOR';
    if (totalValue > 100000 && ownedProperties >= 3) return '🏠 PROPERTY OWNER';
    if (ownedProperties >= 1) return '🔑 FIRST-TIME BUYER';
    return '💸 WINDOW SHOPPER';
  }

  getPortfolioStats(): string[] {
    const owned = this.state.properties.filter(p => p.owned);
    const byType = owned.reduce((acc, prop) => {
      acc[prop.type] = (acc[prop.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(byType).map(([type, count]) =>
      `${type}: ${count} properties`
    );
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = () => {
    if (!this.isRunning) return;
    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface RealEstateTycoonProps {
  isPreview?: boolean;
}

const RealEstateTycoon: React.FC<RealEstateTycoonProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<RealEstateTycoonLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new RealEstateTycoonLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default RealEstateTycoon;
