/* AI Avatar Revolutionary Styles */
.ai-avatar-container {
  position: relative;
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Animated Particles Background */
.ai-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--color, #60a5fa);
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat 8s infinite linear;
  animation-delay: var(--delay, 0s);
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) scale(1);
    opacity: 0;
  }
}

/* Main Avatar Styles */
.ai-avatar {
  position: relative;
  width: 200px;
  height: 200px;
  transform: scale(var(--breathe-scale, 1)) scale(var(--voice-scale, 1));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: avatarIdle 4s ease-in-out infinite;
}

@keyframes avatarIdle {
  0%, 100% { transform: translateY(0) rotateX(0deg); }
  50% { transform: translateY(-5px) rotateX(2deg); }
}

/* Glow Ring Effects */
.avatar-glow-ring {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    var(--glow-color, #3b82f6),
    transparent,
    var(--glow-color, #3b82f6)
  );
  opacity: 0.6;
  animation: ringRotate 8s linear infinite;
  filter: blur(8px);
}

.avatar-middle-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: linear-gradient(
    45deg,
    var(--primary-color, #3b82f6),
    var(--secondary-color, #60a5fa)
  );
  opacity: 0.3;
  animation: ringRotate 6s linear infinite reverse;
  filter: blur(4px);
}

@keyframes ringRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Core Avatar */
.avatar-core {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: 
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), transparent 50%),
    linear-gradient(135deg, var(--primary-color, #3b82f6), var(--secondary-color, #60a5fa));
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 0 50px var(--glow-color, #3b82f6),
    inset 0 0 50px rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Face Elements */
.avatar-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
}

/* Eyes */
.avatar-eyes {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.eye {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.eye.blink {
  height: 2px;
  border-radius: 10px;
}

.eye-pupil {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color, #3b82f6);
  transition: all 0.3s ease;
}

.eye-shine {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

/* Mouth */
.avatar-mouth {
  position: absolute;
  bottom: 35%;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 15px;
  border-radius: 0 0 30px 30px;
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.avatar-mouth.happy {
  border-radius: 30px 30px 0 0;
  bottom: 30%;
}

.avatar-mouth.excited {
  width: 40px;
  height: 20px;
  border-radius: 50%;
  animation: mouthPulse 0.5s ease-in-out infinite alternate;
}

.avatar-mouth.curious {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

@keyframes mouthPulse {
  from { transform: translateX(-50%) scale(1); }
  to { transform: translateX(-50%) scale(1.1); }
}

/* Voice Waves */
.voice-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 2px;
}

.voice-wave {
  width: 2px;
  height: 10px;
  background: var(--primary-color, #3b82f6);
  border-radius: 1px;
  animation: voiceWave 0.6s ease-in-out infinite alternate;
  animation-delay: var(--delay, 0s);
  transform: scaleY(var(--intensity, 0.5));
}

@keyframes voiceWave {
  from { transform: scaleY(0.3); }
  to { transform: scaleY(1); }
}

/* Thinking Dots */
.thinking-dots {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  animation: thinkingDot 1.5s ease-in-out infinite;
}

.dot:nth-child(2) { animation-delay: 0.3s; }
.dot:nth-child(3) { animation-delay: 0.6s; }

@keyframes thinkingDot {
  0%, 60%, 100% { transform: scale(1); opacity: 0.7; }
  30% { transform: scale(1.3); opacity: 1; }
}

/* Neural Network Effect */
.neural-network {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.neural-line {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1px;
  height: 80px;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--primary-color, #3b82f6),
    transparent
  );
  transform-origin: bottom;
  transform: translate(-50%, -100%) rotate(var(--angle, 0deg));
  opacity: 0.3;
  animation: neuralPulse 3s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

@keyframes neuralPulse {
  0%, 100% { opacity: 0.1; transform: translate(-50%, -100%) rotate(var(--angle, 0deg)) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -100%) rotate(var(--angle, 0deg)) scale(1.1); }
}

/* Voice Visualization */
.voice-visualization {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 250px;
  pointer-events: none;
}

.voice-bar {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 3px;
  height: 40px;
  background: var(--glow-color, #3b82f6);
  border-radius: 2px;
  transform-origin: bottom;
  transform: translate(-50%, -100%) rotate(var(--angle, 0deg));
  animation: voiceBar 0.3s ease-in-out infinite alternate;
  animation-delay: var(--delay, 0s);
  opacity: 0.8;
}

@keyframes voiceBar {
  from { 
    transform: translate(-50%, -100%) rotate(var(--angle, 0deg)) scaleY(0.5);
    opacity: 0.5;
  }
  to { 
    transform: translate(-50%, -100%) rotate(var(--angle, 0deg)) scaleY(var(--intensity, 1));
    opacity: 1;
  }
}

/* Listening Pulse */
.listening-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid var(--glow-color, #3b82f6);
  border-radius: 50%;
  opacity: 0;
  animation: listeningPulse 2s ease-out infinite;
}

.pulse-ring:nth-child(2) { animation-delay: 0.7s; }
.pulse-ring:nth-child(3) { animation-delay: 1.4s; }

@keyframes listeningPulse {
  0% {
    width: 100%;
    height: 100%;
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

/* Holographic Grid */
.holographic-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.2;
}

.grid-line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color, #3b82f6),
    transparent
  );
  top: calc(20% + var(--delay, 0s) * 50px);
  animation: gridScan 4s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

@keyframes gridScan {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

/* Status Indicator */
.status-indicator {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  font-family: 'Courier New', monospace;
}

.status-text {
  color: var(--primary-color, #3b82f6);
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 2px;
  margin-bottom: 5px;
  text-shadow: 0 0 10px currentColor;
}

.status-bar {
  width: 100px;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.status-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color, #3b82f6),
    var(--secondary-color, #60a5fa)
  );
  border-radius: 2px;
  animation: statusFill 2s ease-in-out infinite;
}

@keyframes statusFill {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; }
}

/* Emotion-specific animations */
.ai-avatar.thinking .avatar-core {
  animation: thinkingGlow 2s ease-in-out infinite alternate;
}

@keyframes thinkingGlow {
  from { box-shadow: 0 0 50px var(--glow-color, #3b82f6), inset 0 0 50px rgba(255, 255, 255, 0.1); }
  to { box-shadow: 0 0 80px var(--glow-color, #3b82f6), inset 0 0 80px rgba(255, 255, 255, 0.2); }
}

.ai-avatar.excited .avatar-core {
  animation: excitedShake 0.5s ease-in-out infinite;
}

@keyframes excitedShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.ai-avatar.happy .avatar-core {
  animation: happyBounce 1s ease-in-out infinite;
}

@keyframes happyBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-avatar-container {
    width: 250px;
    height: 250px;
  }
  
  .ai-avatar {
    width: 150px;
    height: 150px;
  }
  
  .voice-visualization {
    width: 200px;
    height: 200px;
  }
}
