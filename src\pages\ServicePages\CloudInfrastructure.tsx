import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Cloud, Server, Shield, Settings } from 'lucide-react';

const CloudInfrastructure = () => {
  const features = [
    {
      icon: <Cloud className="text-purple-400" size={24} />,
      title: "Cloud Setup & Migration",
      description: "Seamless migration to AWS, Azure, or Google Cloud"
    },
    {
      icon: <Server className="text-purple-400" size={24} />,
      title: "Infrastructure Management",
      description: "24/7 monitoring and management of cloud resources"
    },
    {
      icon: <Shield className="text-purple-400" size={24} />,
      title: "Security & Compliance",
      description: "Enterprise-grade security and compliance solutions"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Performance Optimization",
      description: "Continuous optimization for cost and performance"
    }
  ];

  const pricing = [
    {
      name: "Startup",
      price: "Contact Us",
      features: [
        "Basic Cloud Setup",
        "Essential Security",
        "Basic Monitoring",
        "Email Support",
        "8/5 Support"
      ]
    },
    {
      name: "Business",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced Cloud Setup",
        "Multi-Cloud Support",
        "Advanced Security",
        "24/7 Monitoring",
        "Priority Support",
        "Disaster Recovery"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom Cloud Architecture",
        "Global Infrastructure",
        "Compliance Management",
        "Advanced Analytics",
        "Dedicated Support Team",
        "Custom SLAs"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Cloud Infrastructure"
      description="Comprehensive cloud infrastructure management services for modern businesses"
      features={features}
      pricing={pricing}
      technologies={['AWS', 'Azure', 'Google Cloud', 'Kubernetes', 'Docker', 'Terraform']}
      codeExample={`// Infrastructure as Code using Terraform
provider "aws" {
  region = "us-west-2"
}

resource "aws_instance" "web" {
  ami           = "ami-0c55b159cbfafe1f0"
  instance_type = "t2.micro"

  tags = {
    Name = "WebServer"
    Environment = "Production"
  }

  vpc_security_group_ids = [aws_security_group.allow_http.id]
}

resource "aws_security_group" "allow_http" {
  name        = "allow_http"
  description = "Allow HTTP inbound traffic"

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}`}
    />
  );
};

export default CloudInfrastructure;
