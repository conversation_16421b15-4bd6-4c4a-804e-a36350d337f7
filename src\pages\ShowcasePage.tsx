import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getProjects } from '../services/showcaseService';
import { Project } from '../types/showcase';
import { Link } from 'react-router-dom';
import { Monitor, Eye } from 'lucide-react';
import PreviewModal from '../components/PreviewModal';

const ShowcasePage: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [previewTitle, setPreviewTitle] = useState<string>('');
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
  
  const openPreview = (url: string, title: string) => {
    setPreviewUrl(url);
    setPreviewTitle(title);
    setIsPreviewOpen(true);
  };
  
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const data = await getProjects();
        setProjects(data);
        setError(null);
      } catch (err) {
        setError('Failed to load projects. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProjects();
  }, []);
  
  // Extract unique categories
  const categories = [...new Set(projects.map(project => project.category))];
  
  // Filter projects by category if selected
  const filteredProjects = selectedCategory 
    ? projects.filter(project => project.category === selectedCategory)
    : projects;
  
  return (
    <div className="bg-black min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-4">Project Showcase</h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Explore our portfolio of completed projects and client work.
            Each project demonstrates our expertise and commitment to quality.
          </p>
        </div>
        
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === null 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => setSelectedCategory(null)}
          >
            All Projects
          </button>
          {categories.map(category => (
            <button
              key={category}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-10">
            <p>{error}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group relative backdrop-blur-sm bg-gray-900/70 rounded-xl overflow-hidden border border-gray-800/50 hover:border-purple-500/50 transition-all duration-300 shadow-lg hover:shadow-purple-500/20"
                style={{
                  backdropFilter: 'blur(12px)',
                  WebkitBackdropFilter: 'blur(12px)'
                }}
              >
                <div className="relative aspect-video overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-gray-900/20 z-10"></div>
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <Monitor className="w-12 h-12 text-gray-400 opacity-50 group-hover:opacity-0 transition-opacity duration-300" />
                  </div>
                  <iframe 
                    src={project.url} 
                    className="absolute inset-0 w-full h-full opacity-90 group-hover:opacity-100 transition-all duration-300 scale-[1.01] group-hover:scale-[1.03]"
                    title={project.name}
                    loading="lazy"
                  ></iframe>
                  <div className="absolute top-0 right-0 m-3 z-20">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/30 text-purple-100 backdrop-blur-sm border border-purple-400/20">
                      {project.category}
                    </span>
                  </div>
                </div>
                
                <div className="p-6 relative z-10">
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900/95 via-gray-900/80 to-gray-900/50 backdrop-blur-md z-[-1]"></div>
                  
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-white group-hover:text-purple-200 transition-colors">{project.name}</h3>
                    <button 
                      onClick={() => openPreview(project.url, project.name)}
                      className="text-purple-400 hover:text-purple-300 focus:outline-none p-2 -m-2 rounded-full hover:bg-purple-500/10 transition-all"
                      aria-label="Preview project"
                    >
                      <Eye className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <p className="text-gray-400 mb-4 line-clamp-2">{project.description}</p>
                  
                  {project.features && project.features.length > 0 && (
                    <div>
                      <div className="flex items-center mb-2">
                        <div className="h-px flex-grow bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
                        <h4 className="text-xs font-semibold text-gray-300 px-2 uppercase tracking-wider">Features</h4>
                        <div className="h-px flex-grow bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
                      </div>
                      
                      <ul className="space-y-1.5">
                        {project.features
                          .filter(feature => feature.isHighlighted)
                          .slice(0, 2)
                          .map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-purple-400 mr-2 opacity-70">•</span>
                              <span className="text-gray-400 text-sm">{feature.feature}</span>
                            </li>
                          ))}
                      </ul>
                      
                      {project.features.length > 2 && (
                        <Link 
                          to={`/showcase/${project.id}`}
                          className="text-purple-400 text-xs hover:text-purple-300 mt-3 inline-flex items-center group/link"
                        >
                          <span>View all features</span>
                          <span className="ml-1 transition-transform group-hover/link:translate-x-0.5">→</span>
                        </Link>
                      )}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
        
        {!loading && !error && filteredProjects.length === 0 && (
          <div className="text-center py-10">
            <p className="text-gray-400">No projects found in this category.</p>
          </div>
        )}
        

      </div>
      {/* Preview Modal */}
      <PreviewModal
        url={previewUrl}
        title={previewTitle}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </div>
  );
};

export default ShowcasePage;
