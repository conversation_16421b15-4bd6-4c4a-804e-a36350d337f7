import React, { useState, useRef, useEffect } from 'react';
import emailjs from '@emailjs/browser';

const ContactForm = () => {
  const form = useRef<HTMLFormElement>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    service: '',
    description: ''
  });

  useEffect(() => {
    emailjs.init("lRPim9tbeflBWVbCy");
  }, []);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const templateParams = {
        to_name: "Spirelab",
        from_name: `${formData.firstName} ${formData.lastName}`,
        from_email: formData.email,
        phone_number: formData.phone,
        service_type: formData.service,
        message: formData.description
      };

      const result = await emailjs.send(
        'service_kfqm6jh',
        'template_vzr0p11',
        templateParams,
        'lRPim9tbeflBWVbCy'
      );

      if (result.text === 'OK') {
        setSubmitStatus('success');
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          service: '',
          description: ''
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
      setErrorMessage('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold mb-4 text-center font-mono" style={{ color: 'var(--color-text-primary)' }}>Book a demo with our IT experts</h2>
          
          {/* Award badges */}
          <div className="flex flex-wrap justify-center gap-4 mb-10">
            <img src="/badges/top-performer.svg" alt="Top Performer" className="h-16" />
            <img src="/badges/leader.svg" alt="Leader" className="h-16" />
            <img src="/badges/frontrunner.svg" alt="Frontrunner" className="h-16" />
            <img src="/badges/easiest-to-use.svg" alt="Easiest to Use" className="h-16" />
          </div>

          <form ref={form} onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <input
                  type="text"
                  required
                  value={formData.firstName}
                  onChange={e => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                  style={{ 
                    color: 'var(--color-text-primary)',
                    borderWidth: '1px',
                    borderColor: 'var(--color-card-border)',
                    borderStyle: 'solid'
                  }}
                  placeholder="First Name *"
                />
              </div>
              <div>
                <input
                  type="text"
                  required
                  value={formData.lastName}
                  onChange={e => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                  style={{ 
                    color: 'var(--color-text-primary)',
                    borderWidth: '1px',
                    borderColor: 'var(--color-card-border)',
                    borderStyle: 'solid'
                  }}
                  placeholder="Last Name *"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                  style={{ 
                    color: 'var(--color-text-primary)',
                    borderWidth: '1px',
                    borderColor: 'var(--color-card-border)',
                    borderStyle: 'solid'
                  }}
                  placeholder="Company Email *"
                />
              </div>
              <div>
                <input
                  type="text"
                  value={formData.phone}
                  onChange={e => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                  style={{ 
                    color: 'var(--color-text-primary)',
                    borderWidth: '1px',
                    borderColor: 'var(--color-card-border)',
                    borderStyle: 'solid'
                  }}
                  placeholder="Company Name *"
                />
              </div>
            </div>

            <div>
              <select
                required
                value={formData.service}
                onChange={e => setFormData(prev => ({ ...prev, service: e.target.value }))}
                className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                style={{ 
                  color: 'var(--color-text-primary)',
                  borderWidth: '1px',
                  borderColor: 'var(--color-card-border)',
                  borderStyle: 'solid'
                }}
              >
                <option value="">Select a service *</option>
                <option value="CMS Development">CMS Development</option>
                <option value="Cloud Infrastructure">Cloud Infrastructure</option>
                <option value="Cybersecurity">Cybersecurity</option>
                <option value="Remote IT Support">Remote IT Support</option>
                <option value="Custom Software">Custom Software</option>
                <option value="DevOps & CI/CD">DevOps & CI/CD</option>
                <option value="Data Analytics">Data Analytics</option>
                <option value="Managed IT">Managed IT</option>
                <option value="Digital Transformation">Digital Transformation</option>
                <option value="Network Solutions">Network Solutions</option>
                <option value="E-commerce Solutions">E-commerce Solutions</option>
                <option value="VDI Solutions">VDI Solutions</option>
              </select>
            </div>

            <div>
              <textarea
                required
                rows={4}
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full bg-gray-900/50 rounded-sm px-4 py-2 focus:outline-none"
                style={{ 
                  color: 'var(--color-text-primary)',
                  borderWidth: '1px',
                  borderColor: 'var(--color-card-border)',
                  borderStyle: 'solid'
                }}
                placeholder="What are you looking to accomplish on this call?"
              ></textarea>
            </div>

            <div className="flex justify-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`px-12 py-2 rounded-sm transition-all ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                style={{ 
                  backgroundColor: 'var(--color-primary)',
                  color: 'var(--color-text-primary)',
                  fontFamily: 'var(--font-family-primary)'
                }}
              >
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </button>
            </div>

            {submitStatus === 'success' && (
              <p className="mt-4 text-center text-sm font-mono" style={{ color: 'var(--color-primary)' }}>Message sent successfully!</p>
            )}
            {submitStatus === 'error' && (
              <p className="mt-4 text-center text-sm font-mono" style={{ color: 'var(--color-primary)' }}>
                {errorMessage || 'Error sending message. Please try again.'}
              </p>
            )}
            
            <p className="text-xs text-center mt-4" style={{ color: 'var(--color-text-secondary)' }}>
              *Required fields. Spirelab will handle your data pursuant to our <a href="#" style={{ color: 'var(--color-primary)' }}>Privacy Policy</a>. This site is protected by reCAPTCHA and the Google <a href="#" style={{ color: 'var(--color-primary)' }}>Privacy Policy</a> and <a href="#" style={{ color: 'var(--color-primary)' }}>Terms of Service</a> apply.
            </p>
          </form>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
