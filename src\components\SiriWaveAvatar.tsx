import React, { useEffect, useRef, useState } from 'react';

interface SiriWaveAvatarProps {
  isListening?: boolean;
  isSpeaking?: boolean;
  isThinking?: boolean;
  emotion?: string;
  size?: number;
}

const SiriWaveAvatar: React.FC<SiriWaveAvatarProps> = ({
  isListening = false,
  isSpeaking = false,
  isThinking = false,
  emotion = 'idle',
  size = 200
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const timeRef = useRef<number>(0);
  const [audioLevel, setAudioLevel] = useState<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = size;
    canvas.height = size;

    const centerX = size / 2;
    const centerY = size / 2;
    const baseRadius = size * 0.15;

    // Number of bars in the wave
    const barCount = 60;
    const maxBarHeight = size * 0.3;

    const drawSiriWave = (time: number) => {
      ctx.clearRect(0, 0, size, size);

      // Determine animation intensity based on state
      let intensity = 0.1;
      let speed = 1;
      let colors = ['#007AFF', '#5AC8FA', '#34C759']; // Default blue colors
      let waveCount = 3;

      if (isListening) {
        intensity = 0.9 + Math.sin(time * 0.008) * 0.4;
        speed = 2.5;
        colors = ['#FF9500', '#FF6B35', '#FF3B30']; // Orange/red for listening
        waveCount = 5; // More waves when listening
      } else if (isSpeaking) {
        intensity = 0.7 + Math.sin(time * 0.006) * 0.5;
        speed = 2;
        colors = ['#30D158', '#32D74B', '#34C759']; // Green for speaking
        waveCount = 4;
      } else if (isThinking) {
        intensity = 0.5 + Math.sin(time * 0.004) * 0.3;
        speed = 1.2;
        colors = ['#BF5AF2', '#AF52DE', '#9F44D3']; // Purple for thinking
        waveCount = 3;
      } else {
        // Idle state - gentle pulsing
        intensity = 0.3 + Math.sin(time * 0.002) * 0.15;
        speed = 0.8;
        colors = ['#007AFF', '#5AC8FA', '#34AADC']; // Blue for idle
        waveCount = 2;
      }

      // Draw multiple wave layers for depth
      for (let layer = 0; layer < waveCount; layer++) {
        const layerOpacity = 0.9 - (layer * 0.15);
        const layerOffset = layer * 0.8;
        const layerSpeed = speed * (1 + layer * 0.3);

        ctx.globalAlpha = layerOpacity;

        for (let i = 0; i < barCount; i++) {
          const angle = (i / barCount) * Math.PI * 2;

          // Create more complex wave patterns
          const wave1 = Math.sin(time * 0.005 * layerSpeed + i * 0.15 + layerOffset) * intensity;
          const wave2 = Math.sin(time * 0.008 * layerSpeed + i * 0.25 + layerOffset * 1.5) * intensity * 0.7;
          const wave3 = Math.sin(time * 0.012 * layerSpeed + i * 0.1 + layerOffset * 2) * intensity * 0.4;

          const combinedWave = wave1 + wave2 + wave3;
          const barHeight = Math.abs(combinedWave) * maxBarHeight;

          // Calculate bar position
          const innerRadius = baseRadius * (0.6 + layer * 0.1);
          const outerRadius = innerRadius + barHeight;

          const x1 = centerX + Math.cos(angle) * innerRadius;
          const y1 = centerY + Math.sin(angle) * innerRadius;
          const x2 = centerX + Math.cos(angle) * outerRadius;
          const y2 = centerY + Math.sin(angle) * outerRadius;

          // Create gradient for each bar
          const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
          const colorIndex = Math.floor((i + layer * 20) / 20) % colors.length;
          const nextColorIndex = (colorIndex + 1) % colors.length;

          gradient.addColorStop(0, colors[colorIndex] + '80');
          gradient.addColorStop(0.5, colors[colorIndex]);
          gradient.addColorStop(1, colors[nextColorIndex] + '40');

          ctx.strokeStyle = gradient;
          ctx.lineWidth = Math.max(1, 4 - layer * 0.8);
          ctx.lineCap = 'round';

          ctx.beginPath();
          ctx.moveTo(x1, y1);
          ctx.lineTo(x2, y2);
          ctx.stroke();
        }
      }

      // Draw center glow effect
      ctx.globalAlpha = 0.6;
      const centerGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, baseRadius * 0.8);
      centerGradient.addColorStop(0, colors[0] + '60');
      centerGradient.addColorStop(0.5, colors[1] + '30');
      centerGradient.addColorStop(1, 'transparent');

      ctx.fillStyle = centerGradient;
      ctx.beginPath();
      ctx.arc(centerX, centerY, baseRadius * 0.8, 0, Math.PI * 2);
      ctx.fill();

      // Add multiple pulsing center dots for Siri effect
      for (let i = 0; i < 3; i++) {
        ctx.globalAlpha = 0.8 - i * 0.2;
        const dotSize = (6 + intensity * 8) * (1 - i * 0.3);
        const pulseOffset = Math.sin(time * 0.01 + i * 1.5) * 0.3;
        const currentDotSize = dotSize * (1 + pulseOffset);

        const dotGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, currentDotSize);
        dotGradient.addColorStop(0, colors[i % colors.length]);
        dotGradient.addColorStop(0.7, colors[i % colors.length] + '80');
        dotGradient.addColorStop(1, colors[i % colors.length] + '00');

        ctx.fillStyle = dotGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, currentDotSize, 0, Math.PI * 2);
        ctx.fill();
      }

      ctx.globalAlpha = 1;
    };

    const animate = (timestamp: number) => {
      timeRef.current = timestamp;
      drawSiriWave(timestamp);
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [size, isListening, isSpeaking, isThinking, emotion]);

  return (
    <div 
      className="siri-wave-avatar"
      style={{
        width: size,
        height: size,
        borderRadius: '50%',
        overflow: 'hidden',
        background: 'radial-gradient(circle, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.05) 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.1)'
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: 'block'
        }}
      />
    </div>
  );
};

export default SiriWaveAvatar;
