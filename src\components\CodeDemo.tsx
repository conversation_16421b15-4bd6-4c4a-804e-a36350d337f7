import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CodeDemo = () => {
  const [frame, setFrame] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setFrame((prev) => (prev + 1) % 3);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="grid grid-cols-3 gap-4">
      {/* Left terminal */}
      <div className="terminal-window">
        <div className="terminal-header">
          <div className="terminal-dot bg-red-500"></div>
          <div className="terminal-dot bg-yellow-500"></div>
          <div className="terminal-dot bg-green-500"></div>
          <span className="text-xs ml-2">LLM</span>
        </div>
        <div className="terminal-content">
          <pre className="text-green-400">
            [.788] [.666] [.586] [.410] [.206]
            <br />
            [.805] [.265] [.613]
            <br />
            [.410] [.505] [.104]
            <br />
            [.273] [.765] [.665]
          </pre>
        </div>
      </div>

      {/* Center terminal */}
      <div className="terminal-window col-span-1">
        <div className="terminal-header">
          <div className="terminal-dot bg-red-500"></div>
          <div className="terminal-dot bg-yellow-500"></div>
          <div className="terminal-dot bg-green-500"></div>
          <span className="text-xs ml-2">E2B SANDBOX</span>
        </div>
        <div className="terminal-content flex items-center justify-center min-h-[200px]">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-purple-400 text-4xl"
          >
            ⚡
          </motion.div>
        </div>
      </div>

      {/* Right terminal */}
      <div className="terminal-window">
        <div className="terminal-header">
          <div className="terminal-dot bg-red-500"></div>
          <div className="terminal-dot bg-yellow-500"></div>
          <div className="terminal-dot bg-green-500"></div>
          <span className="text-xs ml-2">OUTPUT</span>
        </div>
        <div className="terminal-content flex justify-around items-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-orange-500/20 rounded-lg mb-2"></div>
            <span className="text-orange-500">CSV</span>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-500/20 rounded-lg mb-2"></div>
            <span className="text-gray-500">TXT</span>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-500/20 rounded-lg mb-2"></div>
            <span className="text-yellow-500">.JS</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeDemo;