import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { ShoppingCart, CreditCard, Globe, Settings } from 'lucide-react';

const EcommerceSolutions = () => {
  const features = [
    {
      icon: <ShoppingCart className="text-purple-400" size={24} />,
      title: "E-commerce Platform",
      description: "Custom online store development and setup"
    },
    {
      icon: <CreditCard className="text-purple-400" size={24} />,
      title: "Payment Integration",
      description: "Secure payment gateway integration"
    },
    {
      icon: <Globe className="text-purple-400" size={24} />,
      title: "Multi-region Support",
      description: "Multi-currency and language support"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Store Management",
      description: "Complete e-commerce platform management"
    }
  ];

  const pricing = [
    {
      name: "Starter",
      price: "Contact Us",
      features: [
        "Basic Online Store",
        "Payment Gateway",
        "Product Management",
        "Basic Analytics",
        "3 Months Support"
      ]
    },
    {
      name: "Business",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced E-commerce Platform",
        "Multiple Payment Gateways",
        "Inventory Management",
        "Advanced Analytics",
        "Multi-language Support",
        "12 Months Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom E-commerce Solution",
        "Global Market Support",
        "Advanced Integration",
        "Custom Features",
        "Dedicated Support",
        "Enterprise Analytics"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="E-commerce Solutions"
      description="Professional e-commerce platform development and management services"
      features={features}
      pricing={pricing}
      technologies={['WooCommerce', 'Shopify', 'Magento', 'React', 'Node.js', 'Stripe']}
      codeExample={`// E-commerce Platform Integration
class EcommerceStore {
  constructor() {
    this.products = new ProductManager();
    this.cart = new ShoppingCart();
    this.payments = new PaymentProcessor();
    this.inventory = new InventoryManager();
  }

  async processOrder(order) {
    try {
      // Check inventory
      await this.inventory.checkAvailability(order.items);
      
      // Process payment
      const payment = await this.payments.processPayment({
        amount: order.total,
        currency: order.currency,
        method: order.paymentMethod
      });
      
      if (payment.status === 'success') {
        // Update inventory
        await this.inventory.updateStock(order.items);
        
        // Create shipment
        const shipment = await this.createShipment(order);
        
        // Send confirmation
        await this.sendOrderConfirmation(order);
        
        return {
          orderId: order.id,
          status: 'confirmed',
          shipment: shipment,
          payment: payment
        };
      }
    } catch (error) {
      await this.handleOrderError(error, order);
      throw error;
    }
  }
}`}
    />
  );
};

export default EcommerceSolutions;
