import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  host: 'mail.spirelab.net',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'Alpha@123#$'
  },
  tls: {
    rejectUnauthorized: false
  }
});

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  service: string;
  description: string;
  files?: FileList;
}

export async function sendEmail(formData: FormData) {
  const { firstName, lastName, email, phone, service, description } = formData;

  const mailOptions = {
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: `New Contact Form Submission - ${service}`,
    html: `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${firstName} ${lastName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone}</p>
      <p><strong>Service:</strong> ${service}</p>
      <p><strong>Description:</strong></p>
      <p>${description.replace(/\n/g, '<br>')}</p>
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: 'Failed to send email' };
  }
}
