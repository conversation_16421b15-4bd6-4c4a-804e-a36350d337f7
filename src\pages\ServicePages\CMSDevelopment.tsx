import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Code, ShoppingCart, Settings, Globe } from 'lucide-react';

const CMSDevelopment = () => {
  const features = [
    {
      icon: <Code className="text-purple-400" size={24} />,
      title: "WordPress Development",
      description: "Custom WordPress solutions tailored to your business needs"
    },
    {
      icon: <ShoppingCart className="text-purple-400" size={24} />,
      title: "WooCommerce Setup",
      description: "Complete e-commerce platform setup and customization"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Custom Extensions",
      description: "Bespoke plugin and theme development"
    },
    {
      icon: <Globe className="text-purple-400" size={24} />,
      title: "Website Maintenance",
      description: "Regular updates, security patches, and performance optimization"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Basic WordPress Setup",
        "Responsive Design",
        "Essential Plugins",
        "Basic SEO Setup",
        "1 Month Support"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced WordPress Setup",
        "Custom Theme Development",
        "Premium Plugins",
        "Advanced SEO",
        "WooCommerce Integration",
        "3 Months Support"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Full Custom Development",
        "Custom Plugin Development",
        "Multi-site Setup",
        "Advanced Security",
        "Performance Optimization",
        "12 Months Support"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="CMS Development"
      description="Professional WordPress and WooCommerce development services for your business"
      features={features}
      pricing={pricing}
      technologies={['WordPress', 'WooCommerce', 'PHP', 'MySQL', 'JavaScript']}
      codeExample={`// Example WordPress Plugin Code
add_action('init', 'custom_post_type_init');

function custom_post_type_init() {
  register_post_type('product', [
    'labels' => [
      'name' => 'Products',
      'singular_name' => 'Product'
    ],
    'public' => true,
    'has_archive' => true,
    'supports' => [
      'title',
      'editor',
      'thumbnail'
    ]
  ]);
}`}
    />
  );
};

export default CMSDevelopment;
