
import { Mail, MapPin, Phone } from 'lucide-react';

const TopBar = () => {
  return (
    <div className="w-full py-2 text-xs sm:text-sm theme-border" style={{ 
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      borderBottomWidth: '1px',
      borderBottomStyle: 'solid',
      color: 'var(--color-text-secondary)'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-2 md:hidden">
          <span className="text-white/90">Innovative, Artful Solutions</span>
          
          <div className="flex items-center gap-2">
            <a href="tel:+447380187649" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +44 (738) 187-649
            </a>
            <a href="tel:+94770180044" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +94 (770) 180-044
            </a>
          </div>

          <a href="mailto:<EMAIL>" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
            <Mail size={14} className="mr-1.5 theme-icon" />
            <EMAIL>
          </a>

          <div className="flex items-center gap-2">
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              London, United Kingdom
            </span>
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              Colombo, Sri Lanka
            </span>
          </div>
        </div>

        <div className="hidden md:flex lg:hidden flex-col items-center gap-2">
          <span className="text-white/90">Innovative, Artful Solutions</span>
          
          <div className="flex items-center gap-2">
            <a href="tel:+447380187649" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +44 (738) 187-649
            </a>
            <a href="tel:+94770180044" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +94 (770) 180-044
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Mail size={14} className="mr-1.5 theme-icon" />
              <EMAIL>
            </a>
          </div>

          <div className="flex items-center gap-2">
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              London, United Kingdom
            </span>
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              Colombo, Sri Lanka
            </span>
          </div>
        </div>

        <div className="hidden lg:flex items-center justify-between">
          <span className="text-white/90">Innovative, Artful Solutions</span>
          
          <div className="flex items-center gap-6">
            <a href="tel:+447380187649" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +44 (738) 187-649
            </a>
            <a href="tel:+94770180044" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Phone size={14} className="mr-1.5 theme-icon" />
              +94 (770) 180-044
            </a>
            <a href="mailto:<EMAIL>" className="flex items-center transition-colors duration-200" style={{ color: 'var(--color-text-secondary)' }} onMouseOver={(e) => { e.currentTarget.style.color = 'var(--color-primary)'; }} onMouseOut={(e) => { e.currentTarget.style.color = 'var(--color-text-secondary)'; }}>
              <Mail size={14} className="mr-1.5 theme-icon" />
              <EMAIL>
            </a>
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              London, United Kingdom
            </span>
            <span className="flex items-center">
              <MapPin size={14} className="mr-1.5 theme-icon" />
              Colombo, Sri Lanka
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
