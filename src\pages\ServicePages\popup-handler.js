let currentGame = null;
let gameInstance = null;

function openGame(gameType) {
    const popup = document.getElementById('game-popup');
    const canvas = document.getElementById('game-canvas');
    
    // Set canvas size
    canvas.width = Math.min(800, window.innerWidth - 40);
    canvas.height = Math.min(600, window.innerHeight - 100);

    // Show popup
    popup.style.display = 'block';
    
    try {
        console.log('Starting game:', gameType);
        
        // Initialize game
        if (gameType === 'mario') {
            gameInstance = new MarioGame(canvas);
            console.log('Mario game instance created');
        } else if (gameType === 'webgl') {
            gameInstance = new WebGLDemo(canvas);
            console.log('WebGL demo instance created');
        }
        
        currentGame = gameType;
        
        // Fixed the condition for starting the game
        if (gameInstance && (gameType !== 'webgl' || (gameType === 'webgl' && gameInstance.gl))) {
            console.log('Starting game instance');
            gameInstance.start();
        } else {
            console.error('Failed to initialize game instance');
        }
    } catch (error) {
        console.error('Error starting game:', error);
    }

    // Auto-close after 2 minutes
    setTimeout(() => {
        closePopup();
    }, 120000);
}

function closePopup() {
    const popup = document.getElementById('game-popup');
    popup.style.display = 'none';
    
    if (gameInstance) {
        try {
            gameInstance.stop();
        } catch (error) {
            console.error('Error stopping game:', error);
        }
        gameInstance = null;
    }
}

// Handle window resize
window.addEventListener('resize', () => {
    try {
        const previewMarioCanvas = document.getElementById('mario-preview');
        if (previewMarioCanvas) {
            previewMarioCanvas.width = previewMarioCanvas.parentElement.clientWidth;
            previewMarioCanvas.height = previewMarioCanvas.parentElement.clientHeight;
            const marioPreview = new MarioGame(previewMarioCanvas);
            marioPreview.draw();
        }
        
        const previewWebGLCanvas = document.getElementById('webgl-preview');
        if (previewWebGLCanvas) {
            previewWebGLCanvas.width = previewWebGLCanvas.parentElement.clientWidth;
            previewWebGLCanvas.height = previewWebGLCanvas.parentElement.clientHeight;
            const webglPreview = new WebGLDemo(previewWebGLCanvas);
            if (webglPreview.gl) {
                webglPreview.draw();
            }
        }
    } catch (error) {
        console.error('Error handling resize:', error);
    }
});

// Initialize previews when the page loads
document.addEventListener('DOMContentLoaded', () => {
    try {
        const previewMarioCanvas = document.getElementById('mario-preview');
        if (previewMarioCanvas) {
            previewMarioCanvas.width = previewMarioCanvas.parentElement.clientWidth;
            previewMarioCanvas.height = previewMarioCanvas.parentElement.clientHeight;
            const marioPreview = new MarioGame(previewMarioCanvas);
            marioPreview.draw();
        }
        
        const previewWebGLCanvas = document.getElementById('webgl-preview');
        if (previewWebGLCanvas) {
            previewWebGLCanvas.width = previewWebGLCanvas.parentElement.clientWidth;
            previewWebGLCanvas.height = previewWebGLCanvas.parentElement.clientHeight;
            const webglPreview = new WebGLDemo(previewWebGLCanvas);
            if (webglPreview.gl) {
                webglPreview.draw();
            }
        }
    } catch (error) {
        console.error('Error initializing previews:', error);
    }
});
