<?php
// Prevent any output before headers
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Allow all origins during development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Function to send JSON response
function sendJsonResponse($success, $error = null) {
    echo json_encode([
        'success' => $success,
        'error' => $error
    ]);
    exit();
}

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get POST data
        $jsonData = file_get_contents('php://input');
        $data = json_decode($jsonData, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data: ' . json_last_error_msg());
        }

        // Validate required fields
        $requiredFields = ['firstName', 'lastName', 'email', 'service', 'description'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Load PHPMailer
        require 'vendor/autoload.php';
        $mail = new PHPMailer\PHPMailer\PHPMailer();

        // SMTP Configuration
        $mail->isSMTP();
        $mail->Host = 'mail.spirelab.net';
        $mail->SMTPAuth = true;
        $mail->Username = 'your_username'; // Replace with your SMTP username
        $mail->Password = 'your_password'; // Replace with your SMTP password
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;

        // Email settings
        $mail->setFrom('<EMAIL>', 'Spirelab Contact Form');
        $mail->addAddress('<EMAIL>');
        $mail->addReplyTo($data['email'], $data['firstName'] . ' ' . $data['lastName']);
        $mail->isHTML(true);
        $mail->Subject = 'New Contact Form Submission - ' . $data['service'];
        $mail->Body = "
        <html>
        <head>
            <title>New Contact Form Submission</title>
        </head>
        <body>
            <h2>New Contact Form Submission</h2>
            <p><strong>Name:</strong> {$data['firstName']} {$data['lastName']}</p>
            <p><strong>Email:</strong> {$data['email']}</p>
            <p><strong>Phone:</strong> " . (isset($data['phone']) ? $data['phone'] : 'Not provided') . "</p>
            <p><strong>Service:</strong> {$data['service']}</p>
            <p><strong>Description:</strong></p>
            <p>" . nl2br(htmlspecialchars($data['description'])) . "</p>
        </body>
        </html>
        ";

        // Send email
        if (!$mail->send()) {
            throw new Exception('Failed to send email: ' . $mail->ErrorInfo);
        }

        sendJsonResponse(true);

    } catch (Exception $e) {
        error_log('Mail Error: ' . $e->getMessage());
        http_response_code(500);
        sendJsonResponse(false, $e->getMessage());
    }
} else {
    http_response_code(405);
    sendJsonResponse(false, 'Method not allowed');
}
?>
