import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Headphones, Terminal, Settings, Clock } from 'lucide-react';

const RemoteITSupport = () => {
  const features = [
    {
      icon: <Headphones className="text-purple-400" size={24} />,
      title: "24/7 Helpdesk",
      description: "Round-the-clock technical support and assistance"
    },
    {
      icon: <Terminal className="text-purple-400" size={24} />,
      title: "Remote Troubleshooting",
      description: "Quick resolution of technical issues remotely"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "System Maintenance",
      description: "Regular maintenance and system optimization"
    },
    {
      icon: <Clock className="text-purple-400" size={24} />,
      title: "Proactive Monitoring",
      description: "Continuous monitoring to prevent issues"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Business Hours Support",
        "Email Support",
        "Remote Assistance",
        "Basic Monitoring",
        "Monthly Reports"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "24/7 Support",
        "Priority Response",
        "Advanced Monitoring",
        "System Optimization",
        "Weekly Reports",
        "Dedicated Support Team"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom SLA",
        "On-site Support",
        "Preventive Maintenance",
        "Custom Reporting",
        "Technology Consulting",
        "Strategic Planning"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="Remote IT Support"
      description="24/7 technical support and helpdesk services for your business"
      features={features}
      pricing={pricing}
      technologies={['Remote Desktop', 'Help Desk Software', 'Monitoring Tools', 'Ticketing Systems']}
      codeExample={`// Support Ticket Management System
class SupportTicket {
  constructor(priority, description) {
    this.id = generateTicketId();
    this.priority = priority;
    this.description = description;
    this.status = 'open';
    this.createdAt = new Date();
    this.sla = calculateSLA(priority);
  }

  async assignToAgent(agentId) {
    const agent = await getAvailableAgent(agentId);
    this.assignedTo = agent;
    this.status = 'assigned';
    notifyAgent(agent, this);
  }

  async updateStatus(newStatus) {
    this.status = newStatus;
    if (newStatus === 'resolved') {
      await sendCustomerSatisfactionSurvey(this);
    }
  }

  calculateTimeToResolution() {
    return Date.now() - this.createdAt;
  }
}`}
    />
  );
};

export default RemoteITSupport;
