import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { PowerGlitch } from 'powerglitch';

interface ClientInfo {
  name: string;
  logo: string;
  description: string;
  industry: string;
  website?: string;
}

const ClientCard = ({ client, index }: { client: ClientInfo; index: number }) => {
  const logoRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = React.useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsVisible(true);
    }, index * 100);

    return () => clearTimeout(timeout);
  }, [index]);

  const handleMouseEnter = () => {
    if (logoRef.current) {
      PowerGlitch.glitch(logoRef.current, {
        timing: {
          duration: 2000,
          iterations: 1
        },
        glitchTimeSpan: {
          start: 0.4,
          end: 0.7
        },
        shake: {
          velocity: 15,
          amplitudeX: 0.2,
          amplitudeY: 0.2
        },
        slice: {
          count: 6,
          velocity: 15,
          minHeight: 0.02,
          maxHeight: 0.15,
          hueRotate: true
        },
        pulse: false
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-500/50 transition-all duration-300 group"
      onMouseEnter={handleMouseEnter}
    >
      <motion.div 
        className="relative h-32 mb-6 bg-white/5 rounded-lg overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: isVisible ? 1 : 0 }}
        transition={{ duration: 0.5 }}
      >
        <div 
          ref={logoRef} 
          className="absolute inset-0 flex items-center justify-center"
        >
          <div className="relative w-full h-full p-4 flex items-center justify-center">
            <img
              src={client.logo}
              alt={client.name}
              className="opacity-75 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                width: 'auto',
                height: 'auto',
                objectFit: 'contain',
                transform: 'scale(0.9)',
                transition: 'transform 0.3s ease'
              }}
              onLoad={(e) => {
                const img = e.target as HTMLImageElement;
                const container = img.parentElement;
                if (container) {
                  const containerAspect = container.clientWidth / container.clientHeight;
                  const imageAspect = img.naturalWidth / img.naturalHeight;
                  if (imageAspect > containerAspect) {
                    img.style.width = '90%';
                    img.style.height = 'auto';
                  } else {
                    img.style.height = '90%';
                    img.style.width = 'auto';
                  }
                }
              }}
            />
          </div>
        </div>
      </motion.div>
      <h3 className="text-xl font-mono font-bold mb-2 text-purple-400">{client.name}</h3>
      <p className="text-sm text-gray-400 mb-4">{client.industry}</p>
      <p className="text-gray-300 text-sm mb-4">{client.description}</p>
      {client.website && (
        <a
          href={client.website}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block text-sm text-purple-400 hover:text-purple-300 transition-colors font-mono"
        >
          Visit Website →
        </a>
      )}
    </motion.div>
  );
};

const clients: ClientInfo[] = [
  {
    name: "Barber Shop",
    logo: "/logos/Barber_Shop.webp",
    description: "A premium men's grooming establishment offering classic cuts and modern styling services in a sophisticated atmosphere.",
    industry: "Personal Care & Services",
  },
  {
    name: "Bicycle Shop",
    logo: "/logos/Bicycle_Shop.webp",
    description: "Specialized bicycle retailer offering premium bikes, accessories, and expert maintenance services for cycling enthusiasts.",
    industry: "Retail & Sports",
  },
  {
    name: "Bimputh PLC",
    logo: "/logos/Bimputh_PLC.png",
    description: "A leading microfinance institution in Sri Lanka, providing financial services to empower rural communities and small businesses.",
    industry: "Financial Services",
    website: "https://www.bimputhfinance.com"
  },
  {
    name: "Celio",
    logo: "/logos/Celio.svg",
    description: "International men's fashion brand known for contemporary casual wear and accessories with a French design aesthetic.",
    industry: "Fashion Retail",
    website: "https://www.celio.com"
  },
  {
    name: "Cloud Tunes",
    logo: "/logos/Cloud_Tunes.png",
    description: "Digital music streaming platform offering personalized playlists and high-quality audio streaming services.",
    industry: "Entertainment & Technology",
  },
  {
    name: "Daya Group",
    logo: "/logos/Daya_Group.png",
    description: "Diversified conglomerate with interests in manufacturing, trading, and services sectors across Asia.",
    industry: "Conglomerate",
  },
  {
    name: "DMN Super",
    logo: "/logos/DMN_Super.jpg",
    description: "Modern supermarket chain offering fresh produce, groceries, and household essentials with a focus on customer service.",
    industry: "Retail",
  },
  {
    name: "Edlocate",
    logo: "/logos/Edlocate.png",
    description: "Educational technology platform connecting students with quality learning resources and experienced educators worldwide.",
    industry: "Education Technology",
  },
  {
    name: "Excel BPO",
    logo: "/logos/Excel_BPO.png",
    description: "Business process outsourcing company providing customer service, technical support, and back-office solutions.",
    industry: "Business Services",
  },
  {
    name: "Fitness Center",
    logo: "/logos/Fitness Center.webp",
    description: "State-of-the-art fitness facility offering personal training, group classes, and modern exercise equipment.",
    industry: "Health & Fitness",
  },
  {
    name: "I Fly",
    logo: "/logos/I Fly - Liberty Traveks.png",
    description: "Premium travel and aviation services provider offering exceptional flight experiences and travel solutions.",
    industry: "Aviation & Travel",
  },
  {
    name: "Jing Seasons",
    logo: "/logos/Jing_Seasons.png",
    description: "Asian fusion restaurant known for seasonal menus and authentic flavors using premium ingredients.",
    industry: "Food & Beverage",
  },
  {
    name: "Liberty Travels",
    logo: "/logos/Liberty_Travels.png",
    description: "Full-service travel agency specializing in luxury vacations and corporate travel management solutions.",
    industry: "Travel & Tourism",
  },
  {
    name: "Monorays",
    logo: "/logos/Monorays_lite_web.png",
    description: "Leading civil engineering company specializing in infrastructure development, construction, and project management.",
    industry: "Civil Engineering",
  },
  {
    name: "Natural",
    logo: "/logos/Natural.webp",
    description: "Organic and natural products retailer offering sustainable lifestyle products and wellness solutions.",
    industry: "Retail & Wellness",
  },
  {
    name: "Nestle",
    logo: "/logos/Nestle.png",
    description: "Global food and beverage company known for its commitment to nutrition, health, and wellness products.",
    industry: "Food & Beverage",
    website: "https://www.nestle.com"
  },
  {
    name: "Onaro Online",
    logo: "/logos/onaro_online.webp",
    description: "E-commerce platform providing a seamless online shopping experience with a wide range of products.",
    industry: "E-commerce",
  },
  {
    name: "Rescue Animals",
    logo: "/logos/Rescue-Animals-Logo-NEW.jpeg",
    description: "Non-profit organization dedicated to animal welfare, rescue operations, and adoption services.",
    industry: "Non-Profit",
  },
  {
    name: "Science Abode",
    logo: "/logos/Science_Abode.png",
    description: "Educational institution focusing on scientific research and STEM education programs.",
    industry: "Education",
  },
  {
    name: "Stallion",
    logo: "/logos/STALLION.png",
    description: "Premium automotive brand known for high-performance vehicles and exceptional customer service.",
    industry: "Automotive",
  },
  {
    name: "Sync Sound Design",
    logo: "/logos/Sync_Sound_Design.png",
    description: "Professional audio production studio offering sound design, mixing, and post-production services.",
    industry: "Entertainment",
  },
  {
    name: "Twin Fish",
    logo: "/logos/Twin_Fish.webp",
    description: "Seafood restaurant chain known for fresh ingredients and innovative fusion cuisine.",
    industry: "Food & Beverage",
  }
];

const ClientPortfolioPage = () => {
  return (
    <div className="min-h-screen bg-black text-white py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-mono font-bold mb-4">Our Client Portfolio</h1>
          <p className="text-gray-400 font-mono">Trusted by industry leaders across various sectors</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {clients.map((client, index) => (
            <ClientCard key={client.name} client={client} index={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ClientPortfolioPage;
