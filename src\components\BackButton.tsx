import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const BackButton = () => {
  const navigate = useNavigate();

  return (
    <button
      onClick={() => navigate('/')}
      className="absolute top-24 left-8 flex items-center space-x-2 text-[#9ca3af] hover:text-white transition-colors font-mono text-sm"
      aria-label="Go back"
    >
      <ArrowLeft className="w-4 h-4" />
      <span>Back to Home</span>
    </button>
  );
};

export default BackButton;
