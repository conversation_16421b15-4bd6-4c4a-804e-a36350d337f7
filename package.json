{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "tsx src/server/index.ts", "start": "concurrently \"npm run server\" \"npm run preview\""}, "dependencies": {"@emailjs/browser": "^4.4.1", "@tsparticles/basic": "^3.8.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/preset-fire": "^3.1.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.16", "@types/pg": "^8.15.4", "@types/three": "^0.177.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.1", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "openai": "^5.5.1", "pg": "^8.16.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-iframe": "^1.8.5", "react-intersection-observer": "^9.8.1", "react-powerglitch": "^1.0.3", "react-router-dom": "^6.22.2", "react-speech-recognition": "^4.0.1", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/line-clamp": "^0.4.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}