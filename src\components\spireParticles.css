.spire-particles-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
  perspective: 1000px;
}

.particle {
  position: absolute;
  pointer-events: none;
  transform-origin: center center;
  will-change: transform, opacity;
}

/* Star particle styling */
.star-particle {
  position: absolute;
  clip-path: polygon(
    50% 0%, 
    61% 35%, 
    98% 35%, 
    68% 57%, 
    79% 91%, 
    50% 70%, 
    21% 91%, 
    32% 57%, 
    2% 35%, 
    39% 35%
  );
  background: radial-gradient(circle at center, #ffffff 0%, #e0e0ff 50%, #a0a0ff 100%);
  box-shadow: 
    0 0 5px 1px rgba(255, 255, 255, 0.8),
    0 0 10px 2px rgba(160, 160, 255, 0.4);
  animation: starTwinkle 3s infinite alternate;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.6));
}

/* Add a glow effect to some stars */
.star-particle:nth-child(3n) {
  background: radial-gradient(circle at center, #ffffff 0%, #ffe0e0 50%, #ffa0a0 100%);
  box-shadow: 
    0 0 5px 1px rgba(255, 255, 255, 0.8),
    0 0 10px 2px rgba(255, 160, 160, 0.4);
}

.star-particle:nth-child(5n) {
  background: radial-gradient(circle at center, #ffffff 0%, #e0ffff 50%, #a0ffff 100%);
  box-shadow: 
    0 0 5px 1px rgba(255, 255, 255, 0.8),
    0 0 10px 2px rgba(160, 255, 255, 0.4);
}

/* Spire particle styling */
.spire-particle {
  position: absolute;
  clip-path: polygon(
    50% 0%,
    65% 40%,
    50% 100%,
    35% 40%
  );
  background: linear-gradient(to bottom, 
    rgba(255, 80, 80, 0.9) 0%, 
    rgba(255, 50, 80, 0.8) 40%, 
    rgba(156, 30, 47, 0.9) 100%);
  box-shadow: 
    0 0 8px 2px rgba(255, 50, 80, 0.6),
    0 0 15px 4px rgba(255, 50, 80, 0.3);
  animation: spireGlow 4s infinite alternate;
  filter: drop-shadow(0 0 3px rgba(255, 50, 80, 0.5));
}

/* Add variations to spires */
.spire-particle:nth-child(3n) {
  clip-path: polygon(
    50% 0%,
    70% 30%,
    50% 100%,
    30% 30%
  );
  background: linear-gradient(to bottom, 
    rgba(255, 100, 50, 0.9) 0%, 
    rgba(255, 70, 30, 0.8) 40%, 
    rgba(180, 40, 20, 0.9) 100%);
}

.spire-particle:nth-child(5n) {
  clip-path: polygon(
    50% 0%,
    60% 50%,
    50% 100%,
    40% 50%
  );
  background: linear-gradient(to bottom, 
    rgba(255, 50, 100, 0.9) 0%, 
    rgba(220, 30, 80, 0.8) 40%, 
    rgba(150, 20, 60, 0.9) 100%);
}

/* Animations */
@keyframes starTwinkle {
  0% {
    opacity: 0.3;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.4));
  }
  50% {
    opacity: 0.8;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
  }
  100% {
    opacity: 0.4;
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.6));
  }
}

@keyframes spireGlow {
  0% {
    filter: drop-shadow(0 0 3px rgba(255, 50, 80, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 10px rgba(255, 50, 80, 0.7));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(255, 50, 80, 0.5));
  }
}

/* Add subtle rotation to some particles */
.star-particle:nth-child(4n) {
  animation: starTwinkle 3s infinite alternate, starRotate 20s linear infinite;
}

.spire-particle:nth-child(4n) {
  animation: spireGlow 4s infinite alternate, spireRotate 15s linear infinite;
}

@keyframes starRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spireRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Add media query for smaller screens */
@media (max-width: 768px) {
  .star-particle, .spire-particle {
    transform: scale(0.7);
  }
}
