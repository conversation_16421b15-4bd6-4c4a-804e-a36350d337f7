

const Footer = () => {
  return (
    <footer className="bg-black border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-mono font-semibold mb-4">Spirelab</h3>
            <p className="text-gray-400 font-mono text-sm mb-4">
              We work with a passion of taking challenges and creating new ones in advertising sector
            </p>
            <img 
              src="/logos/Spirelab_Lite_logo.png" 
              alt="Spirelab Solutions" 
              className="h-12 w-auto"
            />
          </div>
          
          <div>
            <h3 className="text-lg font-mono font-semibold mb-4">Company</h3>
            <ul className="space-y-2 text-gray-400 font-mono text-sm">
              <li><a href="#about" className="hover:text-white">About</a></li>
              <li><a href="#careers" className="hover:text-white">Careers</a></li>
              <li><a href="#contact" className="hover:text-white">Contact</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-mono font-semibold mb-4">UK Office</h3>
            <address className="text-gray-400 font-mono text-sm not-italic">
              23 New Drum Street<br />
              London<br />
              United Kingdom<br />
              E1 7AY<br />
              <a href="tel:+447380187649" className="hover:text-white">+44 (738) 0187-649</a>
            </address>
          </div>
          
          <div>
            <h3 className="text-lg font-mono font-semibold mb-4">SL Office</h3>
            <address className="text-gray-400 font-mono text-sm not-italic">
              316 2nd Floor<br />
              Super Market Complex<br />
              Mount Lavinia<br />
              Sri Lanka<br />
              <a href="tel:+94770180044" className="hover:text-white">+94 (770) 180-044</a>
            </address>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 font-mono text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} Spirelab Solutions Ltd. All rights reserved.
          </p>
          <div className="flex space-x-4 font-mono text-sm">
            <a href="/terms-of-service" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a>
            <span className="text-gray-600">|</span>
            <a href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
            <span className="text-gray-600">|</span>
            <a href="/product-privacy-policy" className="text-gray-400 hover:text-white transition-colors">Product Privacy Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
