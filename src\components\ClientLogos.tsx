import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { PowerGlitch } from 'powerglitch';

const LogoItem = ({ logo, name }: { logo: string; name: string }) => {
  const logoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (logoRef.current) {
      PowerGlitch.glitch(logoRef.current, {
        timing: {
          duration: 2000,
          iterations: Infinity
        },
        glitchTimeSpan: {
          start: 0.4,
          end: 0.7
        },
        shake: {
          velocity: 15,
          amplitudeX: 0.2,
          amplitudeY: 0.2
        },
        slice: {
          count: 6,
          velocity: 15,
          minHeight: 0.02,
          maxHeight: 0.15,
          hueRotate: true
        },
        pulse: false
      });
    }
  }, []);

  return (
    <div className="flex-shrink-0 w-48 group">
      <div className="relative aspect-video bg-white/5 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10 hover:border-purple-500/50 transition-all duration-300">
        <div className="absolute inset-0 flex flex-col items-center justify-center p-4 z-10">
          <div ref={logoRef}>
            <img 
              src={logo} 
              alt={name}
              className="h-12 object-contain brightness-75 group-hover:brightness-100 transition-all duration-300"
            />
          </div>
          <span className="mt-2 font-mono text-sm text-gray-400 group-hover:text-purple-400 transition-colors">
            {name}
          </span>
        </div>
      </div>
    </div>
  );
};

const ClientLogos = () => {
  const clients = [
    {
      name: "Barber Shop",
      logo: "/logos/Barber_Shop.webp"
    },
    {
      name: "Bicycle Shop",
      logo: "/logos/Bicycle_Shop.webp"
    },
    {
      name: "Bimputh PLC",
      logo: "/logos/Bimputh_PLC.png"
    },
    {
      name: "Celio",
      logo: "/logos/Celio.svg"
    },
    {
      name: "Cloud Tunes",
      logo: "/logos/Cloud_Tunes.png"
    },
    {
      name: "Daya Group",
      logo: "/logos/Daya_Group.png"
    },
    {
      name: "DMN Super",
      logo: "/logos/DMN_Super.jpg"
    },
    {
      name: "Edlocate",
      logo: "/logos/Edlocate.png"
    },
    {
      name: "Excel BPO",
      logo: "/logos/Excel_BPO.png"
    },
    {
      name: "Fitness Center",
      logo: "/logos/Fitness Center.webp"
    },
    {
      name: "I Fly",
      logo: "/logos/I Fly - Liberty Traveks.png"
    },
    {
      name: "Jing Seasons",
      logo: "/logos/Jing_Seasons.png"
    },
    {
      name: "Liberty Travels",
      logo: "/logos/Liberty_Travels.png"
    },
    {
      name: "Lion",
      logo: "/logos/Lion.png"
    },
    {
      name: "Monorays",
      logo: "/logos/Monorays_lite_web.png"
    },
    {
      name: "Natural",
      logo: "/logos/Natural.webp"
    },
    {
      name: "Nestle",
      logo: "/logos/Nestle.png"
    },
    {
      name: "Onaro Online",
      logo: "/logos/onaro_online.webp"
    },
    {
      name: "Rescue Animals",
      logo: "/logos/Rescue-Animals-Logo-NEW.jpeg"
    },
    {
      name: "Science Abode",
      logo: "/logos/Science_Abode.png"
    },
    {
      name: "Stallion",
      logo: "/logos/STALLION.png"
    },
    {
      name: "Sync Sound Design",
      logo: "/logos/Sync_Sound_Design.png"
    },
    {
      name: "Twin Fish",
      logo: "/logos/Twin_Fish.webp"
    }
  ];

  const renderLogos = () => {
    return clients.map((client, index) => (
      <LogoItem key={index} logo={client.logo} name={client.name} />
    ));
  };

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-mono font-bold mb-4 glitch-text">Trusted By Industry Leaders</h2>
          <p className="text-gray-400 font-mono">Powering innovation across the tech landscape</p>
        </motion.div>

        <div className="relative overflow-hidden">
          {/* Mask containers with fixed black backgrounds instead of gradients */}
          <div className="absolute left-0 top-0 bottom-0 w-32 z-10 bg-black" />
          <div className="absolute right-0 top-0 bottom-0 w-32 z-10 bg-black" />
          
          {/* Logo scroller */}
          <div className="mx-32"> {/* Add margin to prevent logos from showing under the masks */}
            <div className="flex animate-slide">
              {renderLogos()}
              {renderLogos()}
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .animate-slide {
          animation: slide 15s linear infinite;
        }
        
        @keyframes slide {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
      `}</style>
    </section>
  );
};

export default ClientLogos;
