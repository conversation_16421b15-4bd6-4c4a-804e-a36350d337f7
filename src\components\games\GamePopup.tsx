import React, { useEffect } from 'react';
import MarioGame from './MarioGame';
import WebGLGame from './WebGLGame';
import SnakeGame from './SnakeGame';
import BreakoutGame from './BreakoutGame';
import FashionStoreGame from './FashionStoreGame';
import BankingSimulator from './BankingSimulator';
import RestaurantManager from './RestaurantManager';
import RealEstateTycoon from './RealEstateTycoon';

interface GamePopupProps {
  gameType: 'mario' | 'webgl' | 'snake' | 'breakout' | 'fashion' | 'banking' | 'restaurant' | 'realestate' | null;
  onClose: () => void;
}

const GamePopup: React.FC<GamePopupProps> = ({ gameType, onClose }) => {
  useEffect(() => {
    if (gameType) {
      const timer = setTimeout(() => {
        onClose();
      }, 120000); // Auto-close after 2 minutes

      return () => clearTimeout(timer);
    }
  }, [gameType, onClose]);

  if (!gameType) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg p-4 relative max-w-4xl w-full">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-white text-2xl hover:text-gray-300"
        >
          ×
        </button>
        <div className="w-full aspect-video">
          {gameType === 'mario' && <MarioGame />}
          {gameType === 'webgl' && <WebGLGame />}
          {gameType === 'snake' && <SnakeGame />}
          {gameType === 'breakout' && <BreakoutGame />}
          {gameType === 'fashion' && <FashionStoreGame />}
          {gameType === 'banking' && <BankingSimulator />}
          {gameType === 'restaurant' && <RestaurantManager />}
          {gameType === 'realestate' && <RealEstateTycoon />}
        </div>
      </div>
    </div>
  );
};

export default GamePopup;
