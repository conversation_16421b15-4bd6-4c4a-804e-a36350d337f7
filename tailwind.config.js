/** @type {import('tailwindcss').Config} */
import { readFileSync, existsSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// We'll use a function to load the theme config at runtime
function loadThemeConfig() {
  try {
    // This is a workaround since we can't directly import TypeScript in Tailwind config
    // In a production environment, you'd want to pre-compile this or use a different approach
    const themeConfigPath = resolve(__dirname, './src/config/theme.json');
    
    if (existsSync(themeConfigPath)) {
      return JSON.parse(readFileSync(themeConfigPath, 'utf8'));
    }
    
    // Default theme if file doesn't exist
    return {
      colors: {
        primary: '#a855f7',
        secondary: '#ec4899',
        accent: '#f97316',
        background: '#000000',
        text: {
          primary: '#ffffff',
          secondary: '#9ca3af'
        },
        icon: '#a855f7',
        highlight: '#a855f7'
      }
    };
  } catch (error) {
    console.error('Error loading theme config:', error);
    // Fallback to default values
    return {
      colors: {
        primary: '#a855f7',
        secondary: '#ec4899',
        accent: '#f97316',
        background: '#000000',
        text: {
          primary: '#ffffff',
          secondary: '#9ca3af'
        },
        icon: '#a855f7',
        highlight: '#a855f7'
      }
    };
  }
}

const themeConfig = loadThemeConfig();

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: themeConfig.colors.primary,
        secondary: themeConfig.colors.secondary,
        accent: themeConfig.colors.accent,
        background: themeConfig.colors.background,
      },
      textColor: {
        primary: themeConfig.colors.text.primary,
        secondary: themeConfig.colors.text.secondary,
      },
      backgroundColor: {
        primary: themeConfig.colors.primary,
        secondary: themeConfig.colors.secondary,
        accent: themeConfig.colors.accent,
        background: themeConfig.colors.background,
      },
      borderColor: {
        primary: themeConfig.colors.primary,
        secondary: themeConfig.colors.secondary,
        accent: themeConfig.colors.accent,
      },
      gradientColorStops: {
        'primary-start': themeConfig.colors.primary,
        'primary-end': themeConfig.colors.secondary,
      },
      fontFamily: {
        primary: [themeConfig.fontFamily.primary],
        secondary: [themeConfig.fontFamily.secondary],
      },
    },
  },
  plugins: [],
};
