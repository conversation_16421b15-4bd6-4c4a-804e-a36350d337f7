@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

:root {
  /* Main theme colors */
  --color-primary: #a855f7;
  --color-secondary: #ec4899;
  --color-accent: #f97316;
  --color-background: #000000;
  
  /* Text colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #9ca3af;
  
  /* Button styles */
  --color-button-primary-bg: #ffffff;
  --color-button-primary-text: #000000;
  --color-button-primary-hover: #f3f4f6;
  --color-button-secondary-bg: transparent;
  --color-button-secondary-text: #ffffff;
  --color-button-secondary-border: rgba(255, 255, 255, 0.2);
  --color-button-secondary-hover: rgba(255, 255, 255, 0.1);
  
  /* Card styles */
  --color-card-bg: rgba(255, 255, 255, 0.05);
  --color-card-border: rgba(255, 255, 255, 0.1);
  --color-card-hover-border: rgba(168, 85, 247, 0.5);
  
  /* Icon color */
  --color-icon: #a855f7;
  
  /* Highlight color */
  --color-highlight: #a855f7;
  
  /* Font families */
  --font-family-primary: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --font-family-secondary: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@layer base {
  body {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    font-family: var(--font-family-primary);
  }
}

/* Text Effects */
.text-gradient {
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-primary));
}

.text-glow {
  text-shadow: 0 0 10px var(--color-highlight);
}

.text-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.text-slide-up {
  animation: slideUp 0.5s ease-out;
}

.text-blur-in {
  animation: blurIn 0.5s ease-out;
}

.text-scale {
  animation: scale 0.5s ease-out;
}

.text-wave {
  animation: wave 2s ease-in-out infinite;
}

.text-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Animation Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blurIn {
  from {
    opacity: 0;
    filter: blur(10px);
  }
  to {
    opacity: 1;
    filter: blur(0);
  }
}

@keyframes scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Glitch Effect */
@keyframes glitch {
  0% {
    transform: translate(0);
    opacity: 1;
  }
  20% {
    transform: translate(-2px, 2px);
    opacity: 0.9;
  }
  40% {
    transform: translate(2px, -2px);
    opacity: 0.8;
  }
  60% {
    transform: translate(-2px, 2px);
    opacity: 0.9;
  }
  80% {
    transform: translate(2px, -2px);
    opacity: 0.8;
  }
  100% {
    transform: translate(0);
    opacity: 1;
  }
}

.animate-glitch {
  animation: glitch 0.3s cubic-bezier(.25, .46, .45, .94) both;
}

/* Slide Animation */
@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-slide {
  animation: slide 20s linear infinite;
  display: flex;
  gap: 2rem;
}

/* Terminal Styles */
.terminal-window {
  background-color: var(--color-background);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  overflow: hidden;
}

.terminal-header {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 9999px;
}

.terminal-content {
  padding: 1rem;
  font-family: var(--font-family-primary);
  font-size: 0.875rem;
}

/* Code Highlighting */
.code-highlight {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 0 0.25rem;
  border-radius: 0.25rem;
}

/* Matrix Background */
@keyframes matrix {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 100%; }
}

.matrix-bg {
  background: linear-gradient(45deg, 
    rgba(0,0,0,0.9) 0%,
    rgba(0,32,0,0.9) 50%,
    rgba(0,0,0,0.9) 100%
  );
  background-size: 400% 400%;
  animation: matrix 15s ease infinite;
}

/* Theme Classes */
.theme-hover-bg:hover {
  background-color: var(--color-primary);
  opacity: 0.2;
}

.theme-border {
  border-color: rgba(196, 30, 58, 0.3);
}

.theme-text {
  color: var(--color-primary);
}

.theme-bg {
  background-color: var(--color-primary);
}

.theme-icon {
  color: var(--color-icon);
}

.theme-dropdown {
  background-color: var(--color-background);
  border: 1px solid var(--color-card-border);
}

.theme-dropdown-item:hover {
  background-color: var(--color-primary);
  opacity: 0.2;
  color: white;
}