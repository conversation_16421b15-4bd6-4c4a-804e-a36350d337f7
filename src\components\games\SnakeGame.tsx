import React, { useEffect, useRef } from 'react';

interface Position {
  x: number;
  y: number;
}

interface GameState {
  snake: Position[];
  food: Position;
  direction: 'up' | 'down' | 'left' | 'right';
  score: number;
  gameOver: boolean;
}

class SnakeGameLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  gridSize: number = 20;
  state: GameState;
  isRunning: boolean = false;
  speed: number = 150;
  lastUpdate: number = 0;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    this.state = {
      snake: [{ x: 5, y: 5 }],
      food: this.generateFood(),
      direction: 'right',
      score: 0,
      gameOver: false
    };

    window.addEventListener('keydown', this.handleKeyPress);
  }

  generateFood(): Position {
    const maxX = Math.floor(this.width / this.gridSize) - 1;
    const maxY = Math.floor(this.height / this.gridSize) - 1;
    return {
      x: Math.floor(Math.random() * maxX),
      y: Math.floor(Math.random() * maxY)
    };
  }

  handleKeyPress = (e: KeyboardEvent) => {
    if (!this.isRunning) return;

    switch (e.key) {
      case 'ArrowUp':
        if (this.state.direction !== 'down') this.state.direction = 'up';
        break;
      case 'ArrowDown':
        if (this.state.direction !== 'up') this.state.direction = 'down';
        break;
      case 'ArrowLeft':
        if (this.state.direction !== 'right') this.state.direction = 'left';
        break;
      case 'ArrowRight':
        if (this.state.direction !== 'left') this.state.direction = 'right';
        break;
    }
  };

  update(timestamp: number) {
    if (!this.isRunning || this.state.gameOver) return;
    if (timestamp - this.lastUpdate < this.speed) return;

    this.lastUpdate = timestamp;

    const head = { ...this.state.snake[0] };
    switch (this.state.direction) {
      case 'up': head.y--; break;
      case 'down': head.y++; break;
      case 'left': head.x--; break;
      case 'right': head.x++; break;
    }

    // Check wall collision
    if (
      head.x < 0 || 
      head.x >= this.width / this.gridSize ||
      head.y < 0 || 
      head.y >= this.height / this.gridSize
    ) {
      this.state.gameOver = true;
      return;
    }

    // Check self collision
    if (this.state.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
      this.state.gameOver = true;
      return;
    }

    // Check food collision
    if (head.x === this.state.food.x && head.y === this.state.food.y) {
      this.state.score += 100;
      this.state.food = this.generateFood();
      this.speed = Math.max(50, this.speed - 2); // Increase speed
    } else {
      this.state.snake.pop();
    }

    this.state.snake.unshift(head);
  }

  draw() {
    // Clear canvas
    this.ctx.fillStyle = '#1a1a1a';
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw snake
    this.ctx.fillStyle = '#4ade80';
    this.state.snake.forEach((segment, index) => {
      this.ctx.fillRect(
        segment.x * this.gridSize,
        segment.y * this.gridSize,
        this.gridSize - 2,
        this.gridSize - 2
      );
    });

    // Draw food
    this.ctx.fillStyle = '#ef4444';
    this.ctx.fillRect(
      this.state.food.x * this.gridSize,
      this.state.food.y * this.gridSize,
      this.gridSize - 2,
      this.gridSize - 2
    );

    // Draw score
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Score: ${this.state.score}`, 10, 30);

    if (this.state.gameOver) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.width, this.height);
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '30px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('GAME OVER', this.width/2, this.height/2);
      this.ctx.font = '20px Arial';
      this.ctx.fillText(`Final Score: ${this.state.score}`, this.width/2, this.height/2 + 40);
    }
  }

  start() {
    this.isRunning = true;
    this.gameLoop(0);
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = (timestamp: number) => {
    if (!this.isRunning) return;

    this.update(timestamp);
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface SnakeGameProps {
  isPreview?: boolean;
}

const SnakeGame: React.FC<SnakeGameProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<SnakeGameLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new SnakeGameLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default SnakeGame;
