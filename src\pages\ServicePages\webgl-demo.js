class WebGLDemo {
    constructor(canvas) {
        this.canvas = canvas;
        this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (!this.gl) {
            console.error('WebGL not supported');
            return;
        }

        this.initShaders();
        this.initBuffers();
        this.squares = [
            { x: -0.5, y: 0.5, vx: 0.01, vy: 0.005 },
            { x: 0.5, y: -0.5, vx: -0.01, vy: -0.005 }
        ];
        
        this.isRunning = false;
    }

    initShaders() {
        // Vertex shader
        const vsSource = `
            attribute vec4 aVertexPosition;
            uniform vec2 uOffset;
            void main() {
                gl_Position = vec4(aVertexPosition.xy + uOffset, 0.0, 1.0);
            }
        `;

        // Fragment shader
        const fsSource = `
            precision mediump float;
            void main() {
                gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
            }
        `;

        const vertexShader = this.compileShader(vsSource, this.gl.VERTEX_SHADER);
        const fragmentShader = this.compileShader(fsSource, this.gl.FRAGMENT_SHADER);

        // Create program
        this.program = this.gl.createProgram();
        this.gl.attachShader(this.program, vertexShader);
        this.gl.attachShader(this.program, fragmentShader);
        this.gl.linkProgram(this.program);

        if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
            console.error('Unable to initialize shader program');
            return;
        }

        this.gl.useProgram(this.program);
    }

    compileShader(source, type) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);

        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            console.error('Shader compile error:', this.gl.getShaderInfoLog(shader));
            this.gl.deleteShader(shader);
            return null;
        }

        return shader;
    }

    initBuffers() {
        // Create square
        const positions = [
            -0.1,  0.1,
             0.1,  0.1,
            -0.1, -0.1,
             0.1, -0.1,
        ];

        const positionBuffer = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, positionBuffer);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(positions), this.gl.STATIC_DRAW);

        const vertexPosition = this.gl.getAttribLocation(this.program, 'aVertexPosition');
        this.gl.enableVertexAttribArray(vertexPosition);
        this.gl.vertexAttribPointer(vertexPosition, 2, this.gl.FLOAT, false, 0, 0);

        this.offsetUniform = this.gl.getUniformLocation(this.program, 'uOffset');
    }

    update() {
        if (!this.isRunning) return;
        
        // Update square positions
        this.squares.forEach(square => {
            square.x += square.vx;
            square.y += square.vy;

            // Bounce off walls
            if (Math.abs(square.x) > 0.9) square.vx *= -1;
            if (Math.abs(square.y) > 0.9) square.vy *= -1;
        });
    }

    draw() {
        if (!this.gl) return;

        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        this.gl.clearColor(0.0, 0.0, 0.0, 1.0);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Draw each square
        this.squares.forEach(square => {
            this.gl.uniform2f(this.offsetUniform, square.x, square.y);
            this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
        });
    }

    start() {
        if (!this.gl) return;
        this.isRunning = true;
        this.animate();
    }

    stop() {
        this.isRunning = false;
    }

    animate() {
        if (!this.isRunning) return;

        this.update();
        this.draw();
        requestAnimationFrame(() => this.animate());
    }

    resize(width, height) {
        if (!this.gl) return;
        this.canvas.width = width;
        this.canvas.height = height;
        this.gl.viewport(0, 0, width, height);
    }
}

// Initialize preview
const previewCanvas = document.getElementById('webgl-preview');
if (previewCanvas) {
    previewCanvas.width = previewCanvas.parentElement.clientWidth;
    previewCanvas.height = previewCanvas.parentElement.clientHeight;
    const previewDemo = new WebGLDemo(previewCanvas);
    if (previewDemo.gl) {
        previewDemo.draw();
    }
}
