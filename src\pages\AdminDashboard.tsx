import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getProjects, deleteProject, login, logout, isAuthenticated, getCurrentUser } from '../services/showcaseService';
import { Project, User } from '../types/showcase';
import { Edit, Trash2, Plus, ExternalLink, LogOut, Lock } from 'lucide-react';
import ProjectForm from '../components/ProjectForm';

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(isAuthenticated());
  const [user, setUser] = useState<User | null>(getCurrentUser());
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loginError, setLoginError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  
  useEffect(() => {
    // Check authentication status on component mount
    const authStatus = isAuthenticated();
    const currentUserData = getCurrentUser();

    setIsLoggedIn(authStatus);
    setUser(currentUserData);

    if (authStatus) {
      fetchProjects();
    }
  }, []);

  useEffect(() => {
    if (isLoggedIn) {
      fetchProjects();
    }
  }, [isLoggedIn]);
  
  const fetchProjects = async () => {
    try {
      setLoading(true);
      const data = await getProjects();
      setProjects(data);
      setError(null);
    } catch (err) {
      setError('Failed to load projects. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoginError(null);

      // Use the actual login service
      const userData = await login(email, password);
      console.log('Login successful:', userData);

      setUser(userData);
      setIsLoggedIn(true);
      setEmail('');
      setPassword('');
    } catch (err) {
      setLoginError('Invalid credentials. Please try again.');
      console.error(err);
    }
  };
  
  const handleLogout = () => {
    logout(); // Call the logout function from showcaseService
    setIsLoggedIn(false);
    setUser(null);
    navigate('/showcase');
  };
  
  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this project?')) {
      return;
    }
    
    try {
      await deleteProject(id);
      setProjects(projects.filter(project => project.id !== id));
    } catch (err) {
      setError('Failed to delete project. Please try again.');
      console.error(err);
    }
  };
  
  const handleEdit = (project: Project) => {
    setEditingProject(project);
    setShowForm(true);
  };
  
  const handleFormClose = () => {
    setShowForm(false);
    setEditingProject(null);
    fetchProjects();
  };
  
  if (!isLoggedIn) {
    return (
      <div className="bg-black min-h-screen py-20">
        <div className="max-w-md mx-auto px-4">
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-800">
            <div className="flex justify-center mb-6">
              <div className="bg-purple-900/30 p-3 rounded-full">
                <Lock className="w-8 h-8 text-purple-400" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-white text-center mb-6">Admin Login</h1>
            
            {loginError && (
              <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded mb-4">
                {loginError}
              </div>
            )}
            
            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label className="block text-gray-400 text-sm mb-2" htmlFor="email">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  required
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-gray-400 text-sm mb-2" htmlFor="password">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  required
                />
              </div>
              
              <button
                type="submit"
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors"
              >
                Login
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-black min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <div className="flex items-center gap-4">
            <button
              onClick={() => {
                setEditingProject(null);
                setShowForm(true);
              }}
              className="inline-flex items-center bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Project
            </button>
            <button
              onClick={handleLogout}
              className="inline-flex items-center bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </button>
          </div>
        </div>
        
        {error && (
          <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}
        
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : (
          <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-800">
                <thead className="bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      URL
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Features
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {projects.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-400">
                        No projects found. Add your first project!
                      </td>
                    </tr>
                  ) : (
                    projects.map((project) => (
                      <tr key={project.id} className="hover:bg-gray-800/50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {project.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                          {project.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                          <a 
                            href={project.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-purple-400 hover:text-purple-300"
                          >
                            {project.url.substring(0, 30)}
                            {project.url.length > 30 ? '...' : ''}
                            <ExternalLink className="w-3 h-3 ml-1" />
                          </a>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                          {project.features ? project.features.length : 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEdit(project)}
                            className="text-indigo-400 hover:text-indigo-300 mr-3"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => project.id && handleDelete(project.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
      
      {showForm && (
        <ProjectForm
          project={editingProject}
          onClose={handleFormClose}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
