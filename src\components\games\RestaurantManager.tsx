import React, { useEffect, useRef } from 'react';

interface Order {
  id: number;
  items: string[];
  totalPrice: number;
  prepTime: number;
  currentPrepTime: number;
  completed: boolean;
}

interface Customer {
  id: number;
  x: number;
  y: number;
  order: Order;
  waitTime: number;
  patience: number;
  satisfied: boolean;
  tableNumber: number;
}

interface GameState {
  customers: Customer[];
  completedOrders: Order[];
  revenue: number;
  reputation: number;
  gameTime: number;
  gameOver: boolean;
  selectedTable: number | null;
  kitchenCapacity: number;
  ordersInKitchen: Order[];
}

class RestaurantManagerLogic {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  state: GameState;
  isRunning: boolean = false;
  lastCustomerSpawn: number = 0;
  orderIdCounter: number = 1;
  customerIdCounter: number = 1;
  menuItems: { name: string; price: number; prepTime: number }[] = [
    { name: 'Burger', price: 12, prepTime: 30 },
    { name: 'Pizza', price: 18, prepTime: 45 },
    { name: 'Salad', price: 8, prepTime: 15 },
    { name: 'Pasta', price: 15, prepTime: 35 },
    { name: 'Steak', price: 25, prepTime: 60 }
  ];

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.width = canvas.width;
    this.height = canvas.height;

    this.state = {
      customers: [],
      completedOrders: [],
      revenue: 0,
      reputation: 100,
      gameTime: 120,
      gameOver: false,
      selectedTable: null,
      kitchenCapacity: 3,
      ordersInKitchen: []
    };

    canvas.addEventListener('click', this.handleClick);
    
    // Start game timer
    setInterval(() => {
      if (this.isRunning && !this.state.gameOver) {
        this.state.gameTime--;
        if (this.state.gameTime <= 0) {
          this.state.gameOver = true;
        }
      }
    }, 1000);
  }

  handleClick = (e: MouseEvent) => {
    if (!this.isRunning || this.state.gameOver) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    // Check table clicks
    for (let i = 0; i < 6; i++) {
      const tableX = 50 + (i % 3) * 150;
      const tableY = 100 + Math.floor(i / 3) * 120;
      
      if (x > tableX - 30 && x < tableX + 30 && y > tableY - 30 && y < tableY + 30) {
        this.handleTableClick(i + 1);
        return;
      }
    }

    // Check kitchen area
    if (x > this.width - 150 && y < 150) {
      this.processKitchenClick();
    }
  };

  handleTableClick(tableNumber: number) {
    const customer = this.state.customers.find(c => c.tableNumber === tableNumber);
    
    if (customer && !customer.order.completed) {
      if (this.state.ordersInKitchen.length < this.state.kitchenCapacity) {
        this.state.ordersInKitchen.push(customer.order);
        customer.order.currentPrepTime = 0;
      }
    }
  }

  processKitchenClick() {
    // Complete the first ready order
    const readyOrder = this.state.ordersInKitchen.find(order => 
      order.currentPrepTime >= order.prepTime
    );
    
    if (readyOrder) {
      readyOrder.completed = true;
      this.state.ordersInKitchen = this.state.ordersInKitchen.filter(o => o.id !== readyOrder.id);
      this.state.completedOrders.push(readyOrder);
      
      const customer = this.state.customers.find(c => c.order.id === readyOrder.id);
      if (customer) {
        customer.satisfied = true;
        this.state.revenue += readyOrder.totalPrice;
        this.state.reputation += customer.patience > 50 ? 2 : 1;
      }
    }
  }

  spawnCustomer() {
    if (this.state.customers.length < 6) {
      // Find available table
      const occupiedTables = this.state.customers.map(c => c.tableNumber);
      const availableTables = [1, 2, 3, 4, 5, 6].filter(t => !occupiedTables.includes(t));
      
      if (availableTables.length > 0) {
        const tableNumber = availableTables[Math.floor(Math.random() * availableTables.length)];
        const tableX = 50 + ((tableNumber - 1) % 3) * 150;
        const tableY = 100 + Math.floor((tableNumber - 1) / 3) * 120;
        
        // Generate random order
        const numItems = Math.floor(Math.random() * 3) + 1;
        const orderItems: string[] = [];
        let totalPrice = 0;
        let totalPrepTime = 0;
        
        for (let i = 0; i < numItems; i++) {
          const item = this.menuItems[Math.floor(Math.random() * this.menuItems.length)];
          orderItems.push(item.name);
          totalPrice += item.price;
          totalPrepTime = Math.max(totalPrepTime, item.prepTime);
        }

        const order: Order = {
          id: this.orderIdCounter++,
          items: orderItems,
          totalPrice,
          prepTime: totalPrepTime,
          currentPrepTime: 0,
          completed: false
        };

        const customer: Customer = {
          id: this.customerIdCounter++,
          x: tableX,
          y: tableY,
          order,
          waitTime: 0,
          patience: 100,
          satisfied: false,
          tableNumber
        };
        
        this.state.customers.push(customer);
      }
    }
  }

  update() {
    if (!this.isRunning || this.state.gameOver) return;

    // Spawn customers
    if (Date.now() - this.lastCustomerSpawn > 5000) {
      this.spawnCustomer();
      this.lastCustomerSpawn = Date.now();
    }

    // Update customers
    this.state.customers.forEach((customer, index) => {
      if (!customer.satisfied) {
        customer.waitTime++;
        customer.patience = Math.max(0, customer.patience - 0.2);
        
        if (customer.patience <= 0) {
          // Customer leaves angry
          this.state.reputation -= 5;
          this.state.customers.splice(index, 1);
        }
      } else {
        // Customer leaves satisfied
        customer.waitTime++;
        if (customer.waitTime > 60) {
          this.state.customers.splice(index, 1);
        }
      }
    });

    // Update kitchen orders
    this.state.ordersInKitchen.forEach(order => {
      if (!order.completed) {
        order.currentPrepTime++;
      }
    });

    this.state.reputation = Math.max(0, Math.min(100, this.state.reputation));
  }

  draw() {
    // Clear canvas
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw tables
    for (let i = 0; i < 6; i++) {
      const tableX = 50 + (i % 3) * 150;
      const tableY = 100 + Math.floor(i / 3) * 120;
      
      this.ctx.fillStyle = '#8b4513';
      this.ctx.fillRect(tableX - 30, tableY - 20, 60, 40);
      
      this.ctx.fillStyle = '#000';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`Table ${i + 1}`, tableX, tableY + 5);
    }

    // Draw customers
    this.state.customers.forEach(customer => {
      // Customer
      this.ctx.fillStyle = customer.satisfied ? '#28a745' : 
                          customer.patience > 50 ? '#ffc107' : '#dc3545';
      this.ctx.beginPath();
      this.ctx.arc(customer.x, customer.y - 40, 15, 0, Math.PI * 2);
      this.ctx.fill();
      
      // Order bubble
      if (!customer.satisfied) {
        this.ctx.fillStyle = 'white';
        this.ctx.fillRect(customer.x - 50, customer.y - 90, 100, 40);
        this.ctx.strokeStyle = '#000';
        this.ctx.strokeRect(customer.x - 50, customer.y - 90, 100, 40);
        
        this.ctx.fillStyle = '#000';
        this.ctx.font = '8px Arial';
        this.ctx.textAlign = 'center';
        customer.order.items.forEach((item, index) => {
          this.ctx.fillText(item, customer.x, customer.y - 80 + index * 10);
        });
        this.ctx.fillText(`$${customer.order.totalPrice}`, customer.x, customer.y - 55);
      }
      
      // Patience bar
      this.ctx.fillStyle = '#e9ecef';
      this.ctx.fillRect(customer.x - 25, customer.y + 20, 50, 6);
      this.ctx.fillStyle = customer.patience > 50 ? '#28a745' : '#dc3545';
      this.ctx.fillRect(customer.x - 25, customer.y + 20, (customer.patience / 100) * 50, 6);
    });

    // Draw kitchen area
    this.ctx.fillStyle = '#6c757d';
    this.ctx.fillRect(this.width - 150, 0, 150, 150);
    this.ctx.fillStyle = '#fff';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('KITCHEN', this.width - 75, 20);
    
    // Draw orders in kitchen
    this.state.ordersInKitchen.forEach((order, index) => {
      const y = 40 + index * 30;
      const progress = Math.min(100, (order.currentPrepTime / order.prepTime) * 100);
      
      this.ctx.fillStyle = '#e9ecef';
      this.ctx.fillRect(this.width - 140, y, 120, 20);
      this.ctx.fillStyle = progress >= 100 ? '#28a745' : '#ffc107';
      this.ctx.fillRect(this.width - 140, y, (progress / 100) * 120, 20);
      
      this.ctx.fillStyle = '#000';
      this.ctx.font = '10px Arial';
      this.ctx.fillText(`Order #${order.id}`, this.width - 80, y + 14);
    });

    // Draw UI
    this.ctx.fillStyle = '#000';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`Revenue: $${this.state.revenue}`, 10, 20);
    this.ctx.fillText(`Reputation: ${Math.round(this.state.reputation)}%`, 10, 40);
    this.ctx.fillText(`Time: ${this.state.gameTime}s`, 10, 60);

    // Instructions
    this.ctx.font = '10px Arial';
    this.ctx.fillText('Click tables to send orders to kitchen', 10, this.height - 40);
    this.ctx.fillText('Click kitchen to serve completed orders', 10, this.height - 25);

    if (this.state.gameOver) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.width, this.height);
      this.ctx.fillStyle = '#fff';
      this.ctx.font = '24px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Restaurant Closed!', this.width/2, this.height/2);
      this.ctx.font = '16px Arial';
      this.ctx.fillText(`Final Revenue: $${this.state.revenue}`, this.width/2, this.height/2 + 30);
      this.ctx.fillText(`Final Reputation: ${Math.round(this.state.reputation)}%`, this.width/2, this.height/2 + 50);
    }
  }

  start() {
    this.isRunning = true;
    this.gameLoop();
  }

  stop() {
    this.isRunning = false;
  }

  gameLoop = () => {
    if (!this.isRunning) return;
    this.update();
    this.draw();
    requestAnimationFrame(this.gameLoop);
  };

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
  }
}

interface RestaurantManagerProps {
  isPreview?: boolean;
}

const RestaurantManager: React.FC<RestaurantManagerProps> = ({ isPreview = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameRef = useRef<RestaurantManagerLogic | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const parent = canvas.parentElement;
    if (!parent) return;

    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    gameRef.current = new RestaurantManagerLogic(canvas);
    
    if (isPreview) {
      gameRef.current.draw();
    } else {
      gameRef.current.start();
      return () => {
        if (gameRef.current) {
          gameRef.current.stop();
        }
      };
    }
  }, [isPreview]);

  return <canvas ref={canvasRef} className="w-full h-full" />;
};

export default RestaurantManager;
