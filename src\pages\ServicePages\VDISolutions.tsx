import React from 'react';
import ServiceLayout from '../ServiceLayout';
import { Monitor, Lock, Cloud, Settings } from 'lucide-react';

const VDISolutions = () => {
  const features = [
    {
      icon: <Monitor className="text-purple-400" size={24} />,
      title: "Virtual Desktops",
      description: "Secure virtual desktop infrastructure setup"
    },
    {
      icon: <Lock className="text-purple-400" size={24} />,
      title: "Secure Access",
      description: "Secure remote access to resources"
    },
    {
      icon: <Cloud className="text-purple-400" size={24} />,
      title: "Cloud Integration",
      description: "Cloud-based VDI solutions"
    },
    {
      icon: <Settings className="text-purple-400" size={24} />,
      title: "Resource Management",
      description: "Efficient resource allocation and management"
    }
  ];

  const pricing = [
    {
      name: "Basic",
      price: "Contact Us",
      features: [
        "Basic VDI Setup",
        "Essential Security",
        "Standard Support",
        "Basic Monitoring",
        "Up to 10 Users"
      ]
    },
    {
      name: "Professional",
      price: "Contact Us",
      recommended: true,
      features: [
        "Advanced VDI Solution",
        "Enhanced Security",
        "24/7 Support",
        "Performance Monitoring",
        "Up to 50 Users",
        "Resource Optimization"
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Us",
      features: [
        "Custom VDI Solution",
        "Advanced Security",
        "Dedicated Support",
        "Custom Integration",
        "Unlimited Users",
        "Global Access"
      ]
    }
  ];

  return (
    <ServiceLayout
      title="VDI Solutions"
      description="Virtual Desktop Infrastructure solutions for modern workplaces"
      features={features}
      pricing={pricing}
      technologies={['VMware Horizon', 'Citrix', 'Microsoft Azure Virtual Desktop', 'AWS WorkSpaces']}
      codeExample={`// VDI Resource Management System
class VDIManager {
  constructor() {
    this.sessions = new Map();
    this.resources = new ResourcePool();
    this.monitoring = new PerformanceMonitor();
  }

  async provisionDesktop(user) {
    try {
      // Check resource availability
      const resources = await this.resources.allocate({
        cpu: '2 cores',
        memory: '8GB',
        storage: '100GB'
      });

      // Create virtual desktop
      const desktop = await this.createVirtualDesktop({
        userId: user.id,
        template: user.template,
        resources: resources
      });

      // Configure security
      await this.configureSecurity(desktop, {
        encryption: true,
        mfa: true,
        networkPolicies: ['restricted']
      });

      // Start monitoring
      this.monitoring.startSession({
        desktopId: desktop.id,
        userId: user.id,
        metrics: ['cpu', 'memory', 'latency']
      });

      return {
        desktopId: desktop.id,
        connectionDetails: desktop.connectionInfo,
        resources: desktop.allocatedResources
      };
    } catch (error) {
      await this.handleProvisioningError(error);
      throw error;
    }
  }

  async optimizeResources() {
    const metrics = await this.monitoring.getMetrics();
    const recommendations = this.analyzeUsage(metrics);
    
    return {
      currentUsage: metrics,
      recommendations: recommendations,
      potentialSavings: this.calculateSavings(recommendations)
    };
  }
}`}
    />
  );
};

export default VDISolutions;
