import express from 'express';
import nodemailer from 'nodemailer';
import cors from 'cors';

const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Create transporter
const transporter = nodemailer.createTransport({
  host: 'mail.spirelab.net',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'Alpha@123#$'
  },
  tls: {
    rejectUnauthorized: false
  },
  debug: true, // Enable debug logging
  logger: true  // Log to console
});

// Verify connection configuration
transporter.verify(function(error, success) {
  if (error) {
    console.log('Server is not ready to take our messages:', error);
  } else {
    console.log('Server is ready to take our messages');
  }
});

app.post('/api/send-email', async (req, res) => {
  console.log('Received request:', req.body);
  
  try {
    const { firstName, lastName, email, phone, service, description } = req.body;

    const mailOptions = {
      from: {
        name: 'Spirelab Contact Form',
        address: '<EMAIL>'
      },
      to: '<EMAIL>',
      replyTo: email,
      subject: `New Contact Form Submission - ${service}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${firstName} ${lastName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone}</p>
        <p><strong>Service:</strong> ${service}</p>
        <p><strong>Description:</strong></p>
        <p>${description.replace(/\n/g, '<br>')}</p>
      `
    };

    console.log('Sending email with options:', mailOptions);

    const info = await transporter.sendMail(mailOptions);
    console.log('Message sent: %s', info.messageId);
    
    res.json({ success: true, messageId: info.messageId });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message,
      details: error.response || 'No additional details available'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error',
    details: err.message
  });
});

app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
