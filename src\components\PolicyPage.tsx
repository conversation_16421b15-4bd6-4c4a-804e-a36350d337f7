import { FC, ReactNode } from 'react';

interface PolicyPageProps {
  title: string;
  children: ReactNode;
}

const PolicyPage: FC<PolicyPageProps> = ({ title, children }) => {
  return (
    <div className="py-16" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 
          className="text-3xl font-bold mb-10 font-mono text-center"
          style={{ color: 'var(--color-text-primary)' }}
        >
          {title}
        </h1>
        <div 
          className="prose prose-lg prose-invert max-w-none font-mono leading-relaxed"
          style={{ 
            color: 'var(--color-text-secondary)',
            '--tw-prose-headings': 'var(--color-text-primary)',
            '--tw-prose-links': 'var(--color-primary)',
            lineHeight: '1.8',
          } as React.CSSProperties}
        >
          <div className="space-y-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PolicyPage;
